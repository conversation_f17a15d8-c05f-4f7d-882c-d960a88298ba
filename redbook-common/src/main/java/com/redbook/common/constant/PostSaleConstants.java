package com.redbook.common.constant;

public class PostSaleConstants {
    //用户类型 0:小程序用户 1:后台运管用户
    public static final int USER_TYPE_APPLET = 0;
    public static final int USER_TYPE_ADMIN = 1;
    //寄修快递方式 1:上门取件 2:自主寄回',
    public static final int SEND_EXPRESS_TYPE_DOOR = 1;
    public static final int SEND_EXPRESS_TYPE_AUTO = 2;

    //寄修单付款方式，1:寄付 2:到付'
    public static final int SEND_EXPRESS_PAY_TYPE_SEND = 1;
    public static final int SEND_EXPRESS_PAY_TYPE_REACH = 2;

    //状态 0:待签收 1:已签收,待复判 2:已复判,待付款 3:已付款,待维修 4:已维修,待老化 5:已老化,待品控 6:已品控,待回寄 7:已回寄,待签收 8：已签收，待评价  9：已完结  21:处理报价异议
    public static final int REPAIR_STATUS_DEFAULT = 0;
    public static final int REPAIR_STATUS_FINISH_SIGN_TO_BE_JUDGE = 1;
    public static final int REPAIR_STATUS_FINISH_JUDGE_TO_BE_PAY = 2;
    public static final int REPAIR_STATUS_HANDLE_PRICE_DISSENT = 21;
    public static final int REPAIR_STATUS_FINISH_PAY_TO_BE_REPAIR = 3;
    public static final int REPAIR_STATUS_FINISH_REPAIR_TO_BE_LAOHUA = 4;
    public static final int REPAIR_STATUS_FINISH_LAOHUA_TO_BE_QC = 5;
    public static final int REPAIR_STATUS_FINISH_QC_TO_BE_RETURN = 6;
    public static final int REPAIR_STATUS_FINISH_RETURN_TO_BE_SIGN = 7;
    public static final int REPAIR_STATUS_FINISH_RETURN_SIGN_TO_BE_EVALUATE = 8;
    public static final int REPAIR_STATUS_END = 9;


    //报废状态 1：已复判，待报废，2：已报废，待付款，3：待回寄  4：报废的就地报废  7:已回寄
    public static final int SCRAP_STATUS_JUDGE_TO_BE_SCRAP = 1;
    public static final int SCRAP_STATUS_SCRAP_TO_BE_PAY = 2;
    public static final int SCRAP_STATUS_FINISH_PAY_TO_BE_RETURN = 3;
    public static final int SCRAP_STATUS_FINISH_LOCAL_SCRAP = 4;
    public static final int SCRAP_STATUS_SCRAP_FINISH_RETURN = 7;
    //放弃维修状态 2：已放弃，待付款，3：待回寄，4：报废的就地报废  7:已回寄
    public static final int ABANDON_STATUS_TO_BE_PAY = 2;
    public static final int ABANDON_STATUS_FINISH_PAY_TO_BE_RETURN = 3;
    public static final int ABANDON_STATUS_FINISH_LOCAL_SCRAP = 4;
    public static final int ABANDON_STATUS_FINISH_RETURN = 7;
    //维修方案,  1:维修 2:换新 3:报废 4:转寄
    public static final int REPAIR_PLAN_CHANGE_NEW = 2;
    public static final int REPAIR_PLAN_SCRAP = 3;

    //报废/放弃维修方式 1:不良件寄回 2:就地报废
    public static final int ABNORMAL_HANDLER_TYPE_RETURN = 1;
    public static final int ABNORMAL_HANDLER_TYPE_LOCAL = 2;


    //备注类型 1:报废 2:报价异议 3:放弃维修
    public static final int REMARK_TYPE_SCRAP = 1;
    public static final int REMARK_TYPE_PRICEDISSENT = 2;
    public static final int REMARK_TYPE_ABANDON = 3;

    //    //操作方式 0:单件操作 1:整单操作
//    public static final int ACTION_METHOD_SINGLE =0;
//    public static final int ACTION_METHOD_TOGETHER =1;

    //备件操作方向 1:采购入库 2:领用出库 3:盘点出库 4:调拨出库
    public static final int SPARE_PART_PURCHASE_IN = 1;
    public static final int SPARE_PART_USE_OUT = 2;
    public static final int SPARE_PART_STOCKTAKING_OUT = 2;
    public static final int SPARE_PART_TRANSFER_OUT = 4;

    //配件订单状态 0:待发货 1:已发货 2:已取消 3:已完成
    public static final Integer PJ_ORDER_STATUS_WAIT_SEND = 0;
    public static final Integer PJ_ORDER_STATUS_ALREADY_SEND = 1;
    public static final Integer PJ_ORDER_STATUS_CANCEL = 2;
    public static final Integer PJ_ORDER_STATUS_DONE = 3;
    //配件订单退款状态：0:无退款 1:申请中 2:审核中 3:退款中 4:已退款
    public static final Integer PJ_RFD_ORDER_STATUS_NONE = 0;
    public static final Integer PJ_RFD_ORDER_STATUS_APPLYING = 1;
    public static final Integer PJ_RFD_ORDER_STATUS_HANDLING = 2;
    public static final Integer PJ_RFD_ORDER_STATUS_REFUNDING = 3;
    public static final Integer PJ_RFD_ORDER_STATUS_DONE = 4;

    //报价异议状态：0：默认无异议 1：用户提出异议 2：确认有异议，重新报价 3：确认无异议
    public static final Integer PRICE_DISSENT_STATUS_DEFAULT = 0;
    public static final Integer PRICE_DISSENT_STATUS_USER_RAISE_DISSENT = 1;
    public static final Integer PRICE_DISSENT_STATUS_ADMIN_RENEW_PRICE = 2;
    public static final Integer PRICE_DISSENT_STATUS_ADMIN_CONFIRM_NO_DISSENT = 3;


    public static final String SF_RESULT_SUCCESS = "A1000";
    public static final String SF_RESULT_DATA_SUCCESS = "S0000";
    //短信提醒付款模块id
    public static final String SMS_TEMPLATEID_REMIND_PAY = "191791";//报价提醒付款
    public static final String SMS_TEMPLATEID_LATE_PAY = "191790";//逾期付款
}
