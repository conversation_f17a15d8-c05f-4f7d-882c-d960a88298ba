package com.redbook.common.utils;


import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static java.util.Calendar.DAY_OF_WEEK;

/**
 * 时间相关的工具类
 */
@Slf4j
public class TimeUtils extends DateUtils {


    /**
     * 日期格式 ： yyyy-MM-dd
     */
    public static final String FORMAT_STANDARD_DATE = "yyyy-MM-dd";

    /**
     * 日期格式 yyyyMMdd
     */
    public static final String FORMAT_DATE_NO_SEPARATOR = "yyyyMMdd";


    /**
     * 时间格式 yyyy-MM-dd HH:mm:ss
     */
    public static final String FORMAT_STANDARD_DATETIME = "yyyy-MM-dd HH:mm:ss";


    /**
     * 时间格式 yyyyMMddHHmmss
     */
    public static final String FORMAT_DATETIME_NO_SEPARATOR = "yyyyMMddHHmmss";


    /**
     * 时间格式 yyyy-MM-dd HH:mm:ss.SSS
     */
    public static final String FORMAT_DATETIME_INCLUDE_MILLISECOND = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 时间格式 HH:mm:ss
     */
    public static final String FORMAT_STANDARD_TIME = "HH:mm:ss";

    /**
     * 时间格式 HHmmss
     */
    public static final String FORMAT_TIME_NO_SEPARATOR = "HHmmss";

    /**
     * 时间格式 HH:mm
     */
    public static final String FORMAT_HOUR_MINUTE = "HH:mm";

    /**
     * 时间格式 mm:ss
     */
    public static final String FORMAT_MINUTE_SECOND = "mm:ss";

    /**
     * 日期格式 yyyy-MM
     */
    public static final String FORMAT_YEAR_MONTH = "yyyy-MM";

    /**
     * 日期格式 yyyyMM
     */
    private static final String FORMAT_YEAR_MONTH_NO_SEPARATOR = "yyyyMM";

    /**
     * 日期格式 MM-dd
     */
    private static final String FORMAT_MONTH_DAY = "MM-dd";

    /**
     * 日期格式 MM
     */
    private static final String FORMAT_DAY = "MM";


    private static final Map<String, DateTimeFormatter> dateTimeFormatterMap = new ConcurrentHashMap<>(12);


    private TimeUtils() {

    }

    /**
     * 获得服务器当前时间，以格式为：yyyy-MM-dd HH:mm:ss的日期字符串形式返回
     */
    public static String getStringOfCurrentDate() {
        return parseDate(new Date(), TimeUtils.FORMAT_STANDARD_DATETIME);
    }

    /**
     * 获得服务器当前时间，以格式为：{format} 的日期字符串形式返回
     */
    public static String getStringOfCurrentDate(String format) {

        return parseDate(new Date(), format);
    }

    /**
     * 获得服务器当前日期及时间
     */
    public static Date getCurrentDate() {
        return new Date();
    }

    /**
     * 获取服务器当前的时间戳
     */
    public static Timestamp getCurrentTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    /**
     * Date转换为String
     *
     * @param date   输入时间，为空返回null
     * @param format 指定Date的格式，为空默认为 yyyy-MM-dd HH:mm:ss
     */
    public static String parseDate(Date date, String format) {
        LocalDateTime localDateTime = date2LocalDateTime(date);
        return parseLocalDateTime(localDateTime, format);
    }


    /**
     * LocalDateTime转换为String
     *
     * @param localDateTime 输入时间，为空返回null
     * @param format        指定Date的格式，为空默认为 yyyy-MM-dd HH:mm:ss
     */
    public static String parseLocalDateTime(LocalDateTime localDateTime, String format) {
        if (localDateTime == null) {
            return null;
        }

        if (format == null || "".equals(format.trim())) {
            format = FORMAT_STANDARD_DATETIME;
        }

        return localDateTime.format(getDateTimeFormatter(format));
    }


    /**
     * 将String 转换为 LocalDate
     *
     * @param str    待转换的字符串,为空返回null
     * @param format 指定str的格式，为空返回null
     */
    public static LocalDate string2LocalDate(String str, String format) {

        if (str == null || "".equals(str.trim())) {
            return null;
        }

        if (format == null || "".equals(format.trim())) {
            return null;
        }

        return LocalDate.parse(str, getDateTimeFormatter(format));
    }

    /**
     * String 转 Date
     *
     * @param str    String类型的日期，为空返回 null
     * @param format 指定str的格式，不能为空
     */
    public static Date string2Date(String str, String format) {
        if (str == null || "".equals(str.trim())) {
            return null;
        }

        try {
            return new Date(
                    new SimpleDateFormat(format, Locale.CHINESE).parse(str).getTime());
        } catch (ParseException e) {
            return null;
        }

    }

    /**
     * String 转 Timestamp
     *
     * @param str    String类型的日期，为空返回 null
     * @param format 指定str的格式，不能为空
     */
    public static Timestamp string2Timestamp(String str, String format) {
        if (str == null || "".equals(str.trim())) {
            return null;
        }

        try {
            return new Timestamp(
                    new SimpleDateFormat(format, Locale.CHINESE).parse(str).getTime());
        } catch (ParseException e) {
            return null;
        }

    }

    /**
     * LocalDateTime转换为Date
     *
     * @param localDateTime 输入时间，为空返回null
     */
    public static Date localDateTime2Date(LocalDateTime localDateTime) {

        if (localDateTime == null) {
            return null;
        }


        ZoneId zoneId = ZoneId.systemDefault();

        ZonedDateTime zdt = localDateTime.atZone(zoneId);

        return Date.from(zdt.toInstant());
    }

    /**
     * Date转换为LocalDateTime
     *
     * @param date 输入时间,为空返回null
     */
    public static LocalDateTime date2LocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime;
    }

    public static LocalDate date2LocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDate();
    }


    /**
     * 获取某天的开始时间 yyyy-MM-dd 00:00:00
     *
     * @param date 输入时间，为空返回null
     */
    public static Date getStartOfDay(Date date) {

        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime = date2LocalDateTime(date);

        return localDateTime2Date(getStartOfDay(localDateTime));
    }

    /**
     * 获取某天的开始时间 yyyy-MM-dd 00:00:00
     *
     * @param localDateTime 输入时间，为空返回null
     */
    public static LocalDateTime getStartOfDay(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.toLocalDate().atStartOfDay();
    }


    /**
     * 获取某天的结束时间 yyyy-MM-dd 23:59:59
     *
     * @param date 输入时间，为空返回null
     */
    public static Date getEndOfDay(Date date) {

        LocalDateTime localDateTime = date2LocalDateTime(date);

        return localDateTime2Date(getEndOfDay(localDateTime));
    }

    /**
     * 获取某天的结束时间 yyyy-MM-dd 23:59:59
     *
     * @param localDateTime 输入时间，为空返回null
     */
    public static LocalDateTime getEndOfDay(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }

        return localDateTime.withHour(23).withMinute(59).withSecond(59);
    }

    /**
     * 获取本周的第一天
     */
    public static String getStartOfWeekDay(Date date, String formate) {
        if (null == date) {
            return null;
        }
        LocalDateTime localDateTime = date2LocalDateTime(date);
        return parseLocalDateTime(getStartOfWeekDay(localDateTime), formate);

    }

    public static LocalDateTime getStartOfWeekDay(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.with(DayOfWeek.MONDAY);
    }

    /**
     * 获取本周的最后一天
     *
     * @param date
     * @param formate
     * @return
     */
    public static String getEndOfWeekDay(Date date, String formate) {
        if (null == date) {
            return null;
        }
        LocalDateTime localDateTime = date2LocalDateTime(date);
        return parseLocalDateTime(getEndOfWeekDay(localDateTime), formate);
    }

    public static LocalDateTime getEndOfWeekDay(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.with(DayOfWeek.SUNDAY);
    }

    /**
     * 获取前一天
     *
     * @param date
     * @return
     */
    public static String getBeforeDay(Date date, String formate) {
        if (null == date) {
            return null;
        }
        LocalDateTime localDateTime = date2LocalDateTime(date);
        return parseLocalDateTime(getBeforeDay(localDateTime), formate);
    }

    public static LocalDateTime getBeforeDay(LocalDateTime localDateTime) {
        return localDateTime.minusDays(1);
    }

    /**
     * 获取后一天
     *
     * @param date
     * @return
     */
    public static String getAfterDay(Date date, String formate) {
        if (null == date) {
            return null;
        }
        LocalDateTime localDateTime = date2LocalDateTime(date);
        return parseLocalDateTime(getAfterDay(localDateTime), formate);
    }

    public static LocalDateTime getAfterDay(LocalDateTime localDateTime) {
        return localDateTime.plusDays(1);
    }

    public static String getStartOfMonthDay(Date date, String formate) {
        LocalDateTime localDateTime = date2LocalDateTime(date);

        return parseLocalDateTime(getStartOfMonthDay(localDateTime), formate);

    }

    /**
     * 获取月初开始时间 yyyy-MM-01 00:00:00
     *
     * @param date 输入时间，为空返回null
     */
    public static Date getStartOfMonthDay(Date date) {

        LocalDateTime localDateTime = date2LocalDateTime(date);

        return localDateTime2Date(getStartOfMonthDay(localDateTime));
    }

    /**
     * 获取月初开始时间 yyyy-MM-01 00:00:00
     *
     * @param localDateTime 输入时间，为空返回null
     */
    public static LocalDateTime getStartOfMonthDay(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return LocalDate.of(localDateTime.getYear(), localDateTime.getMonth(), 1).atStartOfDay();
    }


    /**
     * 获取月末时间
     *
     * @param date 输入时间，为空返回null
     */
    public static Date getEndOfMonthDay(Date date) {

        LocalDateTime localTimeDate = date2LocalDateTime(date);

        return localDateTime2Date(getEndOfMonthDay(localTimeDate));
    }


    /**
     * 获取月末时间
     *
     * @param date 输入时间，为空返回null
     */
    public static String getEndOfMonthDay(Date date, String formate) {

        LocalDateTime localTimeDate = date2LocalDateTime(date);

        return parseLocalDateTime(getEndOfMonthDay(localTimeDate), formate);
    }


    /**
     * 获取月末时间
     *
     * @param localDateTime 输入时间，为空返回null
     */
    public static LocalDateTime getEndOfMonthDay(LocalDateTime localDateTime) {

        if (localDateTime == null) {
            return null;
        }
        //这里获取一个月的最大天数，进行了平闰年的判断
        int monthLength = getMonthLength(localDateTime);

        return localDateTime.withDayOfMonth(monthLength).withHour(23).withMinute(59).withSecond(59);
    }


    /**
     * 获取年初开始时间 yyyy-01-01 00:00:00
     *
     * @param date 输入时间，为空返回null
     */
    public static Date getStartOfYearDay(Date date) {

        LocalDateTime localDateTime = date2LocalDateTime(date);

        return localDateTime2Date(getStartOfYearDay(localDateTime));
    }

    /**
     * 获取季度开始时间 yyyy-MM-01 00:00:00
     *
     * @param year 输入时间，为空返回null
     */
    public static LocalDateTime getStartOfSeasonDay(int year, int season) {

        int month;
        if (1 == season) {
            month = 1;
        } else if (2 == season) {
            month = 4;
        } else if (3 == season) {
            month = 7;
        } else {
            month = 10;
        }
        String date = year + "-" + month;
        LocalDateTime localDateTime = date2LocalDateTime(string2Date(date, TimeUtils.FORMAT_YEAR_MONTH));
        return LocalDate.of(localDateTime.getYear(), localDateTime.getMonth(), 1).atStartOfDay();
    }

    /**
     * 获取上季度开始时间 yyyy-MM-01 00:00:00
     */
    public static String getStartOfSeasonDay(Date date, String formate) {

        int lastMonth;
        int lastYear;

        // 获取当前月
        int month = getMonth(date);
        int year = getYear(date);

        if (month > 9) {
            lastMonth = 7;
            lastYear = year;
        } else if (month > 6) {
            lastMonth = 4;
            lastYear = year;
        } else if (month > 3) {
            lastMonth = 1;
            lastYear = year;
        } else {
            lastMonth = 10;
            lastYear = year - 1;
        }


        return getStartOfMonthDay(TimeUtils.string2Date(lastYear + "-" + lastMonth, FORMAT_YEAR_MONTH), formate);
    }


    /**
     * 获取季末时间
     *
     * @param year 输入时间，为空返回null
     */
    public static LocalDateTime getEndOfSeasonDay(int year, int season) {
        int month;
        if (1 == season) {
            month = 3;
        } else if (2 == season) {
            month = 6;
        } else if (3 == season) {
            month = 9;
        } else {
            month = 12;
        }
        String date = year + "-" + month;

        LocalDateTime localDateTime = date2LocalDateTime(string2Date(date, TimeUtils.FORMAT_YEAR_MONTH));

        //这里获取一个月的最大天数，进行了平闰年的判断
        int monthLength = getMonthLength(localDateTime);

        return localDateTime.withDayOfMonth(monthLength).withHour(23).withMinute(59).withSecond(59);
    }

    /**
     * 获取上季度开始时间 yyyy-MM-01 00:00:00
     */
    public static String getEndOfSeasonDay(Date date, String formate) {

        int lastMonth;
        int lastYear;

        // 获取当前月
        int month = getMonth(date);
        int year = getYear(date);

        if (month > 9) {
            lastMonth = 9;
            lastYear = year;
        } else if (month > 6) {
            lastMonth = 6;
            lastYear = year;
        } else if (month > 3) {
            lastMonth = 3;
            lastYear = year;
        } else {
            lastMonth = 12;
            lastYear = year - 1;
        }


        return getEndOfMonthDay(TimeUtils.string2Date(lastYear + "-" + lastMonth, FORMAT_YEAR_MONTH), formate);
    }

    /**
     * 获取年初开始时间 yyyy-01-01 00:00:00
     *
     * @param localDateTime 输入时间，为空返回null
     */
    public static LocalDateTime getStartOfYearDay(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return LocalDate.of(localDateTime.getYear(), 1, 1).atStartOfDay();
    }


    /**
     * 获取年末时间
     *
     * @param date 输入时间，为空返回null
     */
    public static Date getEndOfYearDay(Date date) {

        LocalDateTime localTimeDate = date2LocalDateTime(date);

        return localDateTime2Date(getEndOfYearDay(localTimeDate));
    }

    /**
     * 获取年末时间
     *
     * @param localDateTime 输入时间，为空返回null
     */
    public static LocalDateTime getEndOfYearDay(LocalDateTime localDateTime) {

        if (localDateTime == null) {
            return null;
        }

        return LocalDateTime.of(localDateTime.getYear(), 12, 31, 23, 59, 59);
    }

    /**
     * 计算时间间隔
     *
     * @param date1        输入时间1，不能为空
     * @param date2        输入时间2，不能为空
     * @param timeInterval 时间间隔标志，不能为空
     * @return 时间间隔
     */
    public static long betweenTime(
            @NonNull Date date1, @NonNull Date date2, @NonNull TimeIntervalEnum timeInterval) {

        LocalDateTime localDateTime1 = date2LocalDateTime(date1);
        LocalDateTime localDateTime2 = date2LocalDateTime(date2);

        return betweenTime(localDateTime1, localDateTime2, timeInterval);
    }

    /**
     * 计算时间间隔
     *
     * @param localDateTime1 输入时间1，不能为空
     * @param localDateTime2 输入时间2，不能为空
     * @param timeInterval   时间间隔标志，不能为空
     * @return 时间间隔
     */
    public static long betweenTime(
            @NonNull LocalDateTime localDateTime1, @NonNull LocalDateTime localDateTime2,
            @NonNull TimeIntervalEnum timeInterval) {

        LocalDateTime start;
        LocalDateTime end;
        if (localDateTime1.isAfter(localDateTime2)) {
            start = localDateTime2;
            end = localDateTime1;
        } else {
            start = localDateTime1;
            end = localDateTime2;
        }

        long between = 0;
        switch (timeInterval) {
            case CAL_NANOS:
                return ChronoUnit.NANOS.between(start, end);
            case CAL_MICROS:
                return ChronoUnit.MICROS.between(start, end);
            case CAL_MILLIS:
                return ChronoUnit.MILLIS.between(start, end);
            case CAL_MINUTES:
                return ChronoUnit.MINUTES.between(start, end);
            case CAL_HOURS:
                return ChronoUnit.HOURS.between(start, end);
            case CAL_HALF_DAYS:
                return ChronoUnit.HALF_DAYS.between(start, end);
            case CAL_DAYS:
                return ChronoUnit.DAYS.between(start, end);
            case CAL_MONTHS:
                return ChronoUnit.MONTHS.between(start, end);
            case CAL_YEARS:
                return ChronoUnit.YEARS.between(start, end);
            case CAL_DECADES:
                return ChronoUnit.DECADES.between(start, end);
            case CAL_CENTURIES:
                return ChronoUnit.CENTURIES.between(start, end);
            case CAL_MILLENNIA:
                return ChronoUnit.MILLENNIA.between(start, end);
            default:
                throw new IllegalArgumentException("The timeInterval is invalid");
        }
    }

    /**
     * 获取指定日期的"年"
     */
    public static int getYear(@NonNull Date date) {
        return getByCalendar(date, Calendar.YEAR);
    }


    /**
     * 获取指定日期的"月"，返回 1-12
     */
    public static int getMonth(@NonNull Date date) {
        // Month值为 0-11   ，因此需要加1
        return getByCalendar(date, Calendar.MONTH) + 1;
    }

    /**
     * 获取指定日期的"年"
     */
    public static int getSeason(@NonNull Date date) {
        int month = getMonth(date);
        if (month <= 3) {
            return 1;
        } else if (month <= 6) {
            return 2;
        } else if (month <= 9) {
            return 3;
        } else {
            return 4;
        }

    }

    /**
     * 获取指定日期的"日",返回 1-31
     */
    public static int getDay(@NonNull Date date) {
        return getByCalendar(date, Calendar.DAY_OF_MONTH);
    }


    /**
     * 获取指定日期的"周",返回 1-7
     *
     * @param date
     */
    public static int getWeek(@NonNull Date date) {
        int week = getByCalendar(date, Calendar.DAY_OF_WEEK);
        return week == 1 ? 7 : week - 1;

    }

    public static int getMonthWeek(@NonNull Date date) {
        int week = getByCalendar(date, Calendar.WEEK_OF_MONTH);
        return week;
    }


    public static long getTimeMillis(LocalDateTime dateTime) {
        return dateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }


    /**
     * 返回指定月的天数
     *
     * @param year  指定年，如2019
     * @param month 指定月，1-12
     * @return 例如：year = 2019 , month = 9 ,return 30
     */
    public static int getMonthLength(int year, int month) {
        return getMonthLength(LocalDate.of(year, month, 1));
    }


    /**
     * 返回指定月的天数
     *
     * @param localDate 指定日期，不能为空
     */
    public static int getMonthLength(@NonNull LocalDate localDate) {

        return localDate.getMonth().length(localDate.isLeapYear());
    }


    /**
     * 返回指定月的天数
     *
     * @param localDateTime 指定时间，不能为空
     */
    public static int getMonthLength(@NonNull LocalDateTime localDateTime) {
        return getMonthLength(localDateTime.toLocalDate());
    }


    /**
     * 返回指定月的天数
     *
     * @param date 指定时间，不能为空
     */
    public static int getMonthLength(@NonNull Date date) {
        return getMonthLength(date2LocalDateTime(date));
    }


    /**
     * 比较两个日期
     * </p>
     * 两个日期都将转换为 yyyyMMdd格式，然后通过比较字符串确定日期大小
     *
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return date1 > date2  return 1
     * date1 < date2  return -1
     * date1 == date2 retrn 0
     * @
     */
    public static int compareTwoDates(Date date1, Date date2) {

        if (date1 == null) {
            return date2 == null ? 0 : -1;
        } else {
            if (date2 == null) {
                return 1;
            } else {
                String strDate1 = parseDate(date1, FORMAT_DATE_NO_SEPARATOR);
                String strDate2 = parseDate(date2, FORMAT_DATE_NO_SEPARATOR);
                int intRet = strDate1.compareTo(strDate2);
                if (intRet > 0) {
                    return 1;
                } else if (intRet == 0) {
                    return 0;
                } else {
                    return -1;
                }
            }
        }
    }


    /**
     * 判断是否为闰年
     *
     * @param year 指定年份，如 2019
     */
    public static boolean isLeapYear(int year) {
        return LocalDate.of(year, 1, 1).isLeapYear();
    }


    /**
     * 判断是否为闰年
     *
     * @param date 指定日期不能为空
     */
    public static boolean isLeapYear(@NonNull Date date) {
        return isLeapYear(getYear(date));
    }


    /**
     * 计算taget是从begin开始计算的第几天，begin为第1天
     *
     * @return <p>begin : 2019-05-24 , target : 2019-05-27 , return 4  <p/>
     * <p>begin : 2019-05-27 , target : 2019-05-24 , return 0  <p/>
     */
    public static long whichDay(@NonNull Date begin, @NonNull Date target) {

        if (begin.after(target)) {
            //begin > target ,return 0
            return 0;
        }
        //以begin为第1天，所以需要会+1
        return betweenTime(begin, target, TimeIntervalEnum.CAL_DAYS) + 1;
    }


    /**
     * 获取 DateTimeFormatter
     *
     * @param format 日期、时间格式
     */
    private static DateTimeFormatter getDateTimeFormatter(String format) {

        if (dateTimeFormatterMap.containsKey(format)) {
            return dateTimeFormatterMap.get(format);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        dateTimeFormatterMap.put(format, formatter);

        return formatter;
    }


    /**
     * 根据生日获取年龄
     *
     * @param birthday 生日
     * @return 整数类型的年龄（周岁）
     */
    public static int getAgeByBirthday(@NonNull Date birthday) {
        Calendar cal = Calendar.getInstance();

        if (cal.before(birthday)) {
            throw new IllegalArgumentException(
                    "The birthDay is before Now.It's unbelievable!");
        }

        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH) + 1;
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

        cal.setTime(birthday);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH) + 1;
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

        int age = yearNow - yearBirth;

        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                // monthNow==monthBirth
                if (dayOfMonthNow < dayOfMonthBirth) {
                    age--;
                }
            } else {
                // monthNow>monthBirth
                age--;
            }
        }
        return age;
    }

    /**
     * 根据出生日期获取带小数的年龄（四舍五入）
     *
     * @param birthday 生日
     * @param scale    保留几位小数
     * @return 年龄（周岁）
     */
    public static BigDecimal getAgeByBirthday(@NonNull Date birthday, int scale) {

        Calendar now = Calendar.getInstance();

        if (now.before(birthday)) {
            throw new IllegalArgumentException("The birthday is before Now.It's unbelievable!");
        }
        Calendar birth = Calendar.getInstance();
        birth.setTime(birthday);

        BigDecimal age =
                BigDecimal.valueOf(now.get(Calendar.YEAR)).subtract(BigDecimal.valueOf(birth.get(Calendar.YEAR)));

        int diffDays = now.get(Calendar.DAY_OF_YEAR) - birth.get(Calendar.DAY_OF_YEAR);

        age = age.add(
                BigDecimal.valueOf(diffDays).divide(BigDecimal.valueOf(
                        now.getActualMaximum(Calendar.DAY_OF_YEAR)), scale, RoundingMode.HALF_UP
                )
        );

        return age;
    }


    private static int getByCalendar(@NonNull Date date, int field) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(date.getTime());
        return calendar.get(field);
    }


    public static long getNowTimeInMillis() {
        return LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public static Date getOneMonth() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, -1);
        Date monday = c.getTime();
        return monday;
    }


    //获取本周的开始时间

    // 获取当日所在周的开始时间
    public static Date getBeginDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayofweek = cal.get(DAY_OF_WEEK);
//        if (dayofweek == 1) {
//            dayofweek += 7;
//        }
        cal.add(Calendar.DATE, 1 - dayofweek);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    // 获取当日所在周的结束时间
    public static Date getEndDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getBeginDayOfWeek(date));
        cal.add(Calendar.DAY_OF_WEEK, 6);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }


    public static Timestamp getDayStartTime(Date d) {
        Calendar calendar = Calendar.getInstance();

        if (null != d) calendar.setTime(d);

        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);

        calendar.set(Calendar.MILLISECOND, 0);

        return new Timestamp(calendar.getTimeInMillis());

    }

    //获取某个日期的结束时间

    public static Timestamp getDayEndTime(Date d) {
        Calendar calendar = Calendar.getInstance();

        if (null != d) calendar.setTime(d);

        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);

        calendar.set(Calendar.MILLISECOND, 999);

        return new Timestamp(calendar.getTimeInMillis());

    }

    public static String formatTime(Integer time) {
        if (time == null) {
            return "00:00:00";
        }
        Integer hour = time / 3600;
        time = time % 3600;
        Integer minute = time / 60;
        Integer second = time % 60;
        return (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute) + ":" + (second < 10 ? ("0" + second) : second);
    }
}



