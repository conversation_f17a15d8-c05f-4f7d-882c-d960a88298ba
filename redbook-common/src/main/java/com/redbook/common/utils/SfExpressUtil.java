package com.redbook.common.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/15
 * @description 顺丰工具类
 */
@Component
@Slf4j
public class SfExpressUtil {
    // 下单
    private final String EXP_RECE_CREATE_ORDER = "EXP_RECE_CREATE_ORDER";
    // 查询路由
    private final String EXP_RECE_SEARCH_ROUTES = "EXP_RECE_SEARCH_ROUTES";
    // 打印面单
    private final String COM_RECE_CLOUD_PRINT_WAYBILLS = "COM_RECE_CLOUD_PRINT_WAYBILLS";
    //取消订单
    private final String EXP_RECE_UPDATE_ORDER = "EXP_RECE_UPDATE_ORDER";

    private final String LANGUAGE_ZHCN = "zh-cn";

    @Value("${express.sfHost}")
    private String sfExpressHost;

    @Value("${express.sfCustomerCode}")
    private String sfCustomerCode;

    @Value("${express.sfCheckCode}")
    private String sfCheckCode;

    /**
     * 下单
     *
     * @param msgData
     * @return
     */
    public String createOrder(String msgData) {
        String result = null;
        String timeStamp = String.valueOf(System.currentTimeMillis());
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("serviceCode", EXP_RECE_CREATE_ORDER);
        paramMap.put("timestamp", timeStamp);
        paramMap.put("partnerID", sfCustomerCode);
        paramMap.put("requestID", UUID.randomUUID().toString());
        paramMap.put("msgData", msgData);
        paramMap.put("msgDigest", buildMsgDigest(msgData, timeStamp));
        try {
            log.info("顺丰快递生成订单入参: serviceCode:{}, partnerID:{}, msgData:{},msgDigest:{}", EXP_RECE_CREATE_ORDER, sfCustomerCode, msgData, paramMap.get("msgDigest"));
            // 发送请求
            result = HttpUtil.post(sfExpressHost, paramMap);
        } catch (Exception e) {
            log.error("顺丰快递生成订单异常:", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }
    public String updateOrder(String msgData) {
        String result = null;
        String timeStamp = String.valueOf(System.currentTimeMillis());
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("serviceCode", EXP_RECE_UPDATE_ORDER);
        paramMap.put("timestamp", timeStamp);
        paramMap.put("partnerID", sfCustomerCode);
        paramMap.put("requestID", UUID.randomUUID().toString());
        paramMap.put("msgData", msgData);
        paramMap.put("msgDigest", buildMsgDigest(msgData, timeStamp));
        try {
            log.info("顺丰快递更新订单入参: serviceCode:{}, partnerID:{}, msgData:{},msgDigest:{}", EXP_RECE_UPDATE_ORDER, sfCustomerCode, msgData, paramMap.get("msgDigest"));
            // 发送请求
            result = HttpUtil.post(sfExpressHost, paramMap);
        } catch (Exception e) {
            log.error("顺丰快递生成订单异常:", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }
    /**
     * 查询订单
     *
     * @param trackingType 查询号类别: 1:根据顺丰运单号查询,trackingNumber将被当作顺丰运单号处理，2:根据客户订单号查询,trackingNumber将被当作客户订单号处理
     * @param orderId      查询号：trackingType=1,则此值为顺丰运单号，如果trackingType=2,则此值为客户订单号
     * @param phone
     * @return
     */
    public String queryOrder(String trackingType, String orderId/* ,String phone*/) {
        Map params = new HashMap();
        String timeStamp = String.valueOf(System.currentTimeMillis());
        JSONObject jSONObjectParam = new JSONObject();
        jSONObjectParam.put("trackingType", trackingType);
        jSONObjectParam.put("language", LANGUAGE_ZHCN);
        jSONObjectParam.put("trackingNumber", orderId);
//        jSONObjectParam.put("checkPhoneNo", phone);
        String msgData = JSONObject.toJSONString(jSONObjectParam);

        params.put("serviceCode", EXP_RECE_SEARCH_ROUTES);
        params.put("partnerID", sfCustomerCode);
        params.put("requestID", UUID.randomUUID().toString());
        params.put("timestamp", timeStamp);
        params.put("msgData", msgData);
        params.put("msgDigest", buildMsgDigest(msgData, timeStamp));
        String result = "";
        try {
            log.info("顺丰快递查询订单入参：orderId:{}, serviceCode:{}, partnerID:{}, msgData:{},msgDigest:{}", orderId,  EXP_RECE_CREATE_ORDER, sfCustomerCode, msgData, params.get("msgDigest"));
            result = HttpUtil.post(sfExpressHost, params);
        } catch (Exception e) {
            log.error("顺丰快递查询订单异常：", e);
        }
        return result;
    }
    //同步打印面单
    public String syncPrintWaybills(String msgData) {
        String result = null;
        String timeStamp = String.valueOf(System.currentTimeMillis());
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("serviceCode", COM_RECE_CLOUD_PRINT_WAYBILLS);
        paramMap.put("timestamp", timeStamp);
        paramMap.put("partnerID", sfCustomerCode);
        paramMap.put("requestID", UUID.randomUUID().toString());
        paramMap.put("msgData", msgData);
        paramMap.put("msgDigest", buildMsgDigest(msgData, timeStamp));
        try {
            log.info("顺丰快递同步打印面单入参: serviceCode:{}, partnerID:{}, msgData:{},msgDigest:{}", COM_RECE_CLOUD_PRINT_WAYBILLS, sfCustomerCode, msgData, paramMap.get("msgDigest"));
            // 发送请求
            result = HttpUtil.post(sfExpressHost, paramMap);
        } catch (Exception e) {
            log.error("顺丰快递同步打印面单异常:", e.getMessage());
        }

        return result;
    }
    private String buildMsgDigest(String msgData, String timeStamp) {
        String msgDigest = null;
        //客户校验码    使用顺丰分配的客户校验码
        String checkWord = this.sfCheckCode;
        //将业务报文+时间戳+校验码组合成需加密的字符串(注意顺序)
        String toVerifyText = msgData + timeStamp + checkWord;
        //因业务报文中可能包含加号、空格等特殊字符，需要urlEnCode处理
        try {
            toVerifyText = URLEncoder.encode(toVerifyText, "UTF-8");
            //进行Md5加密
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(toVerifyText.getBytes("UTF-8"));
            byte[] md = md5.digest();
            //通过BASE64生成数字签名
            msgDigest = new String(new BASE64Encoder().encode(md));

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return msgDigest;
    }
}
