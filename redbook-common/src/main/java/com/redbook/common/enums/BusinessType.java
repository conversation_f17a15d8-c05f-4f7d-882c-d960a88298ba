package com.redbook.common.enums;

/**
 * 业务操作类型
 * 
 * <AUTHOR>
 */
public enum BusinessType
{
    /**
     * 其它
     */
    OTHER,

    /**
     * 新增
     */
    INSERT,

    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE,

    /**
     * 取消
     */
    CANCEL,

    /**
     * 授权
     */
    GRANT,

    /**
     * 导出
     */
    EXPORT,

    /**
     * 导入
     */
    IMPORT,

    /**
     * 强退
     */
    FORCE,

    /**
     * 生成代码
     */
    GENCODE,
    
    /**
     * 清空数据
     */
    CLEAN,

    /**
     * 已兑换
     */
    CONVERTED,

    /**
     * 已取消
     */
    CANCELED,

    /**
     * 分配
     */
    ASSIGN,

    /**
     * 批量分配
     */
    BATCH_ASSIGN,

    /**
     * 回收共有池
     */
    RECYCLE_COMMON_POOL,

    /**
     * 批量转移
     */
    BATCH_TRANSFER,

}
