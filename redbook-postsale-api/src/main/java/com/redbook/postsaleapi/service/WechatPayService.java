package com.redbook.postsaleapi.service;

import com.redbook.postsaleapi.model.WechatRefundOrderBean;
import com.redbook.postsaleapi.model.RefundResult;
import com.redbook.postsaleapi.model.WechatPayOrderBean;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-04-15 15:27
 */
public interface WechatPayService {

    /**
     * 业务统一下单（寄修单、配件购买）
     * @param payOrderBean
     * @return
     */
    String bizCreateOrder(WechatPayOrderBean payOrderBean);

    /**
     * 组装支付参数
     * @param prepayId 微信返回的预支付id
     * @param orderNo 系统业务单号（寄修单、配件单）
     * @return
     */
    Map<String,Object> getPayRequest(String prepayId,String orderNo);

    /**
     * 申请微信退款
     * @param refundOrderBean
     * @return
     */
    RefundResult refunds(WechatRefundOrderBean refundOrderBean);

    /**
     * 微信关单
     * @param orderNo 生成的支付订单号
     */
    Boolean closeOrder(String orderNo);

}
