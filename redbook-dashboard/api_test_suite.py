#!/usr/bin/env python3
"""
通用数据分析框架 API 测试套件 (Python版本)

运行方式：
1. 安装依赖：pip install requests colorama
2. 运行测试：python api_test_suite.py

<AUTHOR>
@since 2024-06-25
"""

import requests
import time
import json
from datetime import datetime
from colorama import init, Fore, Style
from typing import Dict, List, Any, Optional
from urllib.parse import urlencode

# 初始化colorama
init()

# ==================== 配置信息 ====================

CONFIG = {
    # API基础URL - 请根据实际环境修改
    'BASE_URL': 'http://localhost:8080',
    
    # 认证Token
    'AUTH_TOKEN': 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVlNTRiZDU4LWNkN2EtNGNjNS05NTU5LTQ4OTc2NjdlNmE4YSJ9.4UZMb3RwHNllCIvG9Zm6hbO5bix1-GrA22iMHkcITkOafYQULeZNLNNRsX1gavURVhKEUQV974cGcwvFjEh1aA',
    
    # 超时设置（秒）
    'TIMEOUT': 10,
    
    # 性能阈值（毫秒）
    'PERFORMANCE_THRESHOLD': 3000
}

# ==================== 枚举值定义 ====================

class BusinessType:
    STUDY = 'STUDY'
    RENEWAL = 'RENEWAL'
    TASK = 'TASK'
    MARKETING = 'MARKETING'

class DimensionLevel:
    USER = 'USER'
    COUNTRY = 'COUNTRY'
    PROVINCE = 'PROVINCE'
    CITY = 'CITY'
    ALIAS = 'ALIAS'
    SHOP = 'SHOP'
    CLASS = 'CLASS'

class StudyMetrics:
    QUIZ_AVG_SCORE = 'quizAvgScore'
    IMPROVE_SCORE_RATE = 'improveScoreRate'
    IMPROVE_SCORE_MEAN = 'improveScoreMean'
    STUDY_EFFICIENCY_MEAN = 'studyEfficiencyMean'
    STUDY_EFFECT_TIME_DAY = 'studyEffectTimeDay'
    STUDY_NEW_NUM_DAY = 'studyNewNumDay'
    STUDY_SPEED = 'studySpeed'
    REVIEW_FREQUENCY_DAY = 'reviewFrequencyDay'
    REVIEW_NUM_DAY = 'reviewNumDay'
    REVIEW_MULTIPLIER_MEAN = 'reviewMultiplierMean'

class AggregationType:
    SUM = 'SUM'
    AVG = 'AVG'
    MAX = 'MAX'
    MIN = 'MIN'
    COUNT = 'COUNT'

# ==================== 测试统计 ====================

class TestStats:
    def __init__(self):
        self.total = 0
        self.passed = 0
        self.failed = 0
        self.errors = []
        self.response_times = []
        self.start_time = None
        self.end_time = None

test_stats = TestStats()

# ==================== 工具函数 ====================

def create_session() -> requests.Session:
    """创建HTTP会话"""
    session = requests.Session()
    session.headers.update({
        'Authorization': f'Bearer {CONFIG["AUTH_TOKEN"]}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    })
    session.timeout = CONFIG['TIMEOUT']
    return session

def log_test_result(test_name: str, success: bool, response_time: int, error: Optional[str] = None):
    """记录测试结果"""
    test_stats.total += 1
    test_stats.response_times.append(response_time)
    
    if success:
        test_stats.passed += 1
        print(f"{Fore.GREEN}✓ {test_name} ({response_time}ms){Style.RESET_ALL}")
    else:
        test_stats.failed += 1
        test_stats.errors.append({'test_name': test_name, 'error': error or 'Unknown error'})
        print(f"{Fore.RED}✗ {test_name} ({response_time}ms){Style.RESET_ALL}")
        if error:
            print(f"{Fore.RED}  Error: {error}{Style.RESET_ALL}")
    
    # 性能警告
    if response_time > CONFIG['PERFORMANCE_THRESHOLD']:
        print(f"{Fore.YELLOW}  ⚠ Performance warning: {response_time}ms > {CONFIG['PERFORMANCE_THRESHOLD']}ms{Style.RESET_ALL}")

def validate_response(response: requests.Response, expected_fields: List[str] = None) -> bool:
    """验证响应数据结构"""
    if not response or not hasattr(response, 'json'):
        raise Exception('响应数据为空')
    
    try:
        data = response.json()
    except json.JSONDecodeError:
        raise Exception('响应不是有效的JSON格式')
    
    # 检查必要字段
    if expected_fields:
        for field in expected_fields:
            if field not in data:
                raise Exception(f'缺少必要字段: {field}')
    
    return True

async def execute_test(test_name: str, test_function):
    """执行API测试"""
    start_time = time.time()
    success = False
    error = None
    
    try:
        test_function()
        success = True
    except Exception as err:
        error = str(err)
        success = False
    
    response_time = int((time.time() - start_time) * 1000)
    log_test_result(test_name, success, response_time, error)
    
    return success

# ==================== 配置信息接口测试 ====================

def test_get_business_types():
    """测试获取业务类型列表"""
    session = create_session()
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/config/business-types")
    response.raise_for_status()
    
    validate_response(response)
    
    business_types = response.json()
    if not isinstance(business_types, list):
        raise Exception('业务类型列表应该是数组')
    
    if len(business_types) == 0:
        raise Exception('业务类型列表不能为空')
    
    # 验证必要字段
    first_item = business_types[0]
    required_fields = ['code', 'description', 'tableNamePattern', 'handlerClass', 'defaultMetrics']
    for field in required_fields:
        if field not in first_item:
            raise Exception(f'业务类型对象缺少字段: {field}')
    
    print(f"{Fore.BLUE}    Found {len(business_types)} business types{Style.RESET_ALL}")

def test_get_dimension_levels():
    """测试获取维度级别列表"""
    session = create_session()
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/config/dimension-levels")
    response.raise_for_status()
    
    validate_response(response)
    
    dimension_levels = response.json()
    if not isinstance(dimension_levels, list):
        raise Exception('维度级别列表应该是数组')
    
    if len(dimension_levels) == 0:
        raise Exception('维度级别列表不能为空')
    
    # 验证必要字段
    first_item = dimension_levels[0]
    required_fields = ['code', 'level', 'description', 'hierarchyLevel']
    for field in required_fields:
        if field not in first_item:
            raise Exception(f'维度级别对象缺少字段: {field}')
    
    print(f"{Fore.BLUE}    Found {len(dimension_levels)} dimension levels{Style.RESET_ALL}")

def test_get_analysis_types():
    """测试获取分析类型列表"""
    session = create_session()
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/config/analysis-types")
    response.raise_for_status()
    
    validate_response(response)
    
    analysis_types = response.json()
    if not isinstance(analysis_types, list):
        raise Exception('分析类型列表应该是数组')
    
    print(f"{Fore.BLUE}    Found {len(analysis_types)} analysis types{Style.RESET_ALL}")

def test_get_business_type_metrics():
    """测试获取指定业务类型的指标"""
    session = create_session()
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/config/business-types/{BusinessType.STUDY}/metrics")
    response.raise_for_status()
    
    validate_response(response)
    
    metrics = response.json()
    if not isinstance(metrics, list):
        raise Exception('指标列表应该是数组')
    
    if len(metrics) == 0:
        raise Exception('学习数据指标列表不能为空')
    
    print(f"{Fore.BLUE}    Found {len(metrics)} metrics for STUDY business type{Style.RESET_ALL}")

def test_get_config_overview():
    """测试获取配置概览"""
    session = create_session()
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/config/overview")
    response.raise_for_status()
    
    validate_response(response)
    
    overview = response.json()
    required_fields = ['businessTypes', 'dimensionLevels', 'statistics']
    for field in required_fields:
        if field not in overview:
            raise Exception(f'配置概览缺少字段: {field}')
    
    print(f"{Fore.BLUE}    Config overview loaded successfully{Style.RESET_ALL}")

# ==================== 数据分析接口测试 ====================

def test_trend_analysis():
    """测试趋势分析"""
    session = create_session()
    params = {
        'businessType': BusinessType.STUDY,
        'dimensionLevel': DimensionLevel.PROVINCE,
        'metricName': StudyMetrics.QUIZ_AVG_SCORE,
        'startDate': '2024-01-01',
        'endDate': '2024-01-31'
    }
    
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/trend", params=params)
    response.raise_for_status()
    
    validate_response(response)
    
    trend_data = response.json()
    if not isinstance(trend_data, list):
        raise Exception('趋势分析结果应该是数组')
    
    # 验证数据点结构
    if len(trend_data) > 0:
        first_point = trend_data[0]
        required_fields = ['date', 'value', 'formattedValue']
        for field in required_fields:
            if field not in first_point:
                raise Exception(f'趋势数据点缺少字段: {field}')
    
    print(f"{Fore.BLUE}    Found {len(trend_data)} trend points{Style.RESET_ALL}")

def test_ranking_analysis():
    """测试排行分析"""
    session = create_session()
    params = {
        'businessType': BusinessType.STUDY,
        'dimensionLevel': DimensionLevel.CITY,
        'rankBy': StudyMetrics.STUDY_EFFICIENCY_MEAN,
        'startDate': '2024-01-01',
        'endDate': '2024-01-31',
        'limit': 10
    }
    
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/ranking", params=params)
    response.raise_for_status()
    
    validate_response(response)
    
    ranking_data = response.json()
    if not isinstance(ranking_data, list):
        raise Exception('排行分析结果应该是数组')
    
    # 验证排行项结构
    if len(ranking_data) > 0:
        first_item = ranking_data[0]
        required_fields = ['rank', 'name', 'value', 'formattedValue']
        for field in required_fields:
            if field not in first_item:
                raise Exception(f'排行项缺少字段: {field}')
        
        # 验证排名顺序
        if first_item['rank'] != 1:
            raise Exception('第一个排行项的排名应该是1')
    
    print(f"{Fore.BLUE}    Found {len(ranking_data)} ranking items{Style.RESET_ALL}")

def test_comparison_analysis():
    """测试对比分析"""
    session = create_session()
    params = {
        'businessType': BusinessType.STUDY,
        'dimensionLevel': DimensionLevel.PROVINCE,
        'metricName': StudyMetrics.IMPROVE_SCORE_RATE,
        'currentStartDate': '2024-02-01',
        'currentEndDate': '2024-02-29',
        'previousStartDate': '2024-01-01',
        'previousEndDate': '2024-01-31'
    }
    
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/comparison", params=params)
    response.raise_for_status()
    
    validate_response(response)
    
    comparison_data = response.json()
    required_fields = ['currentValue', 'previousValue', 'change', 'changeRate', 'trend']
    for field in required_fields:
        if field not in comparison_data:
            raise Exception(f'对比分析结果缺少字段: {field}')
    
    # 验证趋势值
    valid_trends = ['UP', 'DOWN', 'STABLE']
    if comparison_data['trend'] not in valid_trends:
        raise Exception(f'无效的趋势值: {comparison_data["trend"]}')
    
    print(f"{Fore.BLUE}    Comparison: {comparison_data['trend']}, Change: {comparison_data['changeRate']}%{Style.RESET_ALL}")

def test_summary_analysis():
    """测试汇总统计"""
    session = create_session()
    params = {
        'businessType': BusinessType.STUDY,
        'dimensionLevel': DimensionLevel.COUNTRY,
        'metricName': StudyMetrics.STUDY_EFFECT_TIME_DAY,
        'aggregationType': AggregationType.AVG,
        'startDate': '2024-01-01',
        'endDate': '2024-01-31'
    }
    
    response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/summary", params=params)
    response.raise_for_status()
    
    validate_response(response)
    
    summary_data = response.json()
    if not isinstance(summary_data, (int, float)):
        raise Exception('汇总统计结果应该是数字')
    
    print(f"{Fore.BLUE}    Summary result: {summary_data}{Style.RESET_ALL}")

# ==================== Web页面数据接口测试 ====================

def test_get_metrics_options():
    """测试获取指标下拉框数据"""
    session = create_session()
    session.headers.update({'Content-Type': 'application/x-www-form-urlencoded'})
    
    data = {'businessType': BusinessType.STUDY}
    response = session.post(f"{CONFIG['BASE_URL']}/dashboard/metrics", data=data)
    response.raise_for_status()
    
    result = response.json()
    if result.get('code') != 200:
        raise Exception(f"API返回错误: {result.get('msg')}")
    
    metrics = result.get('data', [])
    if not isinstance(metrics, list):
        raise Exception('指标选项应该是数组')
    
    # 验证选项结构
    if len(metrics) > 0:
        first_option = metrics[0]
        if 'value' not in first_option or 'label' not in first_option:
            raise Exception('指标选项缺少value或label字段')
    
    print(f"{Fore.BLUE}    Found {len(metrics)} metric options{Style.RESET_ALL}")

def test_get_dimension_values():
    """测试获取维度值下拉框数据"""
    session = create_session()
    session.headers.update({'Content-Type': 'application/x-www-form-urlencoded'})
    
    data = {
        'businessType': BusinessType.STUDY,
        'dimensionLevel': DimensionLevel.PROVINCE,
        'dimensionField': 'province'
    }
    response = session.post(f"{CONFIG['BASE_URL']}/dashboard/dimension-values", data=data)
    response.raise_for_status()
    
    result = response.json()
    if result.get('code') != 200:
        raise Exception(f"API返回错误: {result.get('msg')}")
    
    values = result.get('data', [])
    if not isinstance(values, list):
        raise Exception('维度值选项应该是数组')
    
    print(f"{Fore.BLUE}    Found {len(values)} dimension values{Style.RESET_ALL}")

# ==================== 异常场景测试 ====================

def test_invalid_business_type():
    """测试无效的业务类型"""
    session = create_session()
    params = {
        'businessType': 'INVALID_TYPE',
        'dimensionLevel': DimensionLevel.PROVINCE,
        'metricName': StudyMetrics.QUIZ_AVG_SCORE,
        'startDate': '2024-01-01',
        'endDate': '2024-01-31'
    }
    
    try:
        response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/trend", params=params)
        response.raise_for_status()
        raise Exception('应该返回错误，但请求成功了')
    except requests.exceptions.HTTPError as e:
        if e.response.status_code >= 400:
            # 预期的错误
            print(f"{Fore.BLUE}    Expected error: {e.response.status_code}{Style.RESET_ALL}")
        else:
            raise e

def test_invalid_date_format():
    """测试无效的日期格式"""
    session = create_session()
    params = {
        'businessType': BusinessType.STUDY,
        'dimensionLevel': DimensionLevel.PROVINCE,
        'metricName': StudyMetrics.QUIZ_AVG_SCORE,
        'startDate': '2024/01/01',  # 错误格式
        'endDate': '2024-01-31'
    }
    
    try:
        response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/trend", params=params)
        response.raise_for_status()
        raise Exception('应该返回错误，但请求成功了')
    except requests.exceptions.HTTPError as e:
        if e.response.status_code >= 400:
            # 预期的错误
            print(f"{Fore.BLUE}    Expected error: {e.response.status_code}{Style.RESET_ALL}")
        else:
            raise e

def test_missing_required_params():
    """测试缺少必填参数"""
    session = create_session()
    params = {
        'businessType': BusinessType.STUDY,
        # 缺少 dimensionLevel
        'metricName': StudyMetrics.QUIZ_AVG_SCORE,
        'startDate': '2024-01-01',
        'endDate': '2024-01-31'
    }
    
    try:
        response = session.get(f"{CONFIG['BASE_URL']}/api/analysis/trend", params=params)
        response.raise_for_status()
        raise Exception('应该返回错误，但请求成功了')
    except requests.exceptions.HTTPError as e:
        if e.response.status_code >= 400:
            # 预期的错误
            print(f"{Fore.BLUE}    Expected error: {e.response.status_code}{Style.RESET_ALL}")
        else:
            raise e

# ==================== 主测试函数 ====================

def run_all_tests():
    """运行所有测试"""
    print(f"{Fore.CYAN}🚀 开始运行API测试套件...\n{Style.RESET_ALL}")
    test_stats.start_time = time.time()
    
    # 配置信息接口测试
    print(f"{Fore.YELLOW}📋 配置信息接口测试{Style.RESET_ALL}")
    execute_test('获取业务类型列表', test_get_business_types)
    execute_test('获取维度级别列表', test_get_dimension_levels)
    execute_test('获取分析类型列表', test_get_analysis_types)
    execute_test('获取学习数据指标', test_get_business_type_metrics)
    execute_test('获取配置概览', test_get_config_overview)
    
    print('')
    
    # 数据分析接口测试
    print(f"{Fore.YELLOW}📊 数据分析接口测试{Style.RESET_ALL}")
    execute_test('趋势分析', test_trend_analysis)
    execute_test('排行分析', test_ranking_analysis)
    execute_test('对比分析', test_comparison_analysis)
    execute_test('汇总统计', test_summary_analysis)
    
    print('')
    
    # Web页面数据接口测试
    print(f"{Fore.YELLOW}🌐 Web页面数据接口测试{Style.RESET_ALL}")
    execute_test('获取指标下拉框数据', test_get_metrics_options)
    execute_test('获取维度值下拉框数据', test_get_dimension_values)
    
    print('')
    
    # 异常场景测试
    print(f"{Fore.YELLOW}⚠️  异常场景测试{Style.RESET_ALL}")
    execute_test('无效业务类型', test_invalid_business_type)
    execute_test('无效日期格式', test_invalid_date_format)
    execute_test('缺少必填参数', test_missing_required_params)
    
    test_stats.end_time = time.time()
    
    # 输出测试结果
    print_test_summary()

def execute_test(test_name: str, test_function):
    """执行单个测试"""
    start_time = time.time()
    success = False
    error = None
    
    try:
        test_function()
        success = True
    except Exception as err:
        error = str(err)
        success = False
    
    response_time = int((time.time() - start_time) * 1000)
    log_test_result(test_name, success, response_time, error)
    
    return success

def print_test_summary():
    """打印测试摘要"""
    print(f"\n{Fore.CYAN}📈 测试结果摘要{Style.RESET_ALL}")
    print('=' * 50)
    
    total_time = int((test_stats.end_time - test_stats.start_time) * 1000)
    avg_response_time = sum(test_stats.response_times) / len(test_stats.response_times) if test_stats.response_times else 0
    max_response_time = max(test_stats.response_times) if test_stats.response_times else 0
    min_response_time = min(test_stats.response_times) if test_stats.response_times else 0
    
    print(f"{Fore.WHITE}总测试数量: {test_stats.total}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}通过测试: {test_stats.passed}{Style.RESET_ALL}")
    print(f"{Fore.RED}失败测试: {test_stats.failed}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}成功率: {((test_stats.passed / test_stats.total) * 100):.2f}%{Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}⏱️  性能统计{Style.RESET_ALL}")
    print('=' * 50)
    print(f"{Fore.WHITE}总耗时: {total_time}ms{Style.RESET_ALL}")
    print(f"{Fore.WHITE}平均响应时间: {avg_response_time:.2f}ms{Style.RESET_ALL}")
    print(f"{Fore.WHITE}最快响应时间: {min_response_time}ms{Style.RESET_ALL}")
    print(f"{Fore.WHITE}最慢响应时间: {max_response_time}ms{Style.RESET_ALL}")
    
    if test_stats.errors:
        print(f"\n{Fore.RED}❌ 失败测试详情{Style.RESET_ALL}")
        print('=' * 50)
        for error in test_stats.errors:
            print(f"{Fore.RED}{error['test_name']}: {error['error']}{Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}🎉 测试完成!{Style.RESET_ALL}")
    
    # 退出码
    exit(1 if test_stats.failed > 0 else 0)

# ==================== 启动测试 ====================

if __name__ == '__main__':
    try:
        run_all_tests()
    except Exception as error:
        print(f"{Fore.RED}测试套件执行失败: {error}{Style.RESET_ALL}")
        exit(1)
