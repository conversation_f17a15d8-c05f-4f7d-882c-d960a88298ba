# 数据库和Swagger配置修复说明

## 问题描述

在清理旧文件后，发现两个关键问题：
1. **数据库配置丢失**: Dashboard模块的独立数据源配置被删除
2. **Swagger扫描问题**: API接口无法被Swagger正确扫描和显示

## 修复方案

### 1. 恢复数据库配置

#### 1.1 创建数据源配置类
**文件**: `DashboardDataSourceConfig.java`

```java
@Configuration
@MapperScan(basePackages = "com.redbook.dashboard.mapper", sqlSessionFactoryRef = "dashboardSqlSessionFactory")
public class DashboardDataSourceConfig {
    
    @Bean(name = "dashboardDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid.dashboard")
    @Primary
    public DataSource dashboardDataSource() {
        return new DruidDataSource();
    }
    
    @Bean(name = "dashboardTransactionManager")
    @Primary
    public DataSourceTransactionManager dashboardTransactionManager(@Qualifier("dashboardDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
    
    @Bean(name = "dashboardSqlSessionFactory")
    @Primary
    public SqlSessionFactory dashboardSqlSessionFactory(@Qualifier("dashboardDataSource") DataSource dataSource) throws Exception {
        // 配置MyBatis和分页插件
    }
}
```

#### 1.2 配置特点
- **独立数据源**: 使用`spring.datasource.druid.dashboard`配置前缀
- **独立事务管理**: 专用的事务管理器`dashboardTransactionManager`
- **MyBatis配置**: 包含分页插件和类型别名配置
- **Mapper扫描**: 指定扫描`com.redbook.dashboard.mapper`包

### 2. 配置Swagger支持

#### 2.1 创建Swagger配置类
**文件**: `DashboardSwaggerConfig.java`

```java
@Configuration
@EnableSwagger2
public class DashboardSwaggerConfig {
    
    @Bean
    public Docket dashboardApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("dashboard-api")
                .apiInfo(dashboardApiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.redbook.dashboard.controller"))
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.ant("/api/analysis/**"))
                .build();
    }
}
```

#### 2.2 配置特点
- **独立API组**: 使用`dashboard-api`分组
- **包扫描**: 扫描`com.redbook.dashboard.controller`包
- **路径过滤**: 只包含`/api/analysis/**`路径的接口
- **注解过滤**: 只包含标注了`@ApiOperation`的方法

### 3. 整合配置管理

#### 3.1 更新AnalysisConfig
```java
@Configuration
@EnableConfigurationProperties
@ComponentScan(basePackages = "com.redbook.dashboard")
@Import({DashboardDataSourceConfig.class, DashboardSwaggerConfig.class})
public class AnalysisConfig {
    // 整合所有配置
}
```

#### 3.2 更新主应用程序配置
**文件**: `application.yml`
```yaml
spring:
  profiles:
    active: dev
    include: dashboard  # 包含dashboard配置文件
```

### 4. 创建Mapper XML文件

#### 4.1 目录结构
```
redbook-dashboard/src/main/resources/
└── mapper/
    └── dashboard/
        └── AnalysisDataMapper.xml
```

#### 4.2 XML配置特点
- **动态表名支持**: 使用`${tableName}`参数
- **通用结果映射**: 支持所有业务类型的字段映射
- **多种查询类型**: 列表查询、分页查询、统计查询、汇总查询等

### 5. 服务层事务配置

#### 5.1 添加事务注解
```java
@Service
@Transactional(transactionManager = "dashboardTransactionManager")
public class AnalysisDataService {
    // 使用独立的事务管理器
}
```

## 配置验证

### 1. 数据库连接验证
启动应用后检查日志：
```
HikariPool-1 - Starting...
HikariPool-1 - Start completed.
```

### 2. Swagger验证
访问Swagger UI：
- URL: `http://localhost:8080/swagger-ui.html`
- 查看API分组: `dashboard-api`
- 验证接口: `/api/analysis/**`

### 3. API接口验证
测试关键接口：
```bash
# 获取业务类型列表
GET /api/analysis/config/business-types

# 获取维度级别列表  
GET /api/analysis/config/dimension-levels

# 配置概览
GET /api/analysis/config/overview
```

## 数据库表结构要求

### 1. 表命名规范
```
intelligence_{level}_{business}_day
```

示例：
- `intelligence_province_study_module_day` (省份维度学习数据)
- `intelligence_city_study_module_day` (城市维度学习数据)
- `intelligence_user_study_module_day` (用户维度学习数据)

### 2. 必需字段
```sql
-- 基础字段
id BIGINT PRIMARY KEY,
business_type VARCHAR(50),
dimension_level VARCHAR(50),
date_key INT,
study_date DATE,

-- 通用维度字段
dimension_id1 VARCHAR(100),
dimension_id2 VARCHAR(100), 
dimension_id3 VARCHAR(100),
dimension_name1 VARCHAR(200),
dimension_name2 VARCHAR(200),
dimension_name3 VARCHAR(200),

-- 学习指标字段
quiz_avg_score INT,
improve_score_rate FLOAT,
study_efficiency_mean FLOAT,
study_effect_time_day INT,
-- ... 其他指标字段

-- 扩展字段
metrics_data TEXT,
extend_data TEXT,
data_version VARCHAR(20),

-- 审计字段
create_by VARCHAR(64),
create_time DATETIME,
update_by VARCHAR(64), 
update_time DATETIME,
remark VARCHAR(500)
```

## 常见问题解决

### Q1: 数据源连接失败
**解决方案**:
1. 检查`application-dashboard.yml`中的数据库配置
2. 确认数据库服务正常运行
3. 验证网络连接和防火墙设置

### Q2: Swagger无法显示API
**解决方案**:
1. 确认Controller类标注了`@ApiOperation`注解
2. 检查包扫描路径是否正确
3. 验证Swagger配置是否生效

### Q3: Mapper找不到
**解决方案**:
1. 确认Mapper接口标注了`@Mapper`和`@Repository`注解
2. 检查`@MapperScan`配置是否正确
3. 验证XML文件路径是否正确

### Q4: 事务不生效
**解决方案**:
1. 确认使用了正确的事务管理器
2. 检查`@Transactional`注解配置
3. 验证数据源配置是否正确

## 后续优化建议

1. **连接池监控**: 添加Druid监控页面
2. **SQL性能监控**: 配置慢SQL监控
3. **API文档完善**: 添加更详细的接口说明
4. **健康检查**: 实现数据库连接健康检查
5. **配置外部化**: 支持通过环境变量配置数据库连接

## 文件清单

### 新增文件
- `DashboardDataSourceConfig.java` - 数据源配置
- `DashboardSwaggerConfig.java` - Swagger配置  
- `mapper/dashboard/AnalysisDataMapper.xml` - Mapper XML文件
- `DATABASE_AND_SWAGGER_FIX.md` - 本修复文档

### 修改文件
- `AnalysisConfig.java` - 整合配置管理
- `AnalysisDataMapper.java` - 添加Repository注解
- `AnalysisDataService.java` - 添加事务注解
- `application.yml` - 包含dashboard配置文件

现在Dashboard模块具备了完整的独立数据源和Swagger支持！
