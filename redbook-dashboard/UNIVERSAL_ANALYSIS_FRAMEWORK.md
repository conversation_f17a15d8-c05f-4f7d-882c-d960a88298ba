# 通用数据分析框架

## 概述

这是一个基于配置驱动的通用数据分析框架，支持多种业务类型、多维度分析和多种分析方法。框架采用策略模式和枚举配置，实现了高度的可扩展性和可维护性。

## 核心特性

### 1. 多业务类型支持
- **学习数据分析** (STUDY): 支持学习时长、成绩、效率等指标
- **续费数据分析** (RENEWAL): 支持续费率、续费金额等指标（预留）
- **任务数据分析** (TASK): 支持任务完成率、任务得分等指标（预留）
- **营销数据分析** (MARKETING): 支持转化率、点击率等指标（预留）

### 2. 多维度分析
- **用户维度** (USER): 按用户个体统计
- **全国维度** (COUNTRY): 全国汇总数据
- **省份维度** (PROVINCE): 按省份统计
- **城市维度** (CITY): 按城市统计
- **体验中心维度** (ALIAS): 按体验中心统计
- **专卖店维度** (SHOP): 按专卖店统计
- **班级维度** (CLASS): 按班级统计

### 3. 多种分析类型
- **趋势分析** (TREND): 时间序列数据的趋势变化分析
- **排行分析** (RANKING): 按指定指标进行排序的排行榜分析
- **对比分析** (COMPARISON): 不同时间段或不同维度的数据对比分析
- **汇总统计** (SUMMARY): 数据的汇总统计信息
- **列表查询** (LIST): 基础的数据列表展示

## 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    通用分析框架架构                           │
├─────────────────────────────────────────────────────────────┤
│  Controller Layer                                           │
│  ├── UniversalAnalysisController (统一分析接口)              │
│  └── AnalysisConfigController (配置信息接口)                 │
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ├── UniversalAnalysisService (通用分析服务)                 │
│  └── AnalysisDataService (数据访问服务)                      │
├─────────────────────────────────────────────────────────────┤
│  Handler Layer (策略模式)                                    │
│  ├── MetricsHandler (指标处理器接口)                         │
│  ├── StudyMetricsHandler (学习数据处理器)                    │
│  ├── RenewalMetricsHandler (续费数据处理器)                  │
│  ├── TaskMetricsHandler (任务数据处理器)                     │
│  └── MarketingMetricsHandler (营销数据处理器)                │
├─────────────────────────────────────────────────────────────┤
│  Configuration Layer                                        │
│  ├── BusinessTypeEnum (业务类型配置)                         │
│  ├── DimensionLevelEnum (维度级别配置)                       │
│  ├── AnalysisTypeEnum (分析类型配置)                         │
│  └── AnalysisConfig (配置管理)                               │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ├── AnalysisDataEntity (通用数据实体)                       │
│  └── AnalysisDataMapper (数据访问接口)                       │
└─────────────────────────────────────────────────────────────┘
```

### 设计模式

1. **策略模式**: 通过MetricsHandler接口实现不同业务类型的指标处理逻辑
2. **配置驱动**: 通过枚举类管理业务类型、维度级别和分析类型的配置
3. **动态表名**: 支持根据业务类型和维度级别动态生成表名
4. **字段映射**: 通过配置实现不同维度的字段映射关系

## 使用指南

### 1. API接口使用

#### 趋势分析
```http
GET /api/analysis/trend?businessType=STUDY&dimensionLevel=PROVINCE&metricName=quizAvgScore&startDate=2024-01-01&endDate=2024-12-31
```

#### 排行分析
```http
GET /api/analysis/ranking?businessType=STUDY&dimensionLevel=CITY&rankBy=studyEfficiencyMean&startDate=2024-01-01&endDate=2024-12-31&limit=10
```

#### 对比分析
```http
GET /api/analysis/comparison?businessType=STUDY&dimensionLevel=PROVINCE&metricName=quizAvgScore&currentStartDate=2024-12-01&currentEndDate=2024-12-31&previousStartDate=2024-11-01&previousEndDate=2024-11-30
```

#### 汇总统计
```http
GET /api/analysis/summary?businessType=STUDY&dimensionLevel=COUNTRY&metricName=studyEffectTimeDay&aggregationType=AVG&startDate=2024-01-01&endDate=2024-12-31
```

### 2. 配置信息查询

#### 获取所有业务类型
```http
GET /api/analysis/config/business-types
```

#### 获取所有维度级别
```http
GET /api/analysis/config/dimension-levels
```

#### 获取支持的指标
```http
GET /api/analysis/config/business-types/STUDY/metrics
```

### 3. 数据表结构

框架支持动态表名，表名格式为：`intelligence_{level}_{business}_day`

例如：
- `intelligence_province_study_module_day` (省份维度学习数据)
- `intelligence_city_study_module_day` (城市维度学习数据)
- `intelligence_province_renewal_day` (省份维度续费数据)

## 扩展指南

### 1. 添加新的业务类型

1. 在`BusinessTypeEnum`中添加新的枚举值
2. 创建对应的`MetricsHandler`实现类
3. 在`AnalysisDataEntity`中添加相应的字段（如需要）

```java
// 1. 添加枚举
FINANCE("财务数据", "intelligence_{level}_finance_day", FinanceMetricsHandler.class,
        Arrays.asList("revenue", "profit", "cost"));

// 2. 创建处理器
@Component
public class FinanceMetricsHandler implements MetricsHandler {
    // 实现接口方法
}
```

### 2. 添加新的维度级别

1. 在`DimensionLevelEnum`中添加新的枚举值
2. 配置相应的字段映射关系
3. 在`AnalysisDataEntity`中添加对应字段

```java
REGION("region", "区域维度", 
       Arrays.asList("regionId", "regionName"), 
       Arrays.asList("dimensionId1", "dimensionName1"));
```

### 3. 添加新的分析类型

1. 在`AnalysisTypeEnum`中添加新的枚举值
2. 在相应的`MetricsHandler`中实现分析逻辑
3. 在控制器中添加对应的API接口

## 最佳实践

### 1. 性能优化
- 使用合适的数据库索引
- 对大数据量查询进行分页处理
- 缓存常用的配置信息

### 2. 数据质量
- 在数据入库前进行数据验证
- 定期检查数据完整性
- 记录数据质量指标

### 3. 监控告警
- 监控API响应时间
- 设置数据异常告警
- 记录关键操作日志

## 注意事项

1. **数据库表结构**: 确保数据库表结构与`AnalysisDataEntity`字段对应
2. **字段映射**: 维度级别的字段映射需要与实际表结构一致
3. **指标计算**: 不同业务类型的指标计算逻辑需要在对应的Handler中实现
4. **权限控制**: 根据需要添加相应的权限控制逻辑
5. **数据安全**: 敏感数据需要进行脱敏处理

## 未来规划

1. **机器学习集成**: 集成预测分析和异常检测功能
2. **实时分析**: 支持流式数据处理和实时分析
3. **可视化增强**: 提供更丰富的图表类型和交互功能
4. **自动化报告**: 支持定时生成和推送分析报告
5. **多租户支持**: 支持多租户数据隔离和权限管理

## 技术栈

- **后端框架**: Spring Boot 2.x
- **数据库**: MySQL 8.0+
- **ORM框架**: MyBatis Plus
- **API文档**: Swagger 2
- **日志框架**: Logback
- **构建工具**: Maven

## 联系方式

如有问题或建议，请联系开发团队。
