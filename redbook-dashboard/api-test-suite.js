/**
 * 通用数据分析框架 API 测试套件
 * 
 * 运行方式：
 * 1. 安装依赖：npm install axios chalk
 * 2. 运行测试：node api-test-suite.js
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */

const axios = require('axios');
const chalk = require('chalk');

// ==================== 配置信息 ====================

const CONFIG = {
    // API基础URL - 请根据实际环境修改
    BASE_URL: 'http://localhost:8080',
    
    // 认证Token
    AUTH_TOKEN: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVlNTRiZDU4LWNkN2EtNGNjNS05NTU5LTQ4OTc2NjdlNmE4YSJ9.4UZMb3RwHNllCIvG9Zm6hbO5bix1-GrA22iMHkcITkOafYQULeZNLNNRsX1gavURVhKEUQV974cGcwvFjEh1aA',
    
    // 超时设置（毫秒）
    TIMEOUT: 10000,
    
    // 性能阈值（毫秒）
    PERFORMANCE_THRESHOLD: 3000
};

// ==================== 枚举值定义 ====================

const BusinessType = {
    STUDY: 'STUDY',
    RENEWAL: 'RENEWAL',
    TASK: 'TASK',
    MARKETING: 'MARKETING'
};

const DimensionLevel = {
    USER: 'USER',
    COUNTRY: 'COUNTRY',
    PROVINCE: 'PROVINCE',
    CITY: 'CITY',
    ALIAS: 'ALIAS',
    SHOP: 'SHOP',
    CLASS: 'CLASS'
};

const StudyMetrics = {
    QUIZ_AVG_SCORE: 'quizAvgScore',
    IMPROVE_SCORE_RATE: 'improveScoreRate',
    IMPROVE_SCORE_MEAN: 'improveScoreMean',
    STUDY_EFFICIENCY_MEAN: 'studyEfficiencyMean',
    STUDY_EFFECT_TIME_DAY: 'studyEffectTimeDay',
    STUDY_NEW_NUM_DAY: 'studyNewNumDay',
    STUDY_SPEED: 'studySpeed',
    REVIEW_FREQUENCY_DAY: 'reviewFrequencyDay',
    REVIEW_NUM_DAY: 'reviewNumDay',
    REVIEW_MULTIPLIER_MEAN: 'reviewMultiplierMean'
};

const AggregationType = {
    SUM: 'SUM',
    AVG: 'AVG',
    MAX: 'MAX',
    MIN: 'MIN',
    COUNT: 'COUNT'
};

// ==================== 测试统计 ====================

const TestStats = {
    total: 0,
    passed: 0,
    failed: 0,
    errors: [],
    responseTimes: [],
    startTime: null,
    endTime: null
};

// ==================== 工具函数 ====================

/**
 * 创建HTTP客户端
 */
function createHttpClient() {
    return axios.create({
        baseURL: CONFIG.BASE_URL,
        timeout: CONFIG.TIMEOUT,
        headers: {
            'Authorization': `Bearer ${CONFIG.AUTH_TOKEN}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    });
}

/**
 * 记录测试结果
 */
function logTestResult(testName, success, responseTime, error = null) {
    TestStats.total++;
    TestStats.responseTimes.push(responseTime);
    
    if (success) {
        TestStats.passed++;
        console.log(chalk.green(`✓ ${testName} (${responseTime}ms)`));
    } else {
        TestStats.failed++;
        TestStats.errors.push({ testName, error: error?.message || 'Unknown error' });
        console.log(chalk.red(`✗ ${testName} (${responseTime}ms)`));
        if (error) {
            console.log(chalk.red(`  Error: ${error.message}`));
        }
    }
    
    // 性能警告
    if (responseTime > CONFIG.PERFORMANCE_THRESHOLD) {
        console.log(chalk.yellow(`  ⚠ Performance warning: ${responseTime}ms > ${CONFIG.PERFORMANCE_THRESHOLD}ms`));
    }
}

/**
 * 验证响应数据结构
 */
function validateResponse(response, expectedFields = []) {
    if (!response || !response.data) {
        throw new Error('响应数据为空');
    }
    
    // 检查必要字段
    for (const field of expectedFields) {
        if (!(field in response.data)) {
            throw new Error(`缺少必要字段: ${field}`);
        }
    }
    
    return true;
}

/**
 * 执行API测试
 */
async function executeTest(testName, testFunction) {
    const startTime = Date.now();
    let success = false;
    let error = null;
    
    try {
        await testFunction();
        success = true;
    } catch (err) {
        error = err;
        success = false;
    }
    
    const responseTime = Date.now() - startTime;
    logTestResult(testName, success, responseTime, error);
    
    return success;
}

// ==================== 配置信息接口测试 ====================

/**
 * 测试获取业务类型列表
 */
async function testGetBusinessTypes() {
    const client = createHttpClient();
    const response = await client.get('/api/analysis/config/business-types');
    
    validateResponse(response);
    
    const businessTypes = response.data;
    if (!Array.isArray(businessTypes)) {
        throw new Error('业务类型列表应该是数组');
    }
    
    if (businessTypes.length === 0) {
        throw new Error('业务类型列表不能为空');
    }
    
    // 验证必要字段
    const firstItem = businessTypes[0];
    const requiredFields = ['code', 'description', 'tableNamePattern', 'handlerClass', 'defaultMetrics'];
    for (const field of requiredFields) {
        if (!(field in firstItem)) {
            throw new Error(`业务类型对象缺少字段: ${field}`);
        }
    }
    
    console.log(chalk.blue(`    Found ${businessTypes.length} business types`));
}

/**
 * 测试获取维度级别列表
 */
async function testGetDimensionLevels() {
    const client = createHttpClient();
    const response = await client.get('/api/analysis/config/dimension-levels');
    
    validateResponse(response);
    
    const dimensionLevels = response.data;
    if (!Array.isArray(dimensionLevels)) {
        throw new Error('维度级别列表应该是数组');
    }
    
    if (dimensionLevels.length === 0) {
        throw new Error('维度级别列表不能为空');
    }
    
    // 验证必要字段
    const firstItem = dimensionLevels[0];
    const requiredFields = ['code', 'level', 'description', 'hierarchyLevel'];
    for (const field of requiredFields) {
        if (!(field in firstItem)) {
            throw new Error(`维度级别对象缺少字段: ${field}`);
        }
    }
    
    console.log(chalk.blue(`    Found ${dimensionLevels.length} dimension levels`));
}

/**
 * 测试获取分析类型列表
 */
async function testGetAnalysisTypes() {
    const client = createHttpClient();
    const response = await client.get('/api/analysis/config/analysis-types');
    
    validateResponse(response);
    
    const analysisTypes = response.data;
    if (!Array.isArray(analysisTypes)) {
        throw new Error('分析类型列表应该是数组');
    }
    
    console.log(chalk.blue(`    Found ${analysisTypes.length} analysis types`));
}

/**
 * 测试获取指定业务类型的指标
 */
async function testGetBusinessTypeMetrics() {
    const client = createHttpClient();
    const response = await client.get(`/api/analysis/config/business-types/${BusinessType.STUDY}/metrics`);
    
    validateResponse(response);
    
    const metrics = response.data;
    if (!Array.isArray(metrics)) {
        throw new Error('指标列表应该是数组');
    }
    
    if (metrics.length === 0) {
        throw new Error('学习数据指标列表不能为空');
    }
    
    console.log(chalk.blue(`    Found ${metrics.length} metrics for STUDY business type`));
}

/**
 * 测试获取配置概览
 */
async function testGetConfigOverview() {
    const client = createHttpClient();
    const response = await client.get('/api/analysis/config/overview');
    
    validateResponse(response);
    
    const overview = response.data;
    const requiredFields = ['businessTypes', 'dimensionLevels', 'statistics'];
    for (const field of requiredFields) {
        if (!(field in overview)) {
            throw new Error(`配置概览缺少字段: ${field}`);
        }
    }
    
    console.log(chalk.blue(`    Config overview loaded successfully`));
}

// ==================== 数据分析接口测试 ====================

/**
 * 测试趋势分析
 */
async function testTrendAnalysis() {
    const client = createHttpClient();
    const params = {
        businessType: BusinessType.STUDY,
        dimensionLevel: DimensionLevel.PROVINCE,
        metricName: StudyMetrics.QUIZ_AVG_SCORE,
        startDate: '2024-01-01',
        endDate: '2024-01-31'
    };
    
    const response = await client.get('/api/analysis/trend', { params });
    validateResponse(response);
    
    const trendData = response.data;
    if (!Array.isArray(trendData)) {
        throw new Error('趋势分析结果应该是数组');
    }
    
    // 验证数据点结构
    if (trendData.length > 0) {
        const firstPoint = trendData[0];
        const requiredFields = ['date', 'value', 'formattedValue'];
        for (const field of requiredFields) {
            if (!(field in firstPoint)) {
                throw new Error(`趋势数据点缺少字段: ${field}`);
            }
        }
    }
    
    console.log(chalk.blue(`    Found ${trendData.length} trend points`));
}

/**
 * 测试排行分析
 */
async function testRankingAnalysis() {
    const client = createHttpClient();
    const params = {
        businessType: BusinessType.STUDY,
        dimensionLevel: DimensionLevel.CITY,
        rankBy: StudyMetrics.STUDY_EFFICIENCY_MEAN,
        startDate: '2024-01-01',
        endDate: '2024-01-31',
        limit: 10
    };
    
    const response = await client.get('/api/analysis/ranking', { params });
    validateResponse(response);
    
    const rankingData = response.data;
    if (!Array.isArray(rankingData)) {
        throw new Error('排行分析结果应该是数组');
    }
    
    // 验证排行项结构
    if (rankingData.length > 0) {
        const firstItem = rankingData[0];
        const requiredFields = ['rank', 'name', 'value', 'formattedValue'];
        for (const field of requiredFields) {
            if (!(field in firstItem)) {
                throw new Error(`排行项缺少字段: ${field}`);
            }
        }
        
        // 验证排名顺序
        if (firstItem.rank !== 1) {
            throw new Error('第一个排行项的排名应该是1');
        }
    }
    
    console.log(chalk.blue(`    Found ${rankingData.length} ranking items`));
}

/**
 * 测试对比分析
 */
async function testComparisonAnalysis() {
    const client = createHttpClient();
    const params = {
        businessType: BusinessType.STUDY,
        dimensionLevel: DimensionLevel.PROVINCE,
        metricName: StudyMetrics.IMPROVE_SCORE_RATE,
        currentStartDate: '2024-02-01',
        currentEndDate: '2024-02-29',
        previousStartDate: '2024-01-01',
        previousEndDate: '2024-01-31'
    };
    
    const response = await client.get('/api/analysis/comparison', { params });
    validateResponse(response);
    
    const comparisonData = response.data;
    const requiredFields = ['currentValue', 'previousValue', 'change', 'changeRate', 'trend'];
    for (const field of requiredFields) {
        if (!(field in comparisonData)) {
            throw new Error(`对比分析结果缺少字段: ${field}`);
        }
    }
    
    // 验证趋势值
    const validTrends = ['UP', 'DOWN', 'STABLE'];
    if (!validTrends.includes(comparisonData.trend)) {
        throw new Error(`无效的趋势值: ${comparisonData.trend}`);
    }
    
    console.log(chalk.blue(`    Comparison: ${comparisonData.trend}, Change: ${comparisonData.changeRate}%`));
}

/**
 * 测试汇总统计
 */
async function testSummaryAnalysis() {
    const client = createHttpClient();
    const params = {
        businessType: BusinessType.STUDY,
        dimensionLevel: DimensionLevel.COUNTRY,
        metricName: StudyMetrics.STUDY_EFFECT_TIME_DAY,
        aggregationType: AggregationType.AVG,
        startDate: '2024-01-01',
        endDate: '2024-01-31'
    };
    
    const response = await client.get('/api/analysis/summary', { params });
    validateResponse(response);
    
    const summaryData = response.data;
    if (typeof summaryData !== 'number') {
        throw new Error('汇总统计结果应该是数字');
    }
    
    console.log(chalk.blue(`    Summary result: ${summaryData}`));
}

// ==================== Web页面数据接口测试 ====================

/**
 * 测试获取指标下拉框数据
 */
async function testGetMetricsOptions() {
    const client = createHttpClient();
    const formData = new URLSearchParams();
    formData.append('businessType', BusinessType.STUDY);
    
    const response = await client.post('/dashboard/metrics', formData, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    });
    
    if (response.data.code !== 200) {
        throw new Error(`API返回错误: ${response.data.msg}`);
    }
    
    const metrics = response.data.data;
    if (!Array.isArray(metrics)) {
        throw new Error('指标选项应该是数组');
    }
    
    // 验证选项结构
    if (metrics.length > 0) {
        const firstOption = metrics[0];
        if (!('value' in firstOption) || !('label' in firstOption)) {
            throw new Error('指标选项缺少value或label字段');
        }
    }
    
    console.log(chalk.blue(`    Found ${metrics.length} metric options`));
}

/**
 * 测试获取维度值下拉框数据
 */
async function testGetDimensionValues() {
    const client = createHttpClient();
    const formData = new URLSearchParams();
    formData.append('businessType', BusinessType.STUDY);
    formData.append('dimensionLevel', DimensionLevel.PROVINCE);
    formData.append('dimensionField', 'province');
    
    const response = await client.post('/dashboard/dimension-values', formData, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    });
    
    if (response.data.code !== 200) {
        throw new Error(`API返回错误: ${response.data.msg}`);
    }
    
    const values = response.data.data;
    if (!Array.isArray(values)) {
        throw new Error('维度值选项应该是数组');
    }
    
    console.log(chalk.blue(`    Found ${values.length} dimension values`));
}

// ==================== 异常场景测试 ====================

/**
 * 测试无效的业务类型
 */
async function testInvalidBusinessType() {
    const client = createHttpClient();
    const params = {
        businessType: 'INVALID_TYPE',
        dimensionLevel: DimensionLevel.PROVINCE,
        metricName: StudyMetrics.QUIZ_AVG_SCORE,
        startDate: '2024-01-01',
        endDate: '2024-01-31'
    };
    
    try {
        await client.get('/api/analysis/trend', { params });
        throw new Error('应该返回错误，但请求成功了');
    } catch (error) {
        if (error.response && error.response.status >= 400) {
            // 预期的错误
            console.log(chalk.blue(`    Expected error: ${error.response.status}`));
        } else {
            throw error;
        }
    }
}

/**
 * 测试无效的日期格式
 */
async function testInvalidDateFormat() {
    const client = createHttpClient();
    const params = {
        businessType: BusinessType.STUDY,
        dimensionLevel: DimensionLevel.PROVINCE,
        metricName: StudyMetrics.QUIZ_AVG_SCORE,
        startDate: '2024/01/01',  // 错误格式
        endDate: '2024-01-31'
    };
    
    try {
        await client.get('/api/analysis/trend', { params });
        throw new Error('应该返回错误，但请求成功了');
    } catch (error) {
        if (error.response && error.response.status >= 400) {
            // 预期的错误
            console.log(chalk.blue(`    Expected error: ${error.response.status}`));
        } else {
            throw error;
        }
    }
}

/**
 * 测试缺少必填参数
 */
async function testMissingRequiredParams() {
    const client = createHttpClient();
    const params = {
        businessType: BusinessType.STUDY,
        // 缺少 dimensionLevel
        metricName: StudyMetrics.QUIZ_AVG_SCORE,
        startDate: '2024-01-01',
        endDate: '2024-01-31'
    };
    
    try {
        await client.get('/api/analysis/trend', { params });
        throw new Error('应该返回错误，但请求成功了');
    } catch (error) {
        if (error.response && error.response.status >= 400) {
            // 预期的错误
            console.log(chalk.blue(`    Expected error: ${error.response.status}`));
        } else {
            throw error;
        }
    }
}

// ==================== 主测试函数 ====================

/**
 * 运行所有测试
 */
async function runAllTests() {
    console.log(chalk.cyan('🚀 开始运行API测试套件...\n'));
    TestStats.startTime = Date.now();
    
    // 配置信息接口测试
    console.log(chalk.yellow('📋 配置信息接口测试'));
    await executeTest('获取业务类型列表', testGetBusinessTypes);
    await executeTest('获取维度级别列表', testGetDimensionLevels);
    await executeTest('获取分析类型列表', testGetAnalysisTypes);
    await executeTest('获取学习数据指标', testGetBusinessTypeMetrics);
    await executeTest('获取配置概览', testGetConfigOverview);
    
    console.log('');
    
    // 数据分析接口测试
    console.log(chalk.yellow('📊 数据分析接口测试'));
    await executeTest('趋势分析', testTrendAnalysis);
    await executeTest('排行分析', testRankingAnalysis);
    await executeTest('对比分析', testComparisonAnalysis);
    await executeTest('汇总统计', testSummaryAnalysis);
    
    console.log('');
    
    // Web页面数据接口测试
    console.log(chalk.yellow('🌐 Web页面数据接口测试'));
    await executeTest('获取指标下拉框数据', testGetMetricsOptions);
    await executeTest('获取维度值下拉框数据', testGetDimensionValues);
    
    console.log('');
    
    // 异常场景测试
    console.log(chalk.yellow('⚠️  异常场景测试'));
    await executeTest('无效业务类型', testInvalidBusinessType);
    await executeTest('无效日期格式', testInvalidDateFormat);
    await executeTest('缺少必填参数', testMissingRequiredParams);
    
    TestStats.endTime = Date.now();
    
    // 输出测试结果
    printTestSummary();
}

/**
 * 打印测试摘要
 */
function printTestSummary() {
    console.log('\n' + chalk.cyan('📈 测试结果摘要'));
    console.log('='.repeat(50));
    
    const totalTime = TestStats.endTime - TestStats.startTime;
    const avgResponseTime = TestStats.responseTimes.reduce((a, b) => a + b, 0) / TestStats.responseTimes.length;
    const maxResponseTime = Math.max(...TestStats.responseTimes);
    const minResponseTime = Math.min(...TestStats.responseTimes);
    
    console.log(chalk.white(`总测试数量: ${TestStats.total}`));
    console.log(chalk.green(`通过测试: ${TestStats.passed}`));
    console.log(chalk.red(`失败测试: ${TestStats.failed}`));
    console.log(chalk.blue(`成功率: ${((TestStats.passed / TestStats.total) * 100).toFixed(2)}%`));
    
    console.log('\n' + chalk.cyan('⏱️  性能统计'));
    console.log('='.repeat(50));
    console.log(chalk.white(`总耗时: ${totalTime}ms`));
    console.log(chalk.white(`平均响应时间: ${avgResponseTime.toFixed(2)}ms`));
    console.log(chalk.white(`最快响应时间: ${minResponseTime}ms`));
    console.log(chalk.white(`最慢响应时间: ${maxResponseTime}ms`));
    
    if (TestStats.errors.length > 0) {
        console.log('\n' + chalk.red('❌ 失败测试详情'));
        console.log('='.repeat(50));
        TestStats.errors.forEach(error => {
            console.log(chalk.red(`${error.testName}: ${error.error}`));
        });
    }
    
    console.log('\n' + chalk.cyan('🎉 测试完成!'));
    
    // 退出码
    process.exit(TestStats.failed > 0 ? 1 : 0);
}

// ==================== 启动测试 ====================

if (require.main === module) {
    runAllTests().catch(error => {
        console.error(chalk.red('测试套件执行失败:'), error);
        process.exit(1);
    });
}

module.exports = {
    runAllTests,
    TestStats,
    CONFIG
};
