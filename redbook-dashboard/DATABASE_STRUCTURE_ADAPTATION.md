# 数据库结构适配说明

## 概述

基于提供的实际数据库表结构，对通用数据分析框架进行了重新设计和优化，以完全匹配真实的学习数据分析表结构。

## 数据库表结构分析

### 表命名规范
```
intelligence_{dimension}_{business}_day
```

### 各维度表结构

#### 1. 全国维度 (intelligence_country_study_module_day)
```sql
-- 基础字段
date_key, year, month, week_of_year, study_date
-- 业务维度
content_type, study_module
-- 学习指标
quiz_avg_score, improve_score_rate, improve_score_mean, ...
```

#### 2. 省份维度 (intelligence_province_study_module_day)
```sql
-- 基础字段 + 省份维度
province_id, province
-- 其他字段同全国维度
```

#### 3. 城市维度 (intelligence_city_study_module_day)
```sql
-- 基础字段 + 地理维度
province_id, city_id, city
-- 其他字段同全国维度
```

#### 4. 体验中心维度 (intelligence_alias_study_module_day)
```sql
-- 基础字段 + 地理维度 + 机构维度
province_id, city_id, alias_id, alias
-- 其他字段同全国维度
```

#### 5. 专卖店维度 (intelligence_shop_study_module_day)
```sql
-- 基础字段 + 地理维度 + 机构维度
province_id, city_id, alias_id, exclusive_shop_id, shop_name
-- 其他字段同全国维度
```

#### 6. 班级维度 (intelligence_class_study_module_day)
```sql
-- 基础字段 + 地理维度 + 机构维度
province_id, city_id, alias_id, exclusive_shop_id, class_id, class_name
-- 其他字段同全国维度
```

#### 7. 用户维度 (intelligence_user_study_module_day)
```sql
-- 最完整的字段集合，包含所有维度信息
user_id, area_id, province_id, city_id, county_id, alias_id, 
exclusive_shop_id, code, manager_id, class_id, person_name,
province, city, county, alias, shop_name, user_name, teacher_name,
stage_cn, class_name, class_concat_ws, content_type, study_module
-- 学习指标字段同其他维度
```

## 框架适配方案

### 1. 实体类更新 (AnalysisDataEntity)

#### 移除通用字段
```java
// 移除了这些通用字段
private String dimensionId1;
private String dimensionId2; 
private String dimensionId3;
private String dimensionName1;
private String dimensionName2;
private String dimensionName3;
```

#### 添加实际字段
```java
// 用户维度
private String userId;
private String userName;

// 地理维度
private Integer provinceId;
private String province;
private Integer cityId;
private String city;
private Integer countyId;
private String county;

// 机构维度
private Integer aliasId;
private String alias;
private Integer exclusiveShopId;
private String shopName;
private Integer classId;
private String className;

// 用户专用字段
private Integer areaId;
private String code;
private Integer managerId;
private String personName;
private String classConcatWs;
```

### 2. 维度级别枚举更新 (DimensionLevelEnum)

#### 字段映射策略
```java
// 每个维度级别定义主要的ID和名称字段
USER("user", "用户维度", "user_id", "user_name"),
COUNTRY("country", "全国维度", null, null),
PROVINCE("province", "省份维度", "province_id", "province"),
CITY("city", "城市维度", "city_id", "city"),
ALIAS("alias", "体验中心维度", "alias_id", "alias"),
SHOP("shop", "专卖店维度", "exclusive_shop_id", "shop_name"),
CLASS("class", "班级维度", "class_id", "class_name");
```

#### 新增实用方法
```java
// 获取排序字段名
public String getOrderByField()

// 获取分组字段名  
public String getGroupByField()

// 获取WHERE条件字段名
public String getWhereField()

// 判断是否需要包含父级维度字段
public boolean needsParentFields()
```

### 3. Mapper层更新

#### XML结果映射
```xml
<!-- 完全基于实际数据库字段的映射 -->
<result property="userId" column="user_id"/>
<result property="userName" column="user_name"/>
<result property="provinceId" column="province_id"/>
<result property="province" column="province"/>
<!-- ... 其他实际字段映射 -->
```

#### 新增查询方法
```java
// 基于实际字段的趋势分析
List<Map<String, Object>> selectTrendDataByActualFields(...)

// 基于实际字段的排行分析  
List<Map<String, Object>> selectRankingDataByActualFields(...)

// 基于实际字段的汇总统计
Double selectSummaryDataByActualFields(...)
```

### 4. 服务层更新

#### 字段名映射
```java
// 前端字段名到数据库字段名的映射
private String mapToDbFieldName(String frontendFieldName) {
    switch (frontendFieldName) {
        case "provinceId": return "province_id";
        case "cityId": return "city_id";
        case "aliasId": return "alias_id";
        // ... 其他映射规则
    }
}
```

#### 过滤条件构建
```java
// 构建数据库过滤条件
private Map<String, Object> buildDatabaseFilters(
    DimensionLevelEnum dimensionLevel, 
    Map<String, Object> filters)
```

## 查询示例

### 1. 趋势分析查询
```sql
SELECT 
    study_date as date,
    AVG(quiz_avg_score) as value,
    CONCAT(ROUND(AVG(quiz_avg_score), 2), '') as formattedValue
FROM intelligence_province_study_module_day
WHERE study_date BETWEEN '2024-01-01' AND '2024-01-31'
  AND province_id = 110000
GROUP BY study_date
ORDER BY study_date ASC
```

### 2. 排行分析查询
```sql
SELECT 
    ROW_NUMBER() OVER (ORDER BY AVG(study_efficiency_mean) DESC) as rank,
    city as name,
    city_id as dimensionId,
    AVG(study_efficiency_mean) as value,
    CONCAT(ROUND(AVG(study_efficiency_mean), 2), '') as formattedValue
FROM intelligence_city_study_module_day
WHERE study_date BETWEEN '2024-01-01' AND '2024-01-31'
  AND province_id = 110000
GROUP BY city_id, city
ORDER BY AVG(study_efficiency_mean) DESC
LIMIT 10
```

### 3. 汇总统计查询
```sql
SELECT AVG(study_effect_time_day) 
FROM intelligence_country_study_module_day
WHERE study_date BETWEEN '2024-01-01' AND '2024-01-31'
```

## API调用示例

### 1. 省份维度学习效率趋势
```javascript
const params = {
    businessType: 'STUDY',
    dimensionLevel: 'PROVINCE', 
    metricName: 'studyEfficiencyMean',
    startDate: '2024-01-01',
    endDate: '2024-01-31'
};
```

### 2. 城市维度测验成绩排行
```javascript
const params = {
    businessType: 'STUDY',
    dimensionLevel: 'CITY',
    rankBy: 'quizAvgScore',
    startDate: '2024-01-01', 
    endDate: '2024-01-31',
    limit: 10,
    provinceId: '110000'  // 过滤条件
};
```

## 优势

### 1. 完全匹配数据库结构
- 直接使用实际的数据库字段名
- 避免了复杂的字段映射逻辑
- 提高了查询性能

### 2. 类型安全
- 明确的字段类型定义
- 编译时错误检查
- IDE智能提示支持

### 3. 维护性好
- 字段映射关系清晰
- 易于理解和维护
- 便于扩展新的维度

### 4. 性能优化
- 减少了运行时字段映射开销
- 直接的SQL查询
- 更好的数据库索引利用

## 注意事项

### 1. 字段命名规范
- 数据库字段使用下划线命名 (snake_case)
- Java字段使用驼峰命名 (camelCase)
- 需要在Mapper XML中正确映射

### 2. 过滤条件处理
- 前端传入的字段名需要映射为数据库字段名
- 空值和空字符串需要过滤
- 注意SQL注入防护

### 3. 维度层级关系
- 高层级维度包含低层级维度的字段
- 查询时需要考虑维度的层级关系
- 过滤条件要与维度级别匹配

### 4. 扩展性考虑
- 新增业务类型时需要创建对应的表
- 新增维度级别时需要更新枚举和映射
- 保持字段命名的一致性

## 测试验证

使用提供的API测试脚本验证：
1. 配置信息接口正常返回
2. 各维度的趋势分析正确
3. 排行分析结果准确
4. 汇总统计数据正确
5. 过滤条件生效

现在框架已完全适配实际的数据库结构，可以直接对接真实的学习数据进行分析！
