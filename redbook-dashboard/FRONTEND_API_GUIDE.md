# 前端API调用指南

## 概述

本文档为前端开发者提供通用数据分析框架的API调用指南，包括接口地址、参数说明、枚举值定义和调用示例。

## 基础配置

### 接口基础路径
```
配置接口: /api/analysis/config/
分析接口: /api/analysis/
Web页面接口: /dashboard/
```

## 枚举值定义

### 1. 业务类型 (BusinessType)
```javascript
const BUSINESS_TYPES = {
    STUDY: {
        code: 'STUDY',
        name: '学习数据',
        description: '学习时长、成绩、效率等指标分析'
    },
    RENEWAL: {
        code: 'RENEWAL', 
        name: '续费数据',
        description: '续费率、续费金额等指标分析（预留）'
    },
    TASK: {
        code: 'TASK',
        name: '任务数据', 
        description: '任务完成率、任务得分等指标分析（预留）'
    },
    MARKETING: {
        code: 'MARKETING',
        name: '营销数据',
        description: '转化率、点击率等指标分析（预留）'
    }
};
```

### 2. 维度级别 (DimensionLevel)
```javascript
const DIMENSION_LEVELS = {
    USER: {
        code: 'USER',
        name: '用户维度',
        description: '按用户个体统计',
        hierarchyLevel: 6
    },
    COUNTRY: {
        code: 'COUNTRY',
        name: '全国维度', 
        description: '全国汇总数据',
        hierarchyLevel: 0
    },
    PROVINCE: {
        code: 'PROVINCE',
        name: '省份维度',
        description: '按省份统计',
        hierarchyLevel: 1
    },
    CITY: {
        code: 'CITY',
        name: '城市维度',
        description: '按城市统计', 
        hierarchyLevel: 2
    },
    ALIAS: {
        code: 'ALIAS',
        name: '体验中心维度',
        description: '按体验中心统计',
        hierarchyLevel: 3
    },
    SHOP: {
        code: 'SHOP',
        name: '专卖店维度',
        description: '按专卖店统计',
        hierarchyLevel: 4
    },
    CLASS: {
        code: 'CLASS',
        name: '班级维度',
        description: '按班级统计',
        hierarchyLevel: 5
    }
};
```

### 3. 分析类型 (AnalysisType)
```javascript
const ANALYSIS_TYPES = {
    LIST: {
        code: 'LIST',
        name: '列表查询',
        description: '基础数据列表展示'
    },
    TREND: {
        code: 'TREND',
        name: '趋势分析',
        description: '时间序列数据的趋势变化分析'
    },
    RANKING: {
        code: 'RANKING', 
        name: '排行分析',
        description: '按指定指标进行排序的排行榜分析'
    },
    COMPARISON: {
        code: 'COMPARISON',
        name: '对比分析',
        description: '不同时间段或不同维度的数据对比分析'
    },
    SUMMARY: {
        code: 'SUMMARY',
        name: '汇总统计',
        description: '数据的汇总统计信息'
    }
};
```

### 4. 学习数据指标 (StudyMetrics)
```javascript
const STUDY_METRICS = {
    quizAvgScore: {
        code: 'quizAvgScore',
        name: '测验平均分',
        unit: '分',
        dataType: 'INTEGER'
    },
    improveScoreRate: {
        code: 'improveScoreRate',
        name: '提分率',
        unit: '%', 
        dataType: 'PERCENTAGE'
    },
    improveScoreMean: {
        code: 'improveScoreMean',
        name: '平均提分',
        unit: '分',
        dataType: 'INTEGER'
    },
    studyEfficiencyMean: {
        code: 'studyEfficiencyMean',
        name: '学习效率均值',
        unit: '%',
        dataType: 'PERCENTAGE'
    },
    studyEffectTimeDay: {
        code: 'studyEffectTimeDay',
        name: '日有效学习时长',
        unit: '分钟',
        dataType: 'INTEGER'
    },
    studyNewNumDay: {
        code: 'studyNewNumDay',
        name: '日新学数量',
        unit: '个',
        dataType: 'INTEGER'
    },
    studySpeed: {
        code: 'studySpeed',
        name: '学习速度',
        unit: '个/分钟',
        dataType: 'INTEGER'
    },
    reviewFrequencyDay: {
        code: 'reviewFrequencyDay',
        name: '日复习频次',
        unit: '次',
        dataType: 'INTEGER'
    },
    reviewNumDay: {
        code: 'reviewNumDay',
        name: '日复习数量',
        unit: '个',
        dataType: 'INTEGER'
    },
    reviewMultiplierMean: {
        code: 'reviewMultiplierMean',
        name: '复习倍数均值',
        unit: '倍',
        dataType: 'DECIMAL'
    }
};
```

## API接口调用

### 1. 获取配置信息

#### 1.1 获取所有业务类型
```javascript
// GET /api/analysis/config/business-types
fetch('/api/analysis/config/business-types')
    .then(response => response.json())
    .then(data => {
        console.log('业务类型列表:', data);
        // 返回格式: 
        // [
        //   {
        //     code: "STUDY",
        //     description: "学习数据", 
        //     tableNamePattern: "intelligence_{level}_study_module_day",
        //     handlerClass: "StudyMetricsHandler",
        //     defaultMetrics: ["quizAvgScore", "improveScoreRate", ...]
        //   }
        // ]
    });
```

#### 1.2 获取所有维度级别
```javascript
// GET /api/analysis/config/dimension-levels
fetch('/api/analysis/config/dimension-levels')
    .then(response => response.json())
    .then(data => {
        console.log('维度级别列表:', data);
        // 返回格式:
        // [
        //   {
        //     code: "PROVINCE",
        //     level: "province",
        //     description: "省份维度",
        //     hierarchyLevel: 1,
        //     isGeographic: true,
        //     isOrganization: false,
        //     isUser: false
        //   }
        // ]
    });
```

#### 1.3 获取指定业务类型的指标
```javascript
// GET /api/analysis/config/business-types/{businessType}/metrics
const businessType = 'STUDY';
fetch(`/api/analysis/config/business-types/${businessType}/metrics`)
    .then(response => response.json())
    .then(data => {
        console.log('支持的指标:', data);
        // 返回格式: ["quizAvgScore", "improveScoreRate", "studyEfficiencyMean", ...]
    });
```

### 2. 数据分析接口

#### 2.1 趋势分析
```javascript
// GET /api/analysis/trend
const trendParams = {
    businessType: 'STUDY',           // 必填: 业务类型枚举
    dimensionLevel: 'PROVINCE',      // 必填: 维度级别枚举  
    metricName: 'quizAvgScore',      // 必填: 指标名称
    startDate: '2024-01-01',         // 必填: 开始日期 YYYY-MM-DD
    endDate: '2024-12-31',           // 必填: 结束日期 YYYY-MM-DD
    provinceId: '110000',            // 可选: 省份ID过滤
    cityId: '110100',                // 可选: 城市ID过滤
    aliasId: '1001'                  // 可选: 体验中心ID过滤
};

const queryString = new URLSearchParams(trendParams).toString();
fetch(`/api/analysis/trend?${queryString}`)
    .then(response => response.json())
    .then(data => {
        console.log('趋势分析结果:', data);
        // 返回格式:
        // [
        //   {
        //     date: "2024-01-01",
        //     value: 85.5,
        //     formattedValue: "85.5分",
        //     trend: "UP",
        //     changeValue: 2.5,
        //     changeRate: 3.2,
        //     formattedChangeValue: "+2.5分",
        //     formattedChangeRate: "+3.2%"
        //   }
        // ]
    });
```

#### 2.2 排行分析
```javascript
// GET /api/analysis/ranking
const rankingParams = {
    businessType: 'STUDY',           // 必填: 业务类型枚举
    dimensionLevel: 'CITY',          // 必填: 维度级别枚举
    rankBy: 'studyEfficiencyMean',   // 必填: 排序指标名称
    startDate: '2024-01-01',         // 必填: 开始日期
    endDate: '2024-12-31',           // 必填: 结束日期
    limit: 10,                       // 可选: 返回数量限制，默认10
    provinceId: '110000'             // 可选: 省份ID过滤
};

const queryString = new URLSearchParams(rankingParams).toString();
fetch(`/api/analysis/ranking?${queryString}`)
    .then(response => response.json())
    .then(data => {
        console.log('排行分析结果:', data);
        // 返回格式:
        // [
        //   {
        //     rank: 1,
        //     name: "北京市",
        //     value: 95.5,
        //     formattedValue: "95.5%",
        //     metricName: "studyEfficiencyMean",
        //     metricDisplayName: "学习效率均值",
        //     rankTrend: "UP",
        //     valueChangeRate: 3.2
        //   }
        // ]
    });
```

#### 2.3 对比分析
```javascript
// GET /api/analysis/comparison
const comparisonParams = {
    businessType: 'STUDY',           // 必填: 业务类型枚举
    dimensionLevel: 'PROVINCE',      // 必填: 维度级别枚举
    metricName: 'quizAvgScore',      // 必填: 对比指标名称
    currentStartDate: '2024-12-01',  // 必填: 当前期开始日期
    currentEndDate: '2024-12-31',    // 必填: 当前期结束日期
    previousStartDate: '2024-11-01', // 必填: 对比期开始日期
    previousEndDate: '2024-11-30',   // 必填: 对比期结束日期
    provinceId: '110000'             // 可选: 省份ID过滤
};

const queryString = new URLSearchParams(comparisonParams).toString();
fetch(`/api/analysis/comparison?${queryString}`)
    .then(response => response.json())
    .then(data => {
        console.log('对比分析结果:', data);
        // 返回格式:
        // {
        //   currentValue: 95.5,
        //   previousValue: 92.3,
        //   change: 3.2,
        //   changeRate: 3.47,
        //   currentFormattedValue: "95.5分",
        //   previousFormattedValue: "92.3分", 
        //   changeFormattedValue: "+3.2分",
        //   changeRateFormattedValue: "+3.47%",
        //   trend: "UP",
        //   significance: "MODERATE",
        //   currentPeriod: "2024-12-01 至 2024-12-31",
        //   previousPeriod: "2024-11-01 至 2024-11-30"
        // }
    });
```

#### 2.4 汇总统计
```javascript
// GET /api/analysis/summary
const summaryParams = {
    businessType: 'STUDY',           // 必填: 业务类型枚举
    dimensionLevel: 'COUNTRY',       // 必填: 维度级别枚举
    metricName: 'studyEffectTimeDay', // 必填: 指标名称
    aggregationType: 'AVG',          // 必填: 汇总类型 SUM/AVG/MAX/MIN/COUNT
    startDate: '2024-01-01',         // 必填: 开始日期
    endDate: '2024-12-31'            // 必填: 结束日期
};

const queryString = new URLSearchParams(summaryParams).toString();
fetch(`/api/analysis/summary?${queryString}`)
    .then(response => response.json())
    .then(data => {
        console.log('汇总统计结果:', data);
        // 返回格式: 数值类型，如 120.5
    });
```

### 3. Web页面接口 (用于页面数据组装)

#### 3.1 获取指标列表 (下拉框数据)
```javascript
// POST /dashboard/metrics
const formData = new FormData();
formData.append('businessType', 'STUDY');

fetch('/dashboard/metrics', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(result => {
    if (result.code === 200) {
        console.log('指标列表:', result.data);
        // 返回格式:
        // [
        //   {
        //     value: "quizAvgScore",
        //     label: "测验平均分"
        //   }
        // ]
    }
});
```

#### 3.2 获取维度值列表 (如省份列表)
```javascript
// POST /dashboard/dimension-values
const formData = new FormData();
formData.append('businessType', 'STUDY');
formData.append('dimensionLevel', 'PROVINCE');
formData.append('dimensionField', 'province');

fetch('/dashboard/dimension-values', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(result => {
    if (result.code === 200) {
        console.log('省份列表:', result.data);
        // 返回格式:
        // [
        //   {
        //     value: "北京市",
        //     label: "北京市"
        //   }
        // ]
    }
});
```

## 前端组件示例

### Vue.js 组件示例
```vue
<template>
  <div class="analysis-dashboard">
    <!-- 业务类型选择 -->
    <el-select v-model="selectedBusinessType" @change="onBusinessTypeChange">
      <el-option 
        v-for="item in businessTypes" 
        :key="item.code" 
        :label="item.description" 
        :value="item.code">
      </el-option>
    </el-select>
    
    <!-- 维度级别选择 -->
    <el-select v-model="selectedDimensionLevel">
      <el-option 
        v-for="item in dimensionLevels" 
        :key="item.code" 
        :label="item.description" 
        :value="item.code">
      </el-option>
    </el-select>
    
    <!-- 指标选择 -->
    <el-select v-model="selectedMetric">
      <el-option 
        v-for="item in metrics" 
        :key="item.value" 
        :label="item.label" 
        :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedBusinessType: '',
      selectedDimensionLevel: '',
      selectedMetric: '',
      businessTypes: [],
      dimensionLevels: [],
      metrics: []
    };
  },
  
  mounted() {
    this.loadBusinessTypes();
    this.loadDimensionLevels();
  },
  
  methods: {
    async loadBusinessTypes() {
      const response = await fetch('/api/analysis/config/business-types');
      this.businessTypes = await response.json();
    },
    
    async loadDimensionLevels() {
      const response = await fetch('/api/analysis/config/dimension-levels');
      this.dimensionLevels = await response.json();
    },
    
    async onBusinessTypeChange(businessType) {
      if (businessType) {
        const formData = new FormData();
        formData.append('businessType', businessType);
        
        const response = await fetch('/dashboard/metrics', {
          method: 'POST',
          body: formData
        });
        
        const result = await response.json();
        if (result.code === 200) {
          this.metrics = result.data;
        }
      }
    }
  }
};
</script>
```

## 错误处理

### 常见错误码
```javascript
const ERROR_CODES = {
    400: '请求参数错误',
    404: '接口不存在', 
    500: '服务器内部错误',
    // 业务错误码
    'INVALID_BUSINESS_TYPE': '无效的业务类型',
    'INVALID_DIMENSION_LEVEL': '无效的维度级别',
    'INVALID_METRIC_NAME': '无效的指标名称',
    'UNSUPPORTED_OPERATION': '不支持的操作'
};
```

### 错误处理示例
```javascript
async function callAnalysisAPI(url, params) {
    try {
        const response = await fetch(url + '?' + new URLSearchParams(params));
        const result = await response.json();
        
        if (response.ok && result.code === 200) {
            return result.data;
        } else {
            throw new Error(result.msg || '请求失败');
        }
    } catch (error) {
        console.error('API调用失败:', error);
        // 显示用户友好的错误信息
        showErrorMessage(error.message);
        throw error;
    }
}
```

## 注意事项

1. **日期格式**: 所有日期参数使用 `YYYY-MM-DD` 格式
2. **枚举值**: 所有枚举参数必须使用预定义的枚举值，区分大小写
3. **必填参数**: 标记为必填的参数不能为空
4. **数据权限**: 根据用户权限可能返回不同的数据范围
5. **性能考虑**: 大数据量查询建议使用分页或限制时间范围
6. **缓存策略**: 配置信息可以缓存，分析数据建议实时查询

## 联系方式

如有问题请联系后端开发团队或查看完整的API文档。
