<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.dashboard.mapper.AnalysisDataMapper">

    <!-- 通用结果映射 -->
    <resultMap id="AnalysisDataResult" type="com.redbook.dashboard.domain.entity.AnalysisDataEntity">
        <id property="id" column="id"/>
        <result property="businessType" column="business_type"/>
        <result property="dimensionLevel" column="dimension_level"/>
        <result property="dateKey" column="date_key"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="weekOfYear" column="week_of_year"/>
        <result property="studyDate" column="study_date"/>
        
        <!-- 通用维度字段 -->
        <result property="dimensionId1" column="dimension_id1"/>
        <result property="dimensionId2" column="dimension_id2"/>
        <result property="dimensionId3" column="dimension_id3"/>
        <result property="dimensionName1" column="dimension_name1"/>
        <result property="dimensionName2" column="dimension_name2"/>
        <result property="dimensionName3" column="dimension_name3"/>
        
        <!-- 学习数据专用字段 -->
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="teacherName" column="teacher_name"/>
        <result property="stageCn" column="stage_cn"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="aliasId" column="alias_id"/>
        <result property="alias" column="alias"/>
        <result property="exclusiveShopId" column="exclusive_shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
        
        <!-- 学习指标字段 -->
        <result property="contentType" column="content_type"/>
        <result property="studyModule" column="study_module"/>
        <result property="quizAvgScore" column="quiz_avg_score"/>
        <result property="improveScoreRate" column="improve_score_rate"/>
        <result property="improveScoreMean" column="improve_score_mean"/>
        <result property="studyEfficiencyMean" column="study_efficiency_mean"/>
        <result property="studyEffectTimeDay" column="study_effect_time_day"/>
        <result property="studyNewNumDay" column="study_new_num_day"/>
        <result property="studySpeed" column="study_speed"/>
        <result property="reviewFrequencyDay" column="review_frequency_day"/>
        <result property="reviewNumDay" column="review_num_day"/>
        <result property="reviewMultiplierMean" column="review_multiplier_mean"/>
        
        <!-- 扩展字段 -->
        <result property="metricsData" column="metrics_data"/>
        <result property="extendData" column="extend_data"/>
        <result property="dataVersion" column="data_version"/>
        
        <!-- 基础字段 -->
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 动态表名查询示例 -->
    <select id="selectListWithTableName" resultMap="AnalysisDataResult">
        SELECT * FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <!-- 动态表名分页查询示例 -->
    <select id="selectPageWithTableName" resultMap="AnalysisDataResult">
        SELECT * FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <!-- 动态表名统计查询示例 -->
    <select id="selectCountWithTableName" resultType="java.lang.Long">
        SELECT COUNT(*) FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <!-- 查询不重复值示例 -->
    <select id="selectDistinctValues" resultType="java.lang.String">
        SELECT DISTINCT ${fieldName} FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
        ORDER BY ${fieldName}
    </select>

    <!-- 汇总查询示例 -->
    <select id="selectAggregation" resultType="java.lang.Object">
        SELECT ${aggregationType}(${fieldName}) FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <!-- 时间序列数据查询示例 -->
    <select id="selectTimeSeriesData" resultType="com.redbook.dashboard.mapper.AnalysisDataMapper$TimeSeriesData">
        SELECT 
            study_date as studyDate,
            ${aggregationType}(${fieldName}) as metricValue
        FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY study_date
        ORDER BY study_date
    </select>

    <!-- 排行数据查询示例 -->
    <select id="selectRankingData" resultType="com.redbook.dashboard.mapper.AnalysisDataMapper$RankingData">
        SELECT 
            ${dimensionField} as dimensionName,
            ${aggregationType}(${metricField}) as metricValue
        FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY ${dimensionField}
        ORDER BY metricValue DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

</mapper>
