<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.dashboard.mapper.AnalysisDataMapper">

    <!-- 通用结果映射 -->
    <resultMap id="AnalysisDataResult" type="com.redbook.dashboard.domain.entity.AnalysisDataEntity">
        <result property="dateKey" column="date_key"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="weekOfYear" column="week_of_year"/>
        <result property="studyDate" column="study_date"/>

        <!-- 实际维度字段 -->
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="provinceId" column="province_id"/>
        <result property="province" column="province"/>
        <result property="cityId" column="city_id"/>
        <result property="city" column="city"/>
        <result property="countyId" column="county_id"/>
        <result property="county" column="county"/>
        <result property="aliasId" column="alias_id"/>
        <result property="alias" column="alias"/>
        <result property="exclusiveShopId" column="exclusive_shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
        <result property="areaId" column="area_id"/>
        <result property="code" column="code"/>
        <result property="managerId" column="manager_id"/>
        <result property="personName" column="person_name"/>
        <result property="classConcatWs" column="class_concat_ws"/>
        
        <!-- 学习数据专用字段 -->
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="teacherName" column="teacher_name"/>
        <result property="stageCn" column="stage_cn"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="aliasId" column="alias_id"/>
        <result property="alias" column="alias"/>
        <result property="exclusiveShopId" column="exclusive_shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
        
        <!-- 学习指标字段 -->
        <result property="contentType" column="content_type"/>
        <result property="studyModule" column="study_module"/>
        <result property="quizAvgScore" column="quiz_avg_score"/>
        <result property="improveScoreRate" column="improve_score_rate"/>
        <result property="improveScoreMean" column="improve_score_mean"/>
        <result property="studyEfficiencyMean" column="study_efficiency_mean"/>
        <result property="studyEffectTimeDay" column="study_effect_time_day"/>
        <result property="studyNewNumDay" column="study_new_num_day"/>
        <result property="studySpeed" column="study_speed"/>
        <result property="reviewFrequencyDay" column="review_frequency_day"/>
        <result property="reviewNumDay" column="review_num_day"/>
        <result property="reviewMultiplierMean" column="review_multiplier_mean"/>
        
        <!-- 扩展字段 -->
        <result property="metricsData" column="metrics_data"/>
        <result property="extendData" column="extend_data"/>
        <result property="dataVersion" column="data_version"/>
        
        <!-- 基础字段 -->
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 趋势分析查询 -->
    <select id="selectTrendData" resultType="com.redbook.dashboard.domain.vo.TrendPoint">
        SELECT
            study_date as date,
            ${metricField} as value,
            CONCAT(${metricField}, '${unit}') as formattedValue
        FROM ${tableName}
        <where>
            <if test="startDate != null and endDate != null">
                study_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="filters != null">
                <foreach collection="filters" index="key" item="value">
                    AND ${key} = #{value}
                </foreach>
            </if>
        </where>
        ORDER BY study_date ASC
    </select>

    <!-- 排行分析查询 -->
    <select id="selectRankingData" resultType="com.redbook.dashboard.domain.vo.RankingItem">
        SELECT
            ROW_NUMBER() OVER (ORDER BY ${metricField} DESC) as rank,
            ${dimensionNameField} as name,
            ${dimensionIdField} as dimensionId,
            ${metricField} as value,
            CONCAT(${metricField}, '${unit}') as formattedValue
        FROM ${tableName}
        <where>
            <if test="startDate != null and endDate != null">
                study_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="filters != null">
                <foreach collection="filters" index="key" item="value">
                    AND ${key} = #{value}
                </foreach>
            </if>
        </where>
        <if test="dimensionNameField != null">
            GROUP BY ${dimensionIdField}, ${dimensionNameField}
        </if>
        ORDER BY ${metricField} DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 汇总统计查询 -->
    <select id="selectSummaryData" resultType="java.lang.Double">
        SELECT ${aggregationType}(${metricField})
        FROM ${tableName}
        <where>
            <if test="startDate != null and endDate != null">
                study_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="filters != null">
                <foreach collection="filters" index="key" item="value">
                    AND ${key} = #{value}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询不重复值示例 -->
    <select id="selectDistinctValues" resultType="java.lang.String">
        SELECT DISTINCT ${fieldName} FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
        ORDER BY ${fieldName}
    </select>

    <!-- 汇总查询示例 -->
    <select id="selectAggregation" resultType="java.lang.Object">
        SELECT ${aggregationType}(${fieldName}) FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <!-- 时间序列数据查询示例 -->
    <select id="selectTimeSeriesData" resultType="com.redbook.dashboard.mapper.AnalysisDataMapper$TimeSeriesData">
        SELECT 
            study_date as studyDate,
            ${aggregationType}(${fieldName}) as metricValue
        FROM ${tableName}
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY study_date
        ORDER BY study_date
    </select>

    <!-- 基于实际字段的趋势分析查询 -->
    <select id="selectTrendDataByActualFields" resultType="java.util.Map">
        SELECT
            study_date as date,
            AVG(${metricField}) as value,
            CONCAT(ROUND(AVG(${metricField}), 2), '') as formattedValue
        FROM ${tableName}
        <where>
            <if test="startDate != null and endDate != null">
                study_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="filters != null">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
        GROUP BY study_date
        ORDER BY study_date ASC
    </select>

    <!-- 基于实际字段的排行分析查询 -->
    <select id="selectRankingDataByActualFields" resultType="java.util.Map">
        SELECT
            ROW_NUMBER() OVER (ORDER BY AVG(${metricField}) DESC) as rank,
            <choose>
                <when test="dimensionNameField != null">
                    ${dimensionNameField} as name,
                </when>
                <otherwise>
                    CAST(${dimensionIdField} AS CHAR) as name,
                </otherwise>
            </choose>
            CAST(${dimensionIdField} AS CHAR) as dimensionId,
            AVG(${metricField}) as value,
            CONCAT(ROUND(AVG(${metricField}), 2), '') as formattedValue
        FROM ${tableName}
        <where>
            <if test="startDate != null and endDate != null">
                study_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="filters != null">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
            <!-- 过滤空值 -->
            <if test="dimensionIdField != null">
                AND ${dimensionIdField} IS NOT NULL
            </if>
            <if test="dimensionNameField != null">
                AND ${dimensionNameField} IS NOT NULL AND ${dimensionNameField} != ''
            </if>
        </where>
        <choose>
            <when test="dimensionNameField != null">
                GROUP BY ${dimensionIdField}, ${dimensionNameField}
            </when>
            <otherwise>
                GROUP BY ${dimensionIdField}
            </otherwise>
        </choose>
        ORDER BY AVG(${metricField}) DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 基于实际字段的汇总统计查询 -->
    <select id="selectSummaryDataByActualFields" resultType="java.lang.Double">
        SELECT ${aggregationType}(${metricField})
        FROM ${tableName}
        <where>
            <if test="startDate != null and endDate != null">
                study_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="filters != null">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

</mapper>
