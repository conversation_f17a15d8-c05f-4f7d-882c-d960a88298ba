package com.redbook.dashboard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redbook.dashboard.domain.entity.AnalysisDataEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 分析数据Mapper接口
 * 支持动态表名查询，适配多种业务类型和维度级别
 * 使用Dashboard模块独立数据源
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Mapper
@Repository
public interface AnalysisDataMapper extends BaseMapper<AnalysisDataEntity> {
    
    /**
     * 根据动态表名查询数据列表
     * 
     * @param tableName 表名
     * @param queryWrapper 查询条件
     * @return 数据列表
     */
    @Select("SELECT * FROM ${tableName} ${ew.customSqlSegment}")
    List<AnalysisDataEntity> selectListWithTableName(@Param("tableName") String tableName, 
                                                    @Param("ew") QueryWrapper<AnalysisDataEntity> queryWrapper);
    
    /**
     * 根据动态表名分页查询数据
     * 
     * @param tableName 表名
     * @param page 分页对象
     * @param queryWrapper 查询条件
     * @return 分页数据
     */
    @Select("SELECT * FROM ${tableName} ${ew.customSqlSegment}")
    IPage<AnalysisDataEntity> selectPageWithTableName(@Param("tableName") String tableName,
                                                     Page<AnalysisDataEntity> page,
                                                     @Param("ew") QueryWrapper<AnalysisDataEntity> queryWrapper);
    
    /**
     * 根据动态表名统计数据数量
     * 
     * @param tableName 表名
     * @param queryWrapper 查询条件
     * @return 数据数量
     */
    @Select("SELECT COUNT(*) FROM ${tableName} ${ew.customSqlSegment}")
    Long selectCountWithTableName(@Param("tableName") String tableName,
                                 @Param("ew") QueryWrapper<AnalysisDataEntity> queryWrapper);
    
    /**
     * 查询指定字段的不重复值列表
     * 
     * @param tableName 表名
     * @param fieldName 字段名
     * @param queryWrapper 查询条件
     * @return 不重复值列表
     */
    @Select("SELECT DISTINCT ${fieldName} FROM ${tableName} ${ew.customSqlSegment} ORDER BY ${fieldName}")
    List<String> selectDistinctValues(@Param("tableName") String tableName,
                                    @Param("fieldName") String fieldName,
                                    @Param("ew") QueryWrapper<AnalysisDataEntity> queryWrapper);
    
    /**
     * 根据动态表名查询指定字段的汇总统计
     * 
     * @param tableName 表名
     * @param fieldName 字段名
     * @param aggregationType 汇总类型（SUM, AVG, MAX, MIN, COUNT）
     * @param queryWrapper 查询条件
     * @return 汇总结果
     */
    @Select("SELECT ${aggregationType}(${fieldName}) FROM ${tableName} ${ew.customSqlSegment}")
    Object selectAggregation(@Param("tableName") String tableName,
                           @Param("fieldName") String fieldName,
                           @Param("aggregationType") String aggregationType,
                           @Param("ew") QueryWrapper<AnalysisDataEntity> queryWrapper);
    
    /**
     * 根据动态表名查询时间序列数据
     * 用于趋势分析，按日期分组汇总
     * 
     * @param tableName 表名
     * @param fieldName 指标字段名
     * @param aggregationType 汇总类型
     * @param queryWrapper 查询条件
     * @return 时间序列数据
     */
    @Select("SELECT study_date, ${aggregationType}(${fieldName}) as metric_value " +
            "FROM ${tableName} ${ew.customSqlSegment} " +
            "GROUP BY study_date ORDER BY study_date")
    List<TimeSeriesData> selectTimeSeriesData(@Param("tableName") String tableName,
                                            @Param("fieldName") String fieldName,
                                            @Param("aggregationType") String aggregationType,
                                            @Param("ew") QueryWrapper<AnalysisDataEntity> queryWrapper);
    
    /**
     * 根据动态表名查询维度排行数据
     * 用于排行分析，按维度分组汇总并排序
     * 
     * @param tableName 表名
     * @param dimensionField 维度字段名
     * @param metricField 指标字段名
     * @param aggregationType 汇总类型
     * @param queryWrapper 查询条件
     * @param limit 返回数量限制
     * @return 排行数据
     */
    @Select("SELECT ${dimensionField} as dimension_name, ${aggregationType}(${metricField}) as metric_value " +
            "FROM ${tableName} ${ew.customSqlSegment} " +
            "GROUP BY ${dimensionField} " +
            "ORDER BY metric_value DESC " +
            "LIMIT #{limit}")
    List<RankingData> selectRankingData(@Param("tableName") String tableName,
                                      @Param("dimensionField") String dimensionField,
                                      @Param("metricField") String metricField,
                                      @Param("aggregationType") String aggregationType,
                                      @Param("ew") QueryWrapper<AnalysisDataEntity> queryWrapper,
                                      @Param("limit") Integer limit);
    
    /**
     * 时间序列数据结果类
     */
    class TimeSeriesData {
        private String studyDate;
        private Object metricValue;
        
        // getters and setters
        public String getStudyDate() { return studyDate; }
        public void setStudyDate(String studyDate) { this.studyDate = studyDate; }
        public Object getMetricValue() { return metricValue; }
        public void setMetricValue(Object metricValue) { this.metricValue = metricValue; }
    }
    
    /**
     * 排行数据结果类
     */
    class RankingData {
        private String dimensionName;
        private Object metricValue;
        
        // getters and setters
        public String getDimensionName() { return dimensionName; }
        public void setDimensionName(String dimensionName) { this.dimensionName = dimensionName; }
        public Object getMetricValue() { return metricValue; }
        public void setMetricValue(Object metricValue) { this.metricValue = metricValue; }
    }
}
