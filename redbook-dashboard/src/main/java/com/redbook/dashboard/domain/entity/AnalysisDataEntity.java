package com.redbook.dashboard.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.dashboard.domain.enums.BusinessTypeEnum;
import com.redbook.dashboard.domain.enums.DimensionLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 通用分析数据实体
 * 适配所有业务类型和维度的数据分析需求
 * 通过动态表名和字段映射支持多种业务场景
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "#{tableName}")
@ApiModel(value = "AnalysisDataEntity", description = "通用分析数据实体")
public class AnalysisDataEntity extends DashBoardBaseEntity {
    
    /** ==================== 业务标识字段 ==================== */
    
    @ApiModelProperty(value = "业务类型", example = "STUDY", notes = "STUDY-学习数据, RENEWAL-续费数据, TASK-任务数据")
    private String businessType;
    
    @ApiModelProperty(value = "分析维度级别", example = "PROVINCE", notes = "USER-用户, PROVINCE-省份, CITY-城市, ALIAS-体验中心, SHOP-专卖店, CLASS-班级")
    private String dimensionLevel;
    
    /** ==================== 时间维度字段 ==================== */
    
    @ApiModelProperty(value = "日期键", example = "20241225", notes = "格式：YYYYMMDD")
    private Integer dateKey;
    
    @ApiModelProperty(value = "年份", example = "2024")
    private String year;
    
    @ApiModelProperty(value = "月份", example = "12")
    private String month;
    
    @ApiModelProperty(value = "周数", example = "52", notes = "一年中的第几周")
    private String weekOfYear;
    
    @ApiModelProperty(value = "学习日期", example = "2024-12-25")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String studyDate;
    
    /** ==================== 通用维度标识字段 ==================== */
    
    @ApiModelProperty(value = "主维度ID", example = "110000", notes = "根据维度级别动态使用：用户ID、省份ID等")
    private String dimensionId1;
    
    @ApiModelProperty(value = "次维度ID", example = "110100", notes = "根据维度级别动态使用：城市ID、体验中心ID等")
    private String dimensionId2;
    
    @ApiModelProperty(value = "三级维度ID", example = "1001", notes = "根据维度级别动态使用：专卖店ID、班级ID等")
    private String dimensionId3;
    
    /** ==================== 通用维度名称字段 ==================== */
    
    @ApiModelProperty(value = "主维度名称", example = "北京市", notes = "根据维度级别动态使用：用户名、省份名等")
    private String dimensionName1;
    
    @ApiModelProperty(value = "次维度名称", example = "朝阳区", notes = "根据维度级别动态使用：城市名、体验中心名等")
    private String dimensionName2;
    
    @ApiModelProperty(value = "三级维度名称", example = "朝阳专卖店", notes = "根据维度级别动态使用：专卖店名、班级名等")
    private String dimensionName3;
    
    /** ==================== 学习数据专用字段（兼容现有表结构） ==================== */
    
    @ApiModelProperty(value = "用户ID", notes = "学习数据专用字段")
    private String userId;
    
    @ApiModelProperty(value = "用户名", notes = "学习数据专用字段")
    private String userName;
    
    @ApiModelProperty(value = "教师名", notes = "学习数据专用字段")
    private String teacherName;
    
    @ApiModelProperty(value = "学段", notes = "学习数据专用字段")
    private String stageCn;
    
    @ApiModelProperty(value = "省份ID", notes = "地理维度字段")
    private Integer provinceId;
    
    @ApiModelProperty(value = "城市ID", notes = "地理维度字段")
    private Integer cityId;
    
    @ApiModelProperty(value = "省份名称", notes = "地理维度字段")
    private String province;
    
    @ApiModelProperty(value = "城市名称", notes = "地理维度字段")
    private String city;
    
    @ApiModelProperty(value = "体验中心ID", notes = "机构维度字段")
    private Integer aliasId;
    
    @ApiModelProperty(value = "体验中心名称", notes = "机构维度字段")
    private String alias;
    
    @ApiModelProperty(value = "专卖店ID", notes = "机构维度字段")
    private Integer exclusiveShopId;
    
    @ApiModelProperty(value = "专卖店名称", notes = "机构维度字段")
    private String shopName;
    
    @ApiModelProperty(value = "班级ID", notes = "机构维度字段")
    private Integer classId;
    
    @ApiModelProperty(value = "班级名称", notes = "机构维度字段")
    private String className;
    
    /** ==================== 学习指标字段 ==================== */
    
    @ApiModelProperty(value = "内容类型", notes = "学习内容的类型分类")
    private String contentType;
    
    @ApiModelProperty(value = "学习模块", notes = "具体的学习模块名称")
    private String studyModule;
    
    @ApiModelProperty(value = "测验平均分", example = "85")
    private Integer quizAvgScore;
    
    @ApiModelProperty(value = "提分率", example = "0.75", notes = "范围：0-1")
    private Float improveScoreRate;
    
    @ApiModelProperty(value = "平均提分", example = "15")
    private Integer improveScoreMean;
    
    @ApiModelProperty(value = "学习效率均值", example = "0.85", notes = "范围：0-1")
    private Float studyEfficiencyMean;
    
    @ApiModelProperty(value = "日有效学习时长", example = "120", notes = "单位：分钟")
    private Integer studyEffectTimeDay;
    
    @ApiModelProperty(value = "日新学数量", example = "50")
    private Integer studyNewNumDay;
    
    @ApiModelProperty(value = "学习速度", example = "25", notes = "每分钟学习量")
    private Integer studySpeed;
    
    @ApiModelProperty(value = "日复习频次", example = "3")
    private Integer reviewFrequencyDay;
    
    @ApiModelProperty(value = "日复习数量", example = "100")
    private Integer reviewNumDay;
    
    @ApiModelProperty(value = "复习倍数均值", example = "2.5")
    private Integer reviewMultiplierMean;
    
    /** ==================== 通用扩展字段 ==================== */
    
    @ApiModelProperty(value = "业务指标数据", notes = "JSON格式存储所有业务指标，支持动态扩展")
    private String metricsData;
    
    @ApiModelProperty(value = "扩展数据", notes = "JSON格式存储扩展信息")
    private String extendData;
    
    @ApiModelProperty(value = "数据版本", example = "v1.0", notes = "支持数据版本管理")
    private String dataVersion;
    
    /** ==================== 辅助方法 ==================== */
    
    /**
     * 获取业务类型枚举
     */
    public BusinessTypeEnum getBusinessTypeEnum() {
        try {
            return BusinessTypeEnum.valueOf(this.businessType);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取维度级别枚举
     */
    public DimensionLevelEnum getDimensionLevelEnum() {
        try {
            return DimensionLevelEnum.valueOf(this.dimensionLevel);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 设置业务类型枚举
     */
    public void setBusinessTypeEnum(BusinessTypeEnum businessTypeEnum) {
        this.businessType = businessTypeEnum != null ? businessTypeEnum.name() : null;
    }
    
    /**
     * 设置维度级别枚举
     */
    public void setDimensionLevelEnum(DimensionLevelEnum dimensionLevelEnum) {
        this.dimensionLevel = dimensionLevelEnum != null ? dimensionLevelEnum.name() : null;
    }
}
