package com.redbook.dashboard.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 排行榜项目
 * 用于表示排行分析中的单个排行项
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "RankingItem", description = "排行榜项目")
public class RankingItem {
    
    @ApiModelProperty(value = "排名", example = "1", notes = "在排行榜中的位置，从1开始")
    private Integer rank;
    
    @ApiModelProperty(value = "名称", example = "北京市", notes = "排行项的名称（省份、城市、用户等）")
    private String name;
    
    @ApiModelProperty(value = "指标值", example = "95.5", notes = "用于排序的指标数值")
    private Object value;
    
    @ApiModelProperty(value = "格式化后的值", example = "95.5分", notes = "包含单位的格式化显示值")
    private String formattedValue;
    
    @ApiModelProperty(value = "指标名称", example = "quizAvgScore", notes = "排序依据的指标英文名称")
    private String metricName;
    
    @ApiModelProperty(value = "指标显示名称", example = "测验平均分", notes = "排序依据的指标中文显示名称")
    private String metricDisplayName;
    
    @ApiModelProperty(value = "维度ID", example = "110000", notes = "对应的维度标识ID")
    private String dimensionId;
    
    @ApiModelProperty(value = "维度类型", example = "PROVINCE", notes = "维度类型：USER-用户, PROVINCE-省份等")
    private String dimensionType;
    
    @ApiModelProperty(value = "上期排名", example = "3", notes = "上一个统计周期的排名，用于显示排名变化")
    private Integer previousRank;
    
    @ApiModelProperty(value = "排名变化", example = "2", notes = "相对于上期的排名变化，正数表示上升")
    private Integer rankChange;
    
    @ApiModelProperty(value = "排名变化趋势", example = "UP", notes = "排名变化趋势：UP-上升, DOWN-下降, STABLE-稳定, NEW-新上榜")
    private String rankTrend;
    
    @ApiModelProperty(value = "上期指标值", example = "92.3", notes = "上一个统计周期的指标值")
    private Object previousValue;
    
    @ApiModelProperty(value = "指标值变化", example = "3.2", notes = "相对于上期的指标值变化")
    private Object valueChange;
    
    @ApiModelProperty(value = "指标值变化率", example = "3.47", notes = "相对于上期的指标值变化率（百分比）")
    private Double valueChangeRate;
    
    @ApiModelProperty(value = "格式化的指标值变化", example = "+3.2分", notes = "包含符号和单位的指标值变化显示")
    private String formattedValueChange;
    
    @ApiModelProperty(value = "格式化的变化率", example = "+3.47%", notes = "包含符号的变化率显示")
    private String formattedValueChangeRate;
    
    @ApiModelProperty(value = "额外信息", example = "{\"province\":\"北京市\",\"city\":\"朝阳区\"}", notes = "JSON格式的额外维度信息")
    private String extraInfo;
    
    @ApiModelProperty(value = "数据质量", example = "GOOD", notes = "数据质量标识：GOOD-良好, WARNING-警告, ERROR-错误")
    private String dataQuality;
    
    @ApiModelProperty(value = "备注信息", example = "数据异常", notes = "对该排行项的额外说明")
    private String remark;
    
    /**
     * 判断排名是否上升
     */
    public boolean isRankUp() {
        return "UP".equals(rankTrend);
    }
    
    /**
     * 判断排名是否下降
     */
    public boolean isRankDown() {
        return "DOWN".equals(rankTrend);
    }
    
    /**
     * 判断排名是否稳定
     */
    public boolean isRankStable() {
        return "STABLE".equals(rankTrend);
    }
    
    /**
     * 判断是否为新上榜
     */
    public boolean isNewEntry() {
        return "NEW".equals(rankTrend);
    }
    
    /**
     * 获取数值类型的指标值
     */
    public Double getNumericValue() {
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 设置排名变化信息
     */
    public void setRankChangeInfo(Integer previousRank) {
        this.previousRank = previousRank;
        
        if (previousRank == null) {
            this.rankTrend = "NEW";
            this.rankChange = null;
        } else {
            this.rankChange = previousRank - this.rank; // 正数表示排名上升
            
            if (this.rankChange > 0) {
                this.rankTrend = "UP";
            } else if (this.rankChange < 0) {
                this.rankTrend = "DOWN";
            } else {
                this.rankTrend = "STABLE";
            }
        }
    }
    
    /**
     * 设置指标值变化信息
     */
    public void setValueChangeInfo(Object previousValue) {
        this.previousValue = previousValue;
        
        Double currentNum = getNumericValue();
        Double previousNum = null;
        
        if (previousValue instanceof Number) {
            previousNum = ((Number) previousValue).doubleValue();
        } else if (previousValue != null) {
            try {
                previousNum = Double.parseDouble(previousValue.toString());
            } catch (NumberFormatException e) {
                // 忽略转换错误
            }
        }
        
        if (currentNum != null && previousNum != null) {
            this.valueChange = currentNum - previousNum;
            this.valueChangeRate = previousNum != 0 ? ((currentNum - previousNum) / previousNum) * 100 : 0.0;
        }
    }
}
