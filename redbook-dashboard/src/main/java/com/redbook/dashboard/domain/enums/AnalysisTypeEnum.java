package com.redbook.dashboard.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分析类型枚举
 * 定义了数据分析支持的各种分析类型
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Getter
@AllArgsConstructor
public enum AnalysisTypeEnum {
    
    /**
     * 列表查询
     * 基础的数据列表展示
     */
    LIST("list", "列表查询", "基础数据列表展示"),
    
    /**
     * 趋势分析
     * 时间序列数据的趋势变化分析
     */
    TREND("trend", "趋势分析", "时间序列数据的趋势变化分析"),
    
    /**
     * 排行分析
     * 按指定指标进行排序的排行榜分析
     */
    RANKING("ranking", "排行分析", "按指定指标进行排序的排行榜分析"),
    
    /**
     * 对比分析
     * 不同时间段或不同维度的数据对比分析
     */
    COMPARISON("comparison", "对比分析", "不同时间段或不同维度的数据对比分析"),
    
    /**
     * 汇总统计
     * 数据的汇总统计信息
     */
    SUMMARY("summary", "汇总统计", "数据的汇总统计信息"),
    
    /**
     * 分布分析
     * 数据的分布情况分析
     */
    DISTRIBUTION("distribution", "分布分析", "数据的分布情况分析"),
    
    /**
     * 相关性分析
     * 不同指标之间的相关性分析
     */
    CORRELATION("correlation", "相关性分析", "不同指标之间的相关性分析");
    
    /**
     * 分析类型标识
     */
    private final String type;
    
    /**
     * 分析类型名称
     */
    private final String name;
    
    /**
     * 分析类型描述
     */
    private final String description;
    
    /**
     * 根据类型标识获取枚举
     * 
     * @param type 类型标识
     * @return 对应的枚举，如果不存在则返回null
     */
    public static AnalysisTypeEnum fromType(String type) {
        for (AnalysisTypeEnum analysisType : values()) {
            if (analysisType.getType().equals(type)) {
                return analysisType;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为时间相关的分析类型
     * 
     * @return 是否为时间相关的分析类型
     */
    public boolean isTimeRelated() {
        return this == TREND || this == COMPARISON;
    }
    
    /**
     * 判断是否需要排序
     * 
     * @return 是否需要排序
     */
    public boolean requiresSorting() {
        return this == RANKING || this == COMPARISON;
    }
}
