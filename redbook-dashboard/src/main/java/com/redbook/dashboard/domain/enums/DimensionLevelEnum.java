package com.redbook.dashboard.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 维度级别配置枚举
 * 定义了数据分析支持的所有维度级别，包括字段映射关系
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Getter
@AllArgsConstructor
public enum DimensionLevelEnum {
    
    /**
     * 用户维度
     * 最细粒度的数据分析，按用户个体统计
     */
    USER("user", "用户维度", "user_id", "user_name"),

    /**
     * 全国维度
     * 全国汇总数据，不区分地理位置
     */
    COUNTRY("country", "全国维度", null, null),

    /**
     * 省份维度
     * 按省份统计的数据分析
     */
    PROVINCE("province", "省份维度", "province_id", "province"),

    /**
     * 城市维度
     * 按城市统计的数据分析，包含省份信息
     */
    CITY("city", "城市维度", "city_id", "city"),

    /**
     * 体验中心维度
     * 按体验中心统计的数据分析，包含地理层级信息
     */
    ALIAS("alias", "体验中心维度", "alias_id", "alias"),

    /**
     * 专卖店维度
     * 按专卖店统计的数据分析，包含完整的机构层级信息
     */
    SHOP("shop", "专卖店维度", "exclusive_shop_id", "shop_name"),

    /**
     * 班级维度
     * 按班级统计的数据分析，最细粒度的机构维度
     */
    CLASS("class", "班级维度", "class_id", "class_name");
    
    /**
     * 维度级别标识
     */
    private final String level;
    
    /**
     * 维度描述
     */
    private final String description;
    
    /**
     * 源表字段列表
     * 对应数据库表中的实际字段名
     */
    private final List<String> sourceFields;
    
    /**
     * 目标实体字段列表
     * 对应通用实体类中的字段名
     */
    private final List<String> targetFields;
    
    /**
     * 获取维度的层级深度
     * 
     * @return 层级深度（0表示全国，数字越大层级越深）
     */
    public int getHierarchyLevel() {
        switch (this) {
            case COUNTRY:
                return 0;
            case PROVINCE:
                return 1;
            case CITY:
                return 2;
            case ALIAS:
                return 3;
            case SHOP:
                return 4;
            case CLASS:
                return 5;
            case USER:
                return 6;
            default:
                return -1;
        }
    }
    
    /**
     * 判断是否为地理维度
     * 
     * @return 是否为地理维度
     */
    public boolean isGeographicDimension() {
        return this == PROVINCE || this == CITY;
    }
    
    /**
     * 判断是否为机构维度
     * 
     * @return 是否为机构维度
     */
    public boolean isOrganizationDimension() {
        return this == ALIAS || this == SHOP || this == CLASS;
    }
    
    /**
     * 判断是否为用户维度
     * 
     * @return 是否为用户维度
     */
    public boolean isUserDimension() {
        return this == USER;
    }
    
    /**
     * 获取父级维度
     * 
     * @return 父级维度，如果没有则返回null
     */
    public DimensionLevelEnum getParentDimension() {
        switch (this) {
            case CITY:
                return PROVINCE;
            case ALIAS:
                return CITY;
            case SHOP:
                return ALIAS;
            case CLASS:
                return SHOP;
            default:
                return null;
        }
    }
}
