package com.redbook.dashboard.domain.enums;

import com.redbook.dashboard.handler.MetricsHandler;
import com.redbook.dashboard.handler.StudyMetricsHandler;
import com.redbook.dashboard.handler.RenewalMetricsHandler;
import com.redbook.dashboard.handler.TaskMetricsHandler;
import com.redbook.dashboard.handler.MarketingMetricsHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 业务类型配置枚举
 * 支持多种业务数据分析类型，通过配置驱动的方式管理不同业务的表映射和处理器
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    
    /**
     * 学习数据分析
     * 包含学习时长、成绩、效率等指标
     */
    STUDY("学习数据", "intelligence_{level}_study_module_day", StudyMetricsHandler.class, 
          Arrays.asList("quizAvgScore", "improveScoreRate", "improveScoreMean", "studyEfficiencyMean", 
                       "studyEffectTimeDay", "studyNewNumDay", "studySpeed", "reviewFrequencyDay", 
                       "reviewNumDay", "reviewMultiplierMean")),
    
    /**
     * 续费数据分析（预留）
     * 包含续费率、续费金额、续费周期等指标
     */
    RENEWAL("续费数据", "intelligence_{level}_renewal_day", RenewalMetricsHandler.class,
           Arrays.asList("renewalRate", "renewalAmount", "renewalCycle", "churnRate", "ltv", "arpu")),
    
    /**
     * 任务数据分析（预留）
     * 包含任务完成率、任务得分、任务时长等指标
     */
    TASK("任务数据", "intelligence_{level}_task_day", TaskMetricsHandler.class,
         Arrays.asList("taskCompletionRate", "taskScore", "taskTime", "taskDifficulty", "taskType")),
    
    /**
     * 营销数据分析（预留）
     * 包含转化率、点击率、获客成本等指标
     */
    MARKETING("营销数据", "intelligence_{level}_marketing_day", MarketingMetricsHandler.class,
             Arrays.asList("conversionRate", "clickRate", "costPerAcquisition", "roi", "impressions"));
    
    /**
     * 业务描述
     */
    private final String description;
    
    /**
     * 表名模式，{level}会被替换为具体的维度级别
     * 例如：intelligence_province_study_module_day
     */
    private final String tableNamePattern;
    
    /**
     * 对应的指标处理器类
     */
    private final Class<? extends MetricsHandler> handlerClass;
    
    /**
     * 默认支持的指标列表
     */
    private final List<String> defaultMetrics;
    
    /**
     * 根据维度级别生成实际的表名
     * 
     * @param level 维度级别
     * @return 实际表名
     */
    public String getTableName(String level) {
        return tableNamePattern.replace("{level}", level);
    }
    
    /**
     * 根据维度级别枚举生成实际的表名
     * 
     * @param levelEnum 维度级别枚举
     * @return 实际表名
     */
    public String getTableName(DimensionLevelEnum levelEnum) {
        return getTableName(levelEnum.getLevel());
    }
    
    /**
     * 检查是否支持指定的指标
     * 
     * @param metricName 指标名称
     * @return 是否支持
     */
    public boolean supportsMetric(String metricName) {
        return defaultMetrics.contains(metricName);
    }
}
