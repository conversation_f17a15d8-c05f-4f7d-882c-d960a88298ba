package com.redbook.dashboard.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对比分析结果
 * 用于表示两个时间段或两组数据的对比分析结果
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ComparisonResult", description = "对比分析结果")
public class ComparisonResult {
    
    @ApiModelProperty(value = "当前期指标值", example = "95.5", notes = "当前统计周期的指标数值")
    private Object currentValue;
    
    @ApiModelProperty(value = "对比期指标值", example = "92.3", notes = "对比统计周期的指标数值")
    private Object previousValue;
    
    @ApiModelProperty(value = "变化量", example = "3.2", notes = "当前期相对于对比期的变化量")
    private Object change;
    
    @ApiModelProperty(value = "变化率", example = "3.47", notes = "当前期相对于对比期的变化率（百分比）")
    private Double changeRate;
    
    @ApiModelProperty(value = "格式化的当前期值", example = "95.5分", notes = "包含单位的当前期格式化显示值")
    private String currentFormattedValue;
    
    @ApiModelProperty(value = "格式化的对比期值", example = "92.3分", notes = "包含单位的对比期格式化显示值")
    private String previousFormattedValue;
    
    @ApiModelProperty(value = "格式化的变化量", example = "+3.2分", notes = "包含符号和单位的变化量显示")
    private String changeFormattedValue;
    
    @ApiModelProperty(value = "格式化的变化率", example = "+3.47%", notes = "包含符号的变化率显示")
    private String changeRateFormattedValue;
    
    @ApiModelProperty(value = "变化趋势", example = "UP", notes = "变化趋势：UP-上升, DOWN-下降, STABLE-稳定")
    private String trend;
    
    @ApiModelProperty(value = "指标名称", example = "quizAvgScore", notes = "对比的指标英文名称")
    private String metricName;
    
    @ApiModelProperty(value = "指标显示名称", example = "测验平均分", notes = "对比的指标中文显示名称")
    private String metricDisplayName;
    
    @ApiModelProperty(value = "当前期时间范围", example = "2024-12-01 至 2024-12-31", notes = "当前期的时间范围描述")
    private String currentPeriod;
    
    @ApiModelProperty(value = "对比期时间范围", example = "2024-11-01 至 2024-11-30", notes = "对比期的时间范围描述")
    private String previousPeriod;
    
    @ApiModelProperty(value = "对比类型", example = "MONTH_OVER_MONTH", notes = "对比类型：MONTH_OVER_MONTH-环比, YEAR_OVER_YEAR-同比, CUSTOM-自定义")
    private String comparisonType;
    
    @ApiModelProperty(value = "显著性水平", example = "SIGNIFICANT", notes = "变化的显著性：SIGNIFICANT-显著, MODERATE-中等, SLIGHT-轻微, NONE-无变化")
    private String significance;
    
    @ApiModelProperty(value = "当前期数据量", example = "1000", notes = "当前期参与计算的数据条数")
    private Integer currentDataCount;
    
    @ApiModelProperty(value = "对比期数据量", example = "950", notes = "对比期参与计算的数据条数")
    private Integer previousDataCount;
    
    @ApiModelProperty(value = "置信度", example = "95.0", notes = "对比结果的置信度（百分比）")
    private Double confidence;
    
    @ApiModelProperty(value = "数据质量", example = "GOOD", notes = "数据质量标识：GOOD-良好, WARNING-警告, ERROR-错误")
    private String dataQuality;
    
    @ApiModelProperty(value = "备注信息", example = "数据异常", notes = "对该对比结果的额外说明")
    private String remark;
    
    /**
     * 判断是否为上升趋势
     */
    public boolean isUpTrend() {
        return "UP".equals(trend);
    }
    
    /**
     * 判断是否为下降趋势
     */
    public boolean isDownTrend() {
        return "DOWN".equals(trend);
    }
    
    /**
     * 判断是否为稳定趋势
     */
    public boolean isStableTrend() {
        return "STABLE".equals(trend);
    }
    
    /**
     * 判断变化是否显著
     */
    public boolean isSignificantChange() {
        return "SIGNIFICANT".equals(significance);
    }
    
    /**
     * 判断变化是否为中等程度
     */
    public boolean isModerateChange() {
        return "MODERATE".equals(significance);
    }
    
    /**
     * 判断变化是否轻微
     */
    public boolean isSlightChange() {
        return "SLIGHT".equals(significance);
    }
    
    /**
     * 判断是否无变化
     */
    public boolean isNoChange() {
        return "NONE".equals(significance);
    }
    
    /**
     * 获取数值类型的当前期值
     */
    public Double getCurrentNumericValue() {
        return convertToDouble(currentValue);
    }
    
    /**
     * 获取数值类型的对比期值
     */
    public Double getPreviousNumericValue() {
        return convertToDouble(previousValue);
    }
    
    /**
     * 获取数值类型的变化量
     */
    public Double getChangeNumericValue() {
        return convertToDouble(change);
    }
    
    /**
     * 转换为Double类型
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 计算显著性水平
     */
    public void calculateSignificance() {
        Double changeRateAbs = Math.abs(changeRate != null ? changeRate : 0.0);
        
        if (changeRateAbs >= 20.0) {
            this.significance = "SIGNIFICANT";
        } else if (changeRateAbs >= 10.0) {
            this.significance = "MODERATE";
        } else if (changeRateAbs >= 5.0) {
            this.significance = "SLIGHT";
        } else {
            this.significance = "NONE";
        }
    }
}
