package com.redbook.dashboard.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 趋势分析数据点
 * 用于表示时间序列分析中的单个数据点
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "TrendPoint", description = "趋势分析数据点")
public class TrendPoint {
    
    @ApiModelProperty(value = "日期", example = "2024-12-25", notes = "数据点对应的日期")
    private String date;
    
    @ApiModelProperty(value = "时间戳", example = "1703462400000", notes = "日期对应的时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date timestamp;
    
    @ApiModelProperty(value = "指标值", example = "85.5", notes = "该时间点的指标数值")
    private Object value;
    
    @ApiModelProperty(value = "格式化后的值", example = "85.5分", notes = "包含单位的格式化显示值")
    private String formattedValue;
    
    @ApiModelProperty(value = "指标名称", example = "quizAvgScore", notes = "指标的英文名称")
    private String metricName;
    
    @ApiModelProperty(value = "指标显示名称", example = "测验平均分", notes = "指标的中文显示名称")
    private String metricDisplayName;
    
    @ApiModelProperty(value = "变化趋势", example = "UP", notes = "相对于前一个点的变化趋势：UP-上升, DOWN-下降, STABLE-稳定")
    private String trend;
    
    @ApiModelProperty(value = "变化量", example = "2.5", notes = "相对于前一个点的变化量")
    private Object changeValue;
    
    @ApiModelProperty(value = "变化率", example = "3.2", notes = "相对于前一个点的变化率（百分比）")
    private Double changeRate;
    
    @ApiModelProperty(value = "格式化的变化量", example = "+2.5分", notes = "包含符号和单位的变化量显示")
    private String formattedChangeValue;
    
    @ApiModelProperty(value = "格式化的变化率", example = "+3.2%", notes = "包含符号的变化率显示")
    private String formattedChangeRate;
    
    @ApiModelProperty(value = "数据质量", example = "GOOD", notes = "数据质量标识：GOOD-良好, WARNING-警告, ERROR-错误")
    private String dataQuality;
    
    @ApiModelProperty(value = "备注信息", example = "数据异常", notes = "对该数据点的额外说明")
    private String remark;
    
    /**
     * 判断是否为上升趋势
     */
    public boolean isUpTrend() {
        return "UP".equals(trend);
    }
    
    /**
     * 判断是否为下降趋势
     */
    public boolean isDownTrend() {
        return "DOWN".equals(trend);
    }
    
    /**
     * 判断是否为稳定趋势
     */
    public boolean isStableTrend() {
        return "STABLE".equals(trend);
    }
    
    /**
     * 获取数值类型的指标值
     */
    public Double getNumericValue() {
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 设置趋势信息
     */
    public void setTrendInfo(Object previousValue) {
        Double currentNum = getNumericValue();
        Double previousNum = null;
        
        if (previousValue instanceof Number) {
            previousNum = ((Number) previousValue).doubleValue();
        } else if (previousValue != null) {
            try {
                previousNum = Double.parseDouble(previousValue.toString());
            } catch (NumberFormatException e) {
                // 忽略转换错误
            }
        }
        
        if (currentNum != null && previousNum != null) {
            this.changeValue = currentNum - previousNum;
            this.changeRate = previousNum != 0 ? ((currentNum - previousNum) / previousNum) * 100 : 0.0;
            
            if (currentNum > previousNum) {
                this.trend = "UP";
            } else if (currentNum < previousNum) {
                this.trend = "DOWN";
            } else {
                this.trend = "STABLE";
            }
        }
    }
}
