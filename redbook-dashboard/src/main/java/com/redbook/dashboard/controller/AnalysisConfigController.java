package com.redbook.dashboard.controller;

import com.redbook.dashboard.config.AnalysisConfig;
import com.redbook.dashboard.domain.enums.AnalysisTypeEnum;
import com.redbook.dashboard.domain.enums.BusinessTypeEnum;
import com.redbook.dashboard.domain.enums.DimensionLevelEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分析配置信息控制器
 * 提供分析系统的配置信息查询接口
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Slf4j
@RestController
@RequestMapping("/api/analysis/config")
@Api(tags = "分析配置信息接口")
public class AnalysisConfigController {
    
    @Autowired
    private AnalysisConfig analysisConfig;
    
    /**
     * 获取所有业务类型
     */
    @GetMapping("/business-types")
    @ApiOperation(value = "获取所有业务类型", notes = "获取系统支持的所有业务类型列表")
    public List<Map<String, Object>> getBusinessTypes() {
        log.info("获取所有业务类型");
        
        return Arrays.stream(BusinessTypeEnum.values())
                .map(businessType -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("code", businessType.name());
                    info.put("description", businessType.getDescription());
                    info.put("tableNamePattern", businessType.getTableNamePattern());
                    info.put("handlerClass", businessType.getHandlerClass().getSimpleName());
                    info.put("defaultMetrics", businessType.getDefaultMetrics());
                    return info;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有维度级别
     */
    @GetMapping("/dimension-levels")
    @ApiOperation(value = "获取所有维度级别", notes = "获取系统支持的所有维度级别列表")
    public List<Map<String, Object>> getDimensionLevels() {
        log.info("获取所有维度级别");
        
        return Arrays.stream(DimensionLevelEnum.values())
                .map(dimensionLevel -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("code", dimensionLevel.name());
                    info.put("level", dimensionLevel.getLevel());
                    info.put("description", dimensionLevel.getDescription());
                    info.put("sourceFields", dimensionLevel.getSourceFields());
                    info.put("targetFields", dimensionLevel.getTargetFields());
                    info.put("hierarchyLevel", dimensionLevel.getHierarchyLevel());
                    info.put("isGeographic", dimensionLevel.isGeographicDimension());
                    info.put("isOrganization", dimensionLevel.isOrganizationDimension());
                    info.put("isUser", dimensionLevel.isUserDimension());
                    
                    DimensionLevelEnum parent = dimensionLevel.getParentDimension();
                    info.put("parentDimension", parent != null ? parent.name() : null);
                    
                    return info;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有分析类型
     */
    @GetMapping("/analysis-types")
    @ApiOperation(value = "获取所有分析类型", notes = "获取系统支持的所有分析类型列表")
    public List<Map<String, Object>> getAnalysisTypes() {
        log.info("获取所有分析类型");
        
        return Arrays.stream(AnalysisTypeEnum.values())
                .map(analysisType -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("code", analysisType.name());
                    info.put("type", analysisType.getType());
                    info.put("name", analysisType.getName());
                    info.put("description", analysisType.getDescription());
                    info.put("isTimeRelated", analysisType.isTimeRelated());
                    info.put("requiresSorting", analysisType.requiresSorting());
                    return info;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取指定业务类型的详细信息
     */
    @GetMapping("/business-types/{businessType}")
    @ApiOperation(value = "获取业务类型详细信息", notes = "获取指定业务类型的详细配置信息")
    public Map<String, Object> getBusinessTypeInfo(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @PathVariable String businessType) {
        
        log.info("获取业务类型详细信息: {}", businessType);
        
        try {
            BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.valueOf(businessType);
            return analysisConfig.getBusinessTypeInfo(businessTypeEnum);
        } catch (IllegalArgumentException e) {
            log.warn("无效的业务类型: {}", businessType);
            throw new RuntimeException("无效的业务类型: " + businessType);
        }
    }
    
    /**
     * 获取指定维度级别的详细信息
     */
    @GetMapping("/dimension-levels/{dimensionLevel}")
    @ApiOperation(value = "获取维度级别详细信息", notes = "获取指定维度级别的详细配置信息")
    public Map<String, Object> getDimensionLevelInfo(
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @PathVariable String dimensionLevel) {
        
        log.info("获取维度级别详细信息: {}", dimensionLevel);
        
        try {
            DimensionLevelEnum dimensionLevelEnum = DimensionLevelEnum.valueOf(dimensionLevel);
            return analysisConfig.getDimensionLevelInfo(dimensionLevelEnum);
        } catch (IllegalArgumentException e) {
            log.warn("无效的维度级别: {}", dimensionLevel);
            throw new RuntimeException("无效的维度级别: " + dimensionLevel);
        }
    }
    
    /**
     * 获取业务类型和维度级别的表名映射
     */
    @GetMapping("/table-mappings")
    @ApiOperation(value = "获取表名映射", notes = "获取所有业务类型和维度级别的表名映射关系")
    public Map<String, Map<String, String>> getTableMappings() {
        log.info("获取表名映射");
        
        Map<String, Map<String, String>> mappings = new HashMap<>();
        
        for (BusinessTypeEnum businessType : BusinessTypeEnum.values()) {
            Map<String, String> dimensionTables = new HashMap<>();
            
            for (DimensionLevelEnum dimensionLevel : DimensionLevelEnum.values()) {
                String tableName = businessType.getTableName(dimensionLevel);
                dimensionTables.put(dimensionLevel.name(), tableName);
            }
            
            mappings.put(businessType.name(), dimensionTables);
        }
        
        return mappings;
    }
    
    /**
     * 获取指定业务类型支持的指标
     */
    @GetMapping("/business-types/{businessType}/metrics")
    @ApiOperation(value = "获取业务类型支持的指标", notes = "获取指定业务类型支持的所有指标列表")
    public List<String> getBusinessTypeMetrics(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @PathVariable String businessType) {
        
        log.info("获取业务类型支持的指标: {}", businessType);
        
        try {
            BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.valueOf(businessType);
            return businessTypeEnum.getDefaultMetrics();
        } catch (IllegalArgumentException e) {
            log.warn("无效的业务类型: {}", businessType);
            throw new RuntimeException("无效的业务类型: " + businessType);
        }
    }
    
    /**
     * 获取维度级别的字段映射
     */
    @GetMapping("/dimension-levels/{dimensionLevel}/field-mappings")
    @ApiOperation(value = "获取维度级别字段映射", notes = "获取指定维度级别的字段映射关系")
    public Map<String, Object> getDimensionLevelFieldMappings(
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @PathVariable String dimensionLevel) {
        
        log.info("获取维度级别字段映射: {}", dimensionLevel);
        
        try {
            DimensionLevelEnum dimensionLevelEnum = DimensionLevelEnum.valueOf(dimensionLevel);
            
            Map<String, Object> mappings = new HashMap<>();
            mappings.put("sourceFields", dimensionLevelEnum.getSourceFields());
            mappings.put("targetFields", dimensionLevelEnum.getTargetFields());
            
            // 构建字段对应关系
            List<String> sourceFields = dimensionLevelEnum.getSourceFields();
            List<String> targetFields = dimensionLevelEnum.getTargetFields();
            
            Map<String, String> fieldMappings = new HashMap<>();
            if (sourceFields != null && targetFields != null) {
                int minSize = Math.min(sourceFields.size(), targetFields.size());
                for (int i = 0; i < minSize; i++) {
                    fieldMappings.put(sourceFields.get(i), targetFields.get(i));
                }
            }
            mappings.put("fieldMappings", fieldMappings);
            
            return mappings;
            
        } catch (IllegalArgumentException e) {
            log.warn("无效的维度级别: {}", dimensionLevel);
            throw new RuntimeException("无效的维度级别: " + dimensionLevel);
        }
    }
    
    /**
     * 获取系统配置概览
     */
    @GetMapping("/overview")
    @ApiOperation(value = "获取系统配置概览", notes = "获取分析系统的整体配置概览信息")
    public Map<String, Object> getConfigOverview() {
        log.info("获取系统配置概览");
        
        return analysisConfig.getAllConfigInfo();
    }
    
    /**
     * 验证配置组合的有效性
     */
    @GetMapping("/validate")
    @ApiOperation(value = "验证配置组合", notes = "验证业务类型、维度级别、分析类型的组合是否有效")
    public Map<String, Object> validateConfiguration(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @RequestParam String businessType,
            
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @RequestParam String dimensionLevel,
            
            @ApiParam(value = "分析类型", required = true, example = "TREND") 
            @RequestParam String analysisType,
            
            @ApiParam(value = "指标名称", example = "quizAvgScore") 
            @RequestParam(required = false) String metricName) {
        
        log.info("验证配置组合: businessType={}, dimensionLevel={}, analysisType={}, metricName={}", 
                businessType, dimensionLevel, analysisType, metricName);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证枚举值
            BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.valueOf(businessType);
            DimensionLevelEnum dimensionLevelEnum = DimensionLevelEnum.valueOf(dimensionLevel);
            AnalysisTypeEnum analysisTypeEnum = AnalysisTypeEnum.valueOf(analysisType);
            
            result.put("valid", true);
            result.put("businessType", businessTypeEnum.name());
            result.put("dimensionLevel", dimensionLevelEnum.name());
            result.put("analysisType", analysisTypeEnum.name());
            
            // 生成表名
            String tableName = businessTypeEnum.getTableName(dimensionLevelEnum);
            result.put("tableName", tableName);
            
            // 验证指标
            if (metricName != null && !metricName.trim().isEmpty()) {
                boolean metricSupported = businessTypeEnum.supportsMetric(metricName);
                result.put("metricSupported", metricSupported);
                result.put("metricName", metricName);
                
                if (!metricSupported) {
                    result.put("supportedMetrics", businessTypeEnum.getDefaultMetrics());
                }
            }
            
        } catch (IllegalArgumentException e) {
            result.put("valid", false);
            result.put("error", "无效的配置参数: " + e.getMessage());
        } catch (Exception e) {
            result.put("valid", false);
            result.put("error", "配置验证失败: " + e.getMessage());
        }
        
        return result;
    }
}
