package com.redbook.dashboard.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * Dashboard模块独立数据源配置
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Configuration
@MapperScan(basePackages = "com.redbook.dashboard.mapper", sqlSessionFactoryRef = "dashboardSqlSessionFactory")
public class DashboardDataSourceConfig {

    /**
     * Dashboard数据源配置
     */
    @Bean(name = "dashboardDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid.dashboard")
    @Primary
    public DataSource dashboardDataSource() {
        return new DruidDataSource();
    }

    /**
     * Dashboard事务管理器
     */
    @Bean(name = "dashboardTransactionManager")
    @Primary
    public DataSourceTransactionManager dashboardTransactionManager(@Qualifier("dashboardDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * Dashboard SqlSessionFactory
     */
    @Bean(name = "dashboardSqlSessionFactory")
    @Primary
    public SqlSessionFactory dashboardSqlSessionFactory(@Qualifier("dashboardDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        
        // 设置MyBatis配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(false);
        configuration.setCallSettersOnNulls(true);
        configuration.setJdbcTypeForNull(org.apache.ibatis.type.JdbcType.NULL);
        bean.setConfiguration(configuration);
        
        // 设置类型别名包
        bean.setTypeAliasesPackage("com.redbook.dashboard.domain");
        
        // 设置Mapper XML文件位置
        bean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:mapper/dashboard/*.xml"));
        
        // 添加分页插件
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        bean.setPlugins(interceptor);
        
        return bean.getObject();
    }
}
