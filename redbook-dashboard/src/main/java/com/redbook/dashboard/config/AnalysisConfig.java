package com.redbook.dashboard.config;

import com.redbook.dashboard.domain.enums.BusinessTypeEnum;
import com.redbook.dashboard.domain.enums.DimensionLevelEnum;
import com.redbook.dashboard.handler.MetricsHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据分析配置类
 * 管理分析相关的配置和组件初始化
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Slf4j
@Configuration
public class AnalysisConfig {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 指标处理器注册表
     */
    private final Map<BusinessTypeEnum, MetricsHandler> handlerRegistry = new HashMap<>();
    
    /**
     * 初始化配置
     */
    @PostConstruct
    public void init() {
        log.info("初始化数据分析配置...");
        
        // 注册指标处理器
        registerMetricsHandlers();
        
        // 验证配置
        validateConfiguration();
        
        log.info("数据分析配置初始化完成");
    }
    
    /**
     * 注册指标处理器
     */
    private void registerMetricsHandlers() {
        log.info("注册指标处理器...");
        
        for (BusinessTypeEnum businessType : BusinessTypeEnum.values()) {
            try {
                Class<? extends MetricsHandler> handlerClass = businessType.getHandlerClass();
                MetricsHandler handler = applicationContext.getBean(handlerClass);
                handlerRegistry.put(businessType, handler);
                
                log.info("注册指标处理器成功: {} -> {}", businessType, handlerClass.getSimpleName());
            } catch (Exception e) {
                log.warn("注册指标处理器失败: {}, 错误: {}", businessType, e.getMessage());
            }
        }
        
        log.info("指标处理器注册完成，共注册{}个处理器", handlerRegistry.size());
    }
    
    /**
     * 验证配置
     */
    private void validateConfiguration() {
        log.info("验证数据分析配置...");
        
        // 验证业务类型配置
        for (BusinessTypeEnum businessType : BusinessTypeEnum.values()) {
            validateBusinessType(businessType);
        }
        
        // 验证维度级别配置
        for (DimensionLevelEnum dimensionLevel : DimensionLevelEnum.values()) {
            validateDimensionLevel(dimensionLevel);
        }
        
        log.info("数据分析配置验证完成");
    }
    
    /**
     * 验证业务类型配置
     */
    private void validateBusinessType(BusinessTypeEnum businessType) {
        try {
            // 检查表名模式
            String tableNamePattern = businessType.getTableNamePattern();
            if (tableNamePattern == null || tableNamePattern.trim().isEmpty()) {
                log.warn("业务类型{}的表名模式为空", businessType);
                return;
            }
            
            // 检查处理器类
            Class<? extends MetricsHandler> handlerClass = businessType.getHandlerClass();
            if (handlerClass == null) {
                log.warn("业务类型{}的处理器类为空", businessType);
                return;
            }
            
            // 检查默认指标
            if (businessType.getDefaultMetrics() == null || businessType.getDefaultMetrics().isEmpty()) {
                log.warn("业务类型{}的默认指标列表为空", businessType);
                return;
            }
            
            // 测试表名生成
            for (DimensionLevelEnum dimensionLevel : DimensionLevelEnum.values()) {
                String tableName = businessType.getTableName(dimensionLevel);
                if (tableName == null || tableName.trim().isEmpty()) {
                    log.warn("业务类型{}在维度级别{}下生成的表名为空", businessType, dimensionLevel);
                }
            }
            
            log.debug("业务类型{}配置验证通过", businessType);
            
        } catch (Exception e) {
            log.error("验证业务类型{}配置失败", businessType, e);
        }
    }
    
    /**
     * 验证维度级别配置
     */
    private void validateDimensionLevel(DimensionLevelEnum dimensionLevel) {
        try {
            // 检查级别标识
            String level = dimensionLevel.getLevel();
            if (level == null || level.trim().isEmpty()) {
                log.warn("维度级别{}的级别标识为空", dimensionLevel);
                return;
            }
            
            // 检查字段映射
            if (dimensionLevel.getSourceFields() == null) {
                log.warn("维度级别{}的源字段列表为空", dimensionLevel);
            }
            
            if (dimensionLevel.getTargetFields() == null) {
                log.warn("维度级别{}的目标字段列表为空", dimensionLevel);
            }
            
            // 检查字段映射一致性
            if (dimensionLevel.getSourceFields() != null && dimensionLevel.getTargetFields() != null) {
                int sourceSize = dimensionLevel.getSourceFields().size();
                int targetSize = dimensionLevel.getTargetFields().size();
                if (sourceSize != targetSize) {
                    log.warn("维度级别{}的源字段数量({})与目标字段数量({})不一致", 
                            dimensionLevel, sourceSize, targetSize);
                }
            }
            
            // 检查层级关系
            int hierarchyLevel = dimensionLevel.getHierarchyLevel();
            if (hierarchyLevel < 0) {
                log.warn("维度级别{}的层级深度为负数: {}", dimensionLevel, hierarchyLevel);
            }
            
            log.debug("维度级别{}配置验证通过", dimensionLevel);
            
        } catch (Exception e) {
            log.error("验证维度级别{}配置失败", dimensionLevel, e);
        }
    }
    
    /**
     * 获取指标处理器注册表
     */
    @Bean
    public Map<BusinessTypeEnum, MetricsHandler> metricsHandlerRegistry() {
        return handlerRegistry;
    }
    
    /**
     * 获取业务类型配置信息
     */
    public Map<String, Object> getBusinessTypeInfo(BusinessTypeEnum businessType) {
        Map<String, Object> info = new HashMap<>();
        
        info.put("name", businessType.name());
        info.put("description", businessType.getDescription());
        info.put("tableNamePattern", businessType.getTableNamePattern());
        info.put("handlerClass", businessType.getHandlerClass().getSimpleName());
        info.put("defaultMetrics", businessType.getDefaultMetrics());
        info.put("isHandlerRegistered", handlerRegistry.containsKey(businessType));
        
        return info;
    }
    
    /**
     * 获取维度级别配置信息
     */
    public Map<String, Object> getDimensionLevelInfo(DimensionLevelEnum dimensionLevel) {
        Map<String, Object> info = new HashMap<>();
        
        info.put("name", dimensionLevel.name());
        info.put("level", dimensionLevel.getLevel());
        info.put("description", dimensionLevel.getDescription());
        info.put("sourceFields", dimensionLevel.getSourceFields());
        info.put("targetFields", dimensionLevel.getTargetFields());
        info.put("hierarchyLevel", dimensionLevel.getHierarchyLevel());
        info.put("isGeographic", dimensionLevel.isGeographicDimension());
        info.put("isOrganization", dimensionLevel.isOrganizationDimension());
        info.put("isUser", dimensionLevel.isUserDimension());
        info.put("parentDimension", dimensionLevel.getParentDimension());
        
        return info;
    }
    
    /**
     * 获取所有配置信息
     */
    public Map<String, Object> getAllConfigInfo() {
        Map<String, Object> configInfo = new HashMap<>();
        
        // 业务类型配置
        Map<String, Object> businessTypes = new HashMap<>();
        for (BusinessTypeEnum businessType : BusinessTypeEnum.values()) {
            businessTypes.put(businessType.name(), getBusinessTypeInfo(businessType));
        }
        configInfo.put("businessTypes", businessTypes);
        
        // 维度级别配置
        Map<String, Object> dimensionLevels = new HashMap<>();
        for (DimensionLevelEnum dimensionLevel : DimensionLevelEnum.values()) {
            dimensionLevels.put(dimensionLevel.name(), getDimensionLevelInfo(dimensionLevel));
        }
        configInfo.put("dimensionLevels", dimensionLevels);
        
        // 统计信息
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalBusinessTypes", BusinessTypeEnum.values().length);
        statistics.put("totalDimensionLevels", DimensionLevelEnum.values().length);
        statistics.put("registeredHandlers", handlerRegistry.size());
        configInfo.put("statistics", statistics);
        
        return configInfo;
    }
}
