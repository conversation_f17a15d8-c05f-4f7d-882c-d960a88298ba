package com.redbook.dashboard.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Dashboard模块Swagger配置
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Configuration
@EnableSwagger2
public class DashboardSwaggerConfig {

    /**
     * 创建Dashboard模块的API文档
     */
    @Bean
    public Docket dashboardApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("dashboard-api")
                .apiInfo(dashboardApiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.redbook.dashboard.controller"))
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.ant("/api/analysis/**"))
                .build();
    }

    /**
     * Dashboard API信息
     */
    private ApiInfo dashboardApiInfo() {
        return new ApiInfoBuilder()
                .title("RedBook Dashboard API")
                .description("通用数据分析框架API接口文档")
                .version("1.0.0")
                .contact(new Contact("RedBook Team", "", ""))
                .license("Apache License 2.0")
                .licenseUrl("http://www.apache.org/licenses/LICENSE-2.0")
                .build();
    }
}
