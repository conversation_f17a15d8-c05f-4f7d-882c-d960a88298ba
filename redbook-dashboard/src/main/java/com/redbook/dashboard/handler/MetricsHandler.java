package com.redbook.dashboard.handler;

import com.redbook.dashboard.domain.entity.AnalysisDataEntity;
import com.redbook.dashboard.domain.vo.TrendPoint;
import com.redbook.dashboard.domain.vo.RankingItem;
import com.redbook.dashboard.domain.vo.ComparisonResult;

import java.util.List;
import java.util.Map;

/**
 * 指标处理器接口
 * 定义了不同业务类型的指标计算和分析方法
 * 通过策略模式实现不同业务逻辑的解耦
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
public interface MetricsHandler {
    
    /**
     * 解析指标数据
     * 将JSON格式的指标数据解析为Map结构
     * 
     * @param metricsData JSON格式的指标数据
     * @return 解析后的指标Map
     */
    Map<String, Object> parseMetrics(String metricsData);
    
    /**
     * 格式化指标数据用于存储
     * 将Map结构的指标数据格式化为JSON字符串
     * 
     * @param metrics 指标数据Map
     * @return JSON格式的指标字符串
     */
    String formatMetrics(Map<String, Object> metrics);
    
    /**
     * 计算趋势分析
     * 基于时间序列数据计算指定指标的趋势变化
     * 
     * @param dataList 数据列表，按时间排序
     * @param metricName 指标名称
     * @return 趋势点列表
     */
    List<TrendPoint> calculateTrend(List<AnalysisDataEntity> dataList, String metricName);
    
    /**
     * 计算排行分析
     * 按指定指标对数据进行排序，生成排行榜
     * 
     * @param dataList 数据列表
     * @param rankBy 排序依据的指标名称
     * @param limit 返回的排行数量限制，null表示不限制
     * @return 排行项列表
     */
    List<RankingItem> calculateRanking(List<AnalysisDataEntity> dataList, String rankBy, Integer limit);
    
    /**
     * 计算对比分析
     * 对比两个时间段或两组数据的指标差异
     * 
     * @param current 当前期数据
     * @param previous 对比期数据
     * @param metricName 对比的指标名称
     * @return 对比结果
     */
    ComparisonResult calculateComparison(List<AnalysisDataEntity> current, 
                                       List<AnalysisDataEntity> previous, 
                                       String metricName);
    
    /**
     * 获取支持的指标列表
     * 返回当前处理器支持的所有指标名称
     * 
     * @return 支持的指标名称列表
     */
    List<String> getSupportedMetrics();
    
    /**
     * 验证指标数据
     * 检查指标数据的有效性和完整性
     * 
     * @param metrics 指标数据Map
     * @return 是否有效
     */
    boolean validateMetrics(Map<String, Object> metrics);
    
    /**
     * 获取指标的显示名称
     * 将指标的英文名称转换为中文显示名称
     * 
     * @param metricName 指标英文名称
     * @return 指标中文显示名称
     */
    String getMetricDisplayName(String metricName);
    
    /**
     * 获取指标的单位
     * 返回指标的计量单位
     * 
     * @param metricName 指标名称
     * @return 指标单位
     */
    String getMetricUnit(String metricName);
    
    /**
     * 获取指标的数据类型
     * 返回指标的数据类型（整数、小数、百分比等）
     * 
     * @param metricName 指标名称
     * @return 数据类型
     */
    String getMetricDataType(String metricName);
    
    /**
     * 计算指标的汇总值
     * 对多条数据的指定指标进行汇总计算（求和、平均值等）
     * 
     * @param dataList 数据列表
     * @param metricName 指标名称
     * @param aggregationType 汇总类型：SUM-求和, AVG-平均值, MAX-最大值, MIN-最小值
     * @return 汇总结果
     */
    Object calculateAggregation(List<AnalysisDataEntity> dataList, String metricName, String aggregationType);
    
    /**
     * 格式化指标值用于显示
     * 根据指标类型格式化数值，如百分比、保留小数位等
     * 
     * @param metricName 指标名称
     * @param value 指标值
     * @return 格式化后的显示值
     */
    String formatMetricValue(String metricName, Object value);
}
