package com.redbook.dashboard.handler;

import com.alibaba.fastjson.JSON;
import com.redbook.dashboard.domain.entity.AnalysisDataEntity;
import com.redbook.dashboard.domain.vo.TrendPoint;
import com.redbook.dashboard.domain.vo.RankingItem;
import com.redbook.dashboard.domain.vo.ComparisonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习数据指标处理器
 * 实现学习相关指标的计算、分析和格式化
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Slf4j
@Component
public class StudyMetricsHandler implements MetricsHandler {
    
    /**
     * 学习指标的中文名称映射
     */
    private static final Map<String, String> METRIC_DISPLAY_NAMES = new HashMap<>();
    
    /**
     * 学习指标的单位映射
     */
    private static final Map<String, String> METRIC_UNITS = new HashMap<>();
    
    /**
     * 学习指标的数据类型映射
     */
    private static final Map<String, String> METRIC_DATA_TYPES = new HashMap<>();
    
    static {
        // 初始化指标显示名称
        METRIC_DISPLAY_NAMES.put("quizAvgScore", "测验平均分");
        METRIC_DISPLAY_NAMES.put("improveScoreRate", "提分率");
        METRIC_DISPLAY_NAMES.put("improveScoreMean", "平均提分");
        METRIC_DISPLAY_NAMES.put("studyEfficiencyMean", "学习效率均值");
        METRIC_DISPLAY_NAMES.put("studyEffectTimeDay", "日有效学习时长");
        METRIC_DISPLAY_NAMES.put("studyNewNumDay", "日新学数量");
        METRIC_DISPLAY_NAMES.put("studySpeed", "学习速度");
        METRIC_DISPLAY_NAMES.put("reviewFrequencyDay", "日复习频次");
        METRIC_DISPLAY_NAMES.put("reviewNumDay", "日复习数量");
        METRIC_DISPLAY_NAMES.put("reviewMultiplierMean", "复习倍数均值");
        
        // 初始化指标单位
        METRIC_UNITS.put("quizAvgScore", "分");
        METRIC_UNITS.put("improveScoreRate", "%");
        METRIC_UNITS.put("improveScoreMean", "分");
        METRIC_UNITS.put("studyEfficiencyMean", "%");
        METRIC_UNITS.put("studyEffectTimeDay", "分钟");
        METRIC_UNITS.put("studyNewNumDay", "个");
        METRIC_UNITS.put("studySpeed", "个/分钟");
        METRIC_UNITS.put("reviewFrequencyDay", "次");
        METRIC_UNITS.put("reviewNumDay", "个");
        METRIC_UNITS.put("reviewMultiplierMean", "倍");
        
        // 初始化指标数据类型
        METRIC_DATA_TYPES.put("quizAvgScore", "INTEGER");
        METRIC_DATA_TYPES.put("improveScoreRate", "PERCENTAGE");
        METRIC_DATA_TYPES.put("improveScoreMean", "INTEGER");
        METRIC_DATA_TYPES.put("studyEfficiencyMean", "PERCENTAGE");
        METRIC_DATA_TYPES.put("studyEffectTimeDay", "INTEGER");
        METRIC_DATA_TYPES.put("studyNewNumDay", "INTEGER");
        METRIC_DATA_TYPES.put("studySpeed", "INTEGER");
        METRIC_DATA_TYPES.put("reviewFrequencyDay", "INTEGER");
        METRIC_DATA_TYPES.put("reviewNumDay", "INTEGER");
        METRIC_DATA_TYPES.put("reviewMultiplierMean", "DECIMAL");
    }
    
    @Override
    public Map<String, Object> parseMetrics(String metricsData) {
        if (!StringUtils.hasText(metricsData)) {
            return new HashMap<>();
        }
        
        try {
            return JSON.parseObject(metricsData, Map.class);
        } catch (Exception e) {
            log.warn("解析学习指标数据失败: {}", metricsData, e);
            return new HashMap<>();
        }
    }
    
    @Override
    public String formatMetrics(Map<String, Object> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            return "{}";
        }
        
        try {
            return JSON.toJSONString(metrics);
        } catch (Exception e) {
            log.warn("格式化学习指标数据失败: {}", metrics, e);
            return "{}";
        }
    }
    
    @Override
    public List<TrendPoint> calculateTrend(List<AnalysisDataEntity> dataList, String metricName) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }
        
        return dataList.stream()
                .sorted(Comparator.comparing(AnalysisDataEntity::getStudyDate))
                .map(data -> {
                    Object value = extractMetricValue(data, metricName);
                    return TrendPoint.builder()
                            .date(data.getStudyDate())
                            .value(value)
                            .formattedValue(formatMetricValue(metricName, value))
                            .build();
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public List<RankingItem> calculateRanking(List<AnalysisDataEntity> dataList, String rankBy, Integer limit) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<RankingItem> rankings = dataList.stream()
                .map(data -> {
                    Object value = extractMetricValue(data, rankBy);
                    return RankingItem.builder()
                            .name(getDimensionName(data))
                            .value(value)
                            .formattedValue(formatMetricValue(rankBy, value))
                            .build();
                })
                .filter(item -> item.getValue() != null)
                .sorted((a, b) -> compareMetricValues(b.getValue(), a.getValue())) // 降序排列
                .collect(Collectors.toList());
        
        // 添加排名
        for (int i = 0; i < rankings.size(); i++) {
            rankings.get(i).setRank(i + 1);
        }
        
        // 限制返回数量
        if (limit != null && limit > 0 && rankings.size() > limit) {
            rankings = rankings.subList(0, limit);
        }
        
        return rankings;
    }
    
    @Override
    public ComparisonResult calculateComparison(List<AnalysisDataEntity> current, 
                                              List<AnalysisDataEntity> previous, 
                                              String metricName) {
        if (current == null || previous == null) {
            return ComparisonResult.builder().build();
        }
        
        // 计算当前期和对比期的平均值
        Double currentAvg = calculateAverageValue(current, metricName);
        Double previousAvg = calculateAverageValue(previous, metricName);
        
        if (currentAvg == null || previousAvg == null) {
            return ComparisonResult.builder().build();
        }
        
        // 计算变化量和变化率
        Double change = currentAvg - previousAvg;
        Double changeRate = previousAvg != 0 ? (change / previousAvg) * 100 : 0.0;
        
        return ComparisonResult.builder()
                .currentValue(currentAvg)
                .previousValue(previousAvg)
                .change(change)
                .changeRate(changeRate)
                .currentFormattedValue(formatMetricValue(metricName, currentAvg))
                .previousFormattedValue(formatMetricValue(metricName, previousAvg))
                .changeFormattedValue(formatChangeValue(change, metricName))
                .changeRateFormattedValue(formatPercentage(changeRate))
                .trend(changeRate > 0 ? "UP" : changeRate < 0 ? "DOWN" : "STABLE")
                .build();
    }
    
    @Override
    public List<String> getSupportedMetrics() {
        return Arrays.asList(
                "quizAvgScore", "improveScoreRate", "improveScoreMean", "studyEfficiencyMean",
                "studyEffectTimeDay", "studyNewNumDay", "studySpeed", "reviewFrequencyDay",
                "reviewNumDay", "reviewMultiplierMean"
        );
    }
    
    @Override
    public boolean validateMetrics(Map<String, Object> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            return false;
        }
        
        // 检查是否包含至少一个支持的指标
        return metrics.keySet().stream()
                .anyMatch(key -> getSupportedMetrics().contains(key));
    }
    
    @Override
    public String getMetricDisplayName(String metricName) {
        return METRIC_DISPLAY_NAMES.getOrDefault(metricName, metricName);
    }
    
    @Override
    public String getMetricUnit(String metricName) {
        return METRIC_UNITS.getOrDefault(metricName, "");
    }
    
    @Override
    public String getMetricDataType(String metricName) {
        return METRIC_DATA_TYPES.getOrDefault(metricName, "STRING");
    }
    
    @Override
    public Object calculateAggregation(List<AnalysisDataEntity> dataList, String metricName, String aggregationType) {
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }
        
        List<Double> values = dataList.stream()
                .map(data -> extractMetricValue(data, metricName))
                .filter(Objects::nonNull)
                .map(this::convertToDouble)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        if (values.isEmpty()) {
            return null;
        }
        
        switch (aggregationType.toUpperCase()) {
            case "SUM":
                return values.stream().mapToDouble(Double::doubleValue).sum();
            case "AVG":
                return values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            case "MAX":
                return values.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            case "MIN":
                return values.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
            case "COUNT":
                return values.size();
            default:
                return values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        }
    }
    
    @Override
    public String formatMetricValue(String metricName, Object value) {
        if (value == null) {
            return "-";
        }
        
        String dataType = getMetricDataType(metricName);
        String unit = getMetricUnit(metricName);
        
        try {
            switch (dataType) {
                case "PERCENTAGE":
                    Double percentage = convertToDouble(value);
                    if (percentage == null) return "-";
                    return formatPercentage(percentage * 100) + unit;
                    
                case "DECIMAL":
                    Double decimal = convertToDouble(value);
                    if (decimal == null) return "-";
                    DecimalFormat df = new DecimalFormat("#.##");
                    return df.format(decimal) + unit;
                    
                case "INTEGER":
                default:
                    Double number = convertToDouble(value);
                    if (number == null) return "-";
                    return String.valueOf(number.intValue()) + unit;
            }
        } catch (Exception e) {
            log.warn("格式化指标值失败: metricName={}, value={}", metricName, value, e);
            return String.valueOf(value) + unit;
        }
    }
    
    /**
     * 从数据实体中提取指标值
     */
    private Object extractMetricValue(AnalysisDataEntity data, String metricName) {
        try {
            switch (metricName) {
                case "quizAvgScore":
                    return data.getQuizAvgScore();
                case "improveScoreRate":
                    return data.getImproveScoreRate();
                case "improveScoreMean":
                    return data.getImproveScoreMean();
                case "studyEfficiencyMean":
                    return data.getStudyEfficiencyMean();
                case "studyEffectTimeDay":
                    return data.getStudyEffectTimeDay();
                case "studyNewNumDay":
                    return data.getStudyNewNumDay();
                case "studySpeed":
                    return data.getStudySpeed();
                case "reviewFrequencyDay":
                    return data.getReviewFrequencyDay();
                case "reviewNumDay":
                    return data.getReviewNumDay();
                case "reviewMultiplierMean":
                    return data.getReviewMultiplierMean();
                default:
                    // 尝试从metricsData中获取
                    Map<String, Object> metrics = parseMetrics(data.getMetricsData());
                    return metrics.get(metricName);
            }
        } catch (Exception e) {
            log.warn("提取指标值失败: metricName={}, data={}", metricName, data.getId(), e);
            return null;
        }
    }
    
    /**
     * 获取维度名称用于显示
     */
    private String getDimensionName(AnalysisDataEntity data) {
        if (StringUtils.hasText(data.getDimensionName1())) {
            return data.getDimensionName1();
        }
        if (StringUtils.hasText(data.getUserName())) {
            return data.getUserName();
        }
        if (StringUtils.hasText(data.getProvince())) {
            return data.getProvince();
        }
        if (StringUtils.hasText(data.getCity())) {
            return data.getCity();
        }
        if (StringUtils.hasText(data.getAlias())) {
            return data.getAlias();
        }
        if (StringUtils.hasText(data.getShopName())) {
            return data.getShopName();
        }
        if (StringUtils.hasText(data.getClassName())) {
            return data.getClassName();
        }
        return "未知";
    }
    
    /**
     * 比较指标值大小
     */
    private int compareMetricValues(Object a, Object b) {
        Double valueA = convertToDouble(a);
        Double valueB = convertToDouble(b);
        
        if (valueA == null && valueB == null) return 0;
        if (valueA == null) return -1;
        if (valueB == null) return 1;
        
        return Double.compare(valueA, valueB);
    }
    
    /**
     * 计算平均值
     */
    private Double calculateAverageValue(List<AnalysisDataEntity> dataList, String metricName) {
        List<Double> values = dataList.stream()
                .map(data -> extractMetricValue(data, metricName))
                .filter(Objects::nonNull)
                .map(this::convertToDouble)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        if (values.isEmpty()) {
            return null;
        }
        
        return values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
    }
    
    /**
     * 转换为Double类型
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 格式化变化值
     */
    private String formatChangeValue(Double change, String metricName) {
        if (change == null) {
            return "-";
        }
        
        String prefix = change >= 0 ? "+" : "";
        return prefix + formatMetricValue(metricName, change);
    }
    
    /**
     * 格式化百分比
     */
    private String formatPercentage(Double value) {
        if (value == null) {
            return "-";
        }
        
        DecimalFormat df = new DecimalFormat("#.##");
        return df.format(value) + "%";
    }
}
