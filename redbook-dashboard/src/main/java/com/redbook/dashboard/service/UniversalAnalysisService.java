package com.redbook.dashboard.service;

import com.redbook.dashboard.domain.entity.AnalysisDataEntity;
import com.redbook.dashboard.domain.enums.AnalysisTypeEnum;
import com.redbook.dashboard.domain.enums.BusinessTypeEnum;
import com.redbook.dashboard.domain.enums.DimensionLevelEnum;
import com.redbook.dashboard.domain.vo.ComparisonResult;
import com.redbook.dashboard.domain.vo.RankingItem;
import com.redbook.dashboard.domain.vo.TrendPoint;
import com.redbook.dashboard.handler.MetricsHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用数据分析服务
 * 提供统一的数据分析接口，支持多种业务类型和分析维度
 * 通过策略模式和配置驱动实现灵活的数据分析功能
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Slf4j
@Service
public class UniversalAnalysisService {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private AnalysisDataService analysisDataService;
    
    /**
     * 指标处理器缓存
     */
    private final Map<BusinessTypeEnum, MetricsHandler> handlerCache = new HashMap<>();
    
    /**
     * 执行趋势分析
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param metricName 指标名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param filters 过滤条件
     * @return 趋势分析结果
     */
    public List<TrendPoint> analyzeTrend(BusinessTypeEnum businessType,
                                       DimensionLevelEnum dimensionLevel,
                                       String metricName,
                                       String startDate,
                                       String endDate,
                                       Map<String, Object> filters) {
        log.info("执行趋势分析: businessType={}, dimensionLevel={}, metricName={}, startDate={}, endDate={}", 
                businessType, dimensionLevel, metricName, startDate, endDate);
        
        try {
            // 获取指标处理器
            MetricsHandler handler = getMetricsHandler(businessType);
            
            // 查询数据
            List<AnalysisDataEntity> dataList = analysisDataService.queryAnalysisData(
                    businessType, dimensionLevel, startDate, endDate, filters);
            
            // 执行趋势分析
            List<TrendPoint> trendPoints = handler.calculateTrend(dataList, metricName);
            
            // 设置趋势信息
            for (int i = 1; i < trendPoints.size(); i++) {
                TrendPoint current = trendPoints.get(i);
                TrendPoint previous = trendPoints.get(i - 1);
                current.setTrendInfo(previous.getValue());
            }
            
            log.info("趋势分析完成，返回{}个数据点", trendPoints.size());
            return trendPoints;
            
        } catch (Exception e) {
            log.error("趋势分析失败", e);
            throw new RuntimeException("趋势分析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行排行分析
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param rankBy 排序依据指标
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 返回数量限制
     * @param filters 过滤条件
     * @return 排行分析结果
     */
    public List<RankingItem> analyzeRanking(BusinessTypeEnum businessType,
                                          DimensionLevelEnum dimensionLevel,
                                          String rankBy,
                                          String startDate,
                                          String endDate,
                                          Integer limit,
                                          Map<String, Object> filters) {
        log.info("执行排行分析: businessType={}, dimensionLevel={}, rankBy={}, limit={}", 
                businessType, dimensionLevel, rankBy, limit);
        
        try {
            // 获取指标处理器
            MetricsHandler handler = getMetricsHandler(businessType);
            
            // 查询数据
            List<AnalysisDataEntity> dataList = analysisDataService.queryAnalysisData(
                    businessType, dimensionLevel, startDate, endDate, filters);
            
            // 执行排行分析
            List<RankingItem> rankings = handler.calculateRanking(dataList, rankBy, limit);
            
            // 设置指标显示信息
            rankings.forEach(item -> {
                item.setMetricName(rankBy);
                item.setMetricDisplayName(handler.getMetricDisplayName(rankBy));
            });
            
            log.info("排行分析完成，返回{}个排行项", rankings.size());
            return rankings;
            
        } catch (Exception e) {
            log.error("排行分析失败", e);
            throw new RuntimeException("排行分析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行对比分析
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param metricName 指标名称
     * @param currentStartDate 当前期开始日期
     * @param currentEndDate 当前期结束日期
     * @param previousStartDate 对比期开始日期
     * @param previousEndDate 对比期结束日期
     * @param filters 过滤条件
     * @return 对比分析结果
     */
    public ComparisonResult analyzeComparison(BusinessTypeEnum businessType,
                                            DimensionLevelEnum dimensionLevel,
                                            String metricName,
                                            String currentStartDate,
                                            String currentEndDate,
                                            String previousStartDate,
                                            String previousEndDate,
                                            Map<String, Object> filters) {
        log.info("执行对比分析: businessType={}, dimensionLevel={}, metricName={}", 
                businessType, dimensionLevel, metricName);
        
        try {
            // 获取指标处理器
            MetricsHandler handler = getMetricsHandler(businessType);
            
            // 查询当前期数据
            List<AnalysisDataEntity> currentData = analysisDataService.queryAnalysisData(
                    businessType, dimensionLevel, currentStartDate, currentEndDate, filters);
            
            // 查询对比期数据
            List<AnalysisDataEntity> previousData = analysisDataService.queryAnalysisData(
                    businessType, dimensionLevel, previousStartDate, previousEndDate, filters);
            
            // 执行对比分析
            ComparisonResult result = handler.calculateComparison(currentData, previousData, metricName);
            
            // 设置指标显示信息
            result.setMetricName(metricName);
            result.setMetricDisplayName(handler.getMetricDisplayName(metricName));
            result.setCurrentPeriod(currentStartDate + " 至 " + currentEndDate);
            result.setPreviousPeriod(previousStartDate + " 至 " + previousEndDate);
            result.setCurrentDataCount(currentData.size());
            result.setPreviousDataCount(previousData.size());
            
            // 计算显著性水平
            result.calculateSignificance();
            
            log.info("对比分析完成: 当前期={}, 对比期={}, 变化率={}%", 
                    result.getCurrentValue(), result.getPreviousValue(), result.getChangeRate());
            return result;
            
        } catch (Exception e) {
            log.error("对比分析失败", e);
            throw new RuntimeException("对比分析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行汇总统计
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param metricName 指标名称
     * @param aggregationType 汇总类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param filters 过滤条件
     * @return 汇总结果
     */
    public Object analyzeSummary(BusinessTypeEnum businessType,
                               DimensionLevelEnum dimensionLevel,
                               String metricName,
                               String aggregationType,
                               String startDate,
                               String endDate,
                               Map<String, Object> filters) {
        log.info("执行汇总统计: businessType={}, dimensionLevel={}, metricName={}, aggregationType={}", 
                businessType, dimensionLevel, metricName, aggregationType);
        
        try {
            // 获取指标处理器
            MetricsHandler handler = getMetricsHandler(businessType);
            
            // 查询数据
            List<AnalysisDataEntity> dataList = analysisDataService.queryAnalysisData(
                    businessType, dimensionLevel, startDate, endDate, filters);
            
            // 执行汇总计算
            Object result = handler.calculateAggregation(dataList, metricName, aggregationType);
            
            log.info("汇总统计完成: 结果={}", result);
            return result;
            
        } catch (Exception e) {
            log.error("汇总统计失败", e);
            throw new RuntimeException("汇总统计失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取支持的指标列表
     * 
     * @param businessType 业务类型
     * @return 支持的指标列表
     */
    public List<String> getSupportedMetrics(BusinessTypeEnum businessType) {
        try {
            MetricsHandler handler = getMetricsHandler(businessType);
            return handler.getSupportedMetrics();
        } catch (Exception e) {
            log.error("获取支持的指标列表失败", e);
            throw new RuntimeException("获取支持的指标列表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证分析参数
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param analysisType 分析类型
     * @param metricName 指标名称
     * @return 验证结果
     */
    public boolean validateAnalysisParams(BusinessTypeEnum businessType,
                                        DimensionLevelEnum dimensionLevel,
                                        AnalysisTypeEnum analysisType,
                                        String metricName) {
        try {
            // 检查业务类型
            if (businessType == null) {
                log.warn("业务类型不能为空");
                return false;
            }
            
            // 检查维度级别
            if (dimensionLevel == null) {
                log.warn("维度级别不能为空");
                return false;
            }
            
            // 检查分析类型
            if (analysisType == null) {
                log.warn("分析类型不能为空");
                return false;
            }
            
            // 检查指标名称
            if (!StringUtils.hasText(metricName)) {
                log.warn("指标名称不能为空");
                return false;
            }
            
            // 检查指标是否支持
            MetricsHandler handler = getMetricsHandler(businessType);
            if (!handler.getSupportedMetrics().contains(metricName)) {
                log.warn("业务类型{}不支持指标{}", businessType, metricName);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证分析参数失败", e);
            return false;
        }
    }
    
    /**
     * 获取指标处理器
     * 
     * @param businessType 业务类型
     * @return 指标处理器
     */
    private MetricsHandler getMetricsHandler(BusinessTypeEnum businessType) {
        // 从缓存中获取
        MetricsHandler handler = handlerCache.get(businessType);
        if (handler != null) {
            return handler;
        }
        
        // 从Spring容器中获取
        try {
            Class<? extends MetricsHandler> handlerClass = businessType.getHandlerClass();
            handler = applicationContext.getBean(handlerClass);
            
            // 缓存处理器
            handlerCache.put(businessType, handler);
            
            return handler;
        } catch (Exception e) {
            log.error("获取指标处理器失败: businessType={}", businessType, e);
            throw new RuntimeException("获取指标处理器失败: " + e.getMessage(), e);
        }
    }
}
