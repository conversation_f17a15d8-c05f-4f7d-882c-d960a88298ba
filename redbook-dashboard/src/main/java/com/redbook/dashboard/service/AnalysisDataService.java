package com.redbook.dashboard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redbook.dashboard.domain.entity.AnalysisDataEntity;
import com.redbook.dashboard.domain.enums.BusinessTypeEnum;
import com.redbook.dashboard.domain.enums.DimensionLevelEnum;
import com.redbook.dashboard.mapper.AnalysisDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 分析数据服务
 * 提供统一的数据访问接口，支持动态表名和多维度查询
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Slf4j
@Service
public class AnalysisDataService {
    
    @Autowired
    private AnalysisDataMapper analysisDataMapper;
    
    /**
     * 查询分析数据
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param filters 过滤条件
     * @return 数据列表
     */
    public List<AnalysisDataEntity> queryAnalysisData(BusinessTypeEnum businessType,
                                                     DimensionLevelEnum dimensionLevel,
                                                     String startDate,
                                                     String endDate,
                                                     Map<String, Object> filters) {
        log.info("查询分析数据: businessType={}, dimensionLevel={}, startDate={}, endDate={}", 
                businessType, dimensionLevel, startDate, endDate);
        
        try {
            // 构建查询条件
            QueryWrapper<AnalysisDataEntity> queryWrapper = buildQueryWrapper(
                    businessType, dimensionLevel, startDate, endDate, filters);
            
            // 设置动态表名
            String tableName = businessType.getTableName(dimensionLevel);
            
            // 执行查询
            List<AnalysisDataEntity> dataList = analysisDataMapper.selectListWithTableName(tableName, queryWrapper);
            
            log.info("查询完成，返回{}条数据", dataList.size());
            return dataList;
            
        } catch (Exception e) {
            log.error("查询分析数据失败", e);
            throw new RuntimeException("查询分析数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 分页查询分析数据
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param filters 过滤条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页数据
     */
    public IPage<AnalysisDataEntity> queryAnalysisDataPage(BusinessTypeEnum businessType,
                                                          DimensionLevelEnum dimensionLevel,
                                                          String startDate,
                                                          String endDate,
                                                          Map<String, Object> filters,
                                                          Integer pageNum,
                                                          Integer pageSize) {
        log.info("分页查询分析数据: businessType={}, dimensionLevel={}, pageNum={}, pageSize={}", 
                businessType, dimensionLevel, pageNum, pageSize);
        
        try {
            // 构建查询条件
            QueryWrapper<AnalysisDataEntity> queryWrapper = buildQueryWrapper(
                    businessType, dimensionLevel, startDate, endDate, filters);
            
            // 设置动态表名
            String tableName = businessType.getTableName(dimensionLevel);
            
            // 创建分页对象
            Page<AnalysisDataEntity> page = new Page<>(pageNum, pageSize);
            
            // 执行分页查询
            IPage<AnalysisDataEntity> result = analysisDataMapper.selectPageWithTableName(tableName, page, queryWrapper);
            
            log.info("分页查询完成，返回{}条数据，总计{}条", result.getRecords().size(), result.getTotal());
            return result;
            
        } catch (Exception e) {
            log.error("分页查询分析数据失败", e);
            throw new RuntimeException("分页查询分析数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 统计分析数据数量
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param filters 过滤条件
     * @return 数据数量
     */
    public Long countAnalysisData(BusinessTypeEnum businessType,
                                DimensionLevelEnum dimensionLevel,
                                String startDate,
                                String endDate,
                                Map<String, Object> filters) {
        log.info("统计分析数据数量: businessType={}, dimensionLevel={}", businessType, dimensionLevel);
        
        try {
            // 构建查询条件
            QueryWrapper<AnalysisDataEntity> queryWrapper = buildQueryWrapper(
                    businessType, dimensionLevel, startDate, endDate, filters);
            
            // 设置动态表名
            String tableName = businessType.getTableName(dimensionLevel);
            
            // 执行统计查询
            Long count = analysisDataMapper.selectCountWithTableName(tableName, queryWrapper);
            
            log.info("统计完成，数据数量: {}", count);
            return count;
            
        } catch (Exception e) {
            log.error("统计分析数据数量失败", e);
            throw new RuntimeException("统计分析数据数量失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询维度值列表
     * 用于获取某个维度的所有可选值（如所有省份、所有城市等）
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param dimensionField 维度字段名
     * @param filters 过滤条件
     * @return 维度值列表
     */
    public List<String> queryDimensionValues(BusinessTypeEnum businessType,
                                           DimensionLevelEnum dimensionLevel,
                                           String dimensionField,
                                           Map<String, Object> filters) {
        log.info("查询维度值列表: businessType={}, dimensionLevel={}, dimensionField={}", 
                businessType, dimensionLevel, dimensionField);
        
        try {
            // 设置动态表名
            String tableName = businessType.getTableName(dimensionLevel);
            
            // 构建基础查询条件
            QueryWrapper<AnalysisDataEntity> queryWrapper = new QueryWrapper<>();
            
            // 添加过滤条件
            if (filters != null && !filters.isEmpty()) {
                addFiltersToQuery(queryWrapper, filters);
            }
            
            // 执行查询
            List<String> values = analysisDataMapper.selectDistinctValues(tableName, dimensionField, queryWrapper);
            
            log.info("查询维度值完成，返回{}个值", values.size());
            return values;
            
        } catch (Exception e) {
            log.error("查询维度值列表失败", e);
            throw new RuntimeException("查询维度值列表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建查询条件
     * 
     * @param businessType 业务类型
     * @param dimensionLevel 维度级别
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param filters 过滤条件
     * @return 查询条件
     */
    private QueryWrapper<AnalysisDataEntity> buildQueryWrapper(BusinessTypeEnum businessType,
                                                              DimensionLevelEnum dimensionLevel,
                                                              String startDate,
                                                              String endDate,
                                                              Map<String, Object> filters) {
        QueryWrapper<AnalysisDataEntity> queryWrapper = new QueryWrapper<>();
        
        // 添加业务类型条件
        if (businessType != null) {
            queryWrapper.eq("business_type", businessType.name());
        }
        
        // 添加维度级别条件
        if (dimensionLevel != null) {
            queryWrapper.eq("dimension_level", dimensionLevel.name());
        }
        
        // 添加时间范围条件
        if (StringUtils.hasText(startDate)) {
            queryWrapper.ge("study_date", startDate);
        }
        if (StringUtils.hasText(endDate)) {
            queryWrapper.le("study_date", endDate);
        }
        
        // 添加自定义过滤条件
        if (filters != null && !filters.isEmpty()) {
            addFiltersToQuery(queryWrapper, filters);
        }
        
        // 默认按日期排序
        queryWrapper.orderByAsc("study_date");
        
        return queryWrapper;
    }
    
    /**
     * 添加过滤条件到查询中
     * 
     * @param queryWrapper 查询条件构建器
     * @param filters 过滤条件
     */
    private void addFiltersToQuery(QueryWrapper<AnalysisDataEntity> queryWrapper, Map<String, Object> filters) {
        for (Map.Entry<String, Object> entry : filters.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value == null) {
                continue;
            }
            
            // 处理不同类型的过滤条件
            if (value instanceof List) {
                // 列表类型，使用IN条件
                List<?> valueList = (List<?>) value;
                if (!valueList.isEmpty()) {
                    queryWrapper.in(convertToColumnName(key), valueList);
                }
            } else if (key.endsWith("_like")) {
                // 模糊查询
                String columnName = convertToColumnName(key.substring(0, key.length() - 5));
                queryWrapper.like(columnName, value.toString());
            } else if (key.endsWith("_ge")) {
                // 大于等于
                String columnName = convertToColumnName(key.substring(0, key.length() - 3));
                queryWrapper.ge(columnName, value);
            } else if (key.endsWith("_le")) {
                // 小于等于
                String columnName = convertToColumnName(key.substring(0, key.length() - 3));
                queryWrapper.le(columnName, value);
            } else if (key.endsWith("_gt")) {
                // 大于
                String columnName = convertToColumnName(key.substring(0, key.length() - 3));
                queryWrapper.gt(columnName, value);
            } else if (key.endsWith("_lt")) {
                // 小于
                String columnName = convertToColumnName(key.substring(0, key.length() - 3));
                queryWrapper.lt(columnName, value);
            } else {
                // 等于条件
                queryWrapper.eq(convertToColumnName(key), value);
            }
        }
    }
    
    /**
     * 将字段名转换为数据库列名
     * 
     * @param fieldName 字段名
     * @return 数据库列名
     */
    private String convertToColumnName(String fieldName) {
        // 简单的驼峰转下划线
        return fieldName.replaceAll("([A-Z])", "_$1").toLowerCase();
    }
}
