package com.redbook.dashboard.config;

import com.redbook.dashboard.domain.enums.BusinessTypeEnum;
import com.redbook.dashboard.domain.enums.DimensionLevelEnum;
import com.redbook.dashboard.handler.MetricsHandler;
import com.redbook.dashboard.handler.StudyMetricsHandler;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分析配置测试类
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@SpringBootTest
public class AnalysisConfigTest {
    
    @Autowired
    private AnalysisConfig analysisConfig;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Test
    public void testAnalysisConfigInitialization() {
        assertNotNull(analysisConfig, "AnalysisConfig应该被正确注入");
    }
    
    @Test
    public void testBusinessTypeConfiguration() {
        // 测试业务类型配置
        for (BusinessTypeEnum businessType : BusinessTypeEnum.values()) {
            Map<String, Object> info = analysisConfig.getBusinessTypeInfo(businessType);
            
            assertNotNull(info, "业务类型信息不应为空: " + businessType);
            assertNotNull(info.get("name"), "业务类型名称不应为空: " + businessType);
            assertNotNull(info.get("description"), "业务类型描述不应为空: " + businessType);
            assertNotNull(info.get("tableNamePattern"), "表名模式不应为空: " + businessType);
            assertNotNull(info.get("handlerClass"), "处理器类不应为空: " + businessType);
            assertNotNull(info.get("defaultMetrics"), "默认指标不应为空: " + businessType);
        }
    }
    
    @Test
    public void testDimensionLevelConfiguration() {
        // 测试维度级别配置
        for (DimensionLevelEnum dimensionLevel : DimensionLevelEnum.values()) {
            Map<String, Object> info = analysisConfig.getDimensionLevelInfo(dimensionLevel);
            
            assertNotNull(info, "维度级别信息不应为空: " + dimensionLevel);
            assertNotNull(info.get("name"), "维度级别名称不应为空: " + dimensionLevel);
            assertNotNull(info.get("level"), "维度级别标识不应为空: " + dimensionLevel);
            assertNotNull(info.get("description"), "维度级别描述不应为空: " + dimensionLevel);
            assertTrue((Integer) info.get("hierarchyLevel") >= 0, "层级深度应该大于等于0: " + dimensionLevel);
        }
    }
    
    @Test
    public void testTableNameGeneration() {
        // 测试表名生成
        BusinessTypeEnum businessType = BusinessTypeEnum.STUDY;
        DimensionLevelEnum dimensionLevel = DimensionLevelEnum.PROVINCE;
        
        String tableName = businessType.getTableName(dimensionLevel);
        assertNotNull(tableName, "表名不应为空");
        assertTrue(tableName.contains("province"), "表名应包含维度级别");
        assertTrue(tableName.contains("study"), "表名应包含业务类型");
    }
    
    @Test
    public void testStudyMetricsHandlerExists() {
        // 测试学习数据处理器是否存在
        StudyMetricsHandler handler = applicationContext.getBean(StudyMetricsHandler.class);
        assertNotNull(handler, "StudyMetricsHandler应该被正确注册");
        
        // 测试支持的指标
        assertFalse(handler.getSupportedMetrics().isEmpty(), "应该支持至少一个指标");
        assertTrue(handler.getSupportedMetrics().contains("quizAvgScore"), "应该支持quizAvgScore指标");
    }
    
    @Test
    public void testMetricsHandlerRegistry() {
        // 测试指标处理器注册表
        Map<BusinessTypeEnum, MetricsHandler> registry = analysisConfig.metricsHandlerRegistry();
        assertNotNull(registry, "处理器注册表不应为空");
        
        // 至少应该注册了学习数据处理器
        assertTrue(registry.containsKey(BusinessTypeEnum.STUDY), "应该注册了学习数据处理器");
        assertNotNull(registry.get(BusinessTypeEnum.STUDY), "学习数据处理器不应为空");
    }
    
    @Test
    public void testConfigOverview() {
        // 测试配置概览
        Map<String, Object> overview = analysisConfig.getAllConfigInfo();
        assertNotNull(overview, "配置概览不应为空");
        
        assertTrue(overview.containsKey("businessTypes"), "应该包含业务类型配置");
        assertTrue(overview.containsKey("dimensionLevels"), "应该包含维度级别配置");
        assertTrue(overview.containsKey("statistics"), "应该包含统计信息");
        
        @SuppressWarnings("unchecked")
        Map<String, Object> statistics = (Map<String, Object>) overview.get("statistics");
        assertTrue((Integer) statistics.get("totalBusinessTypes") > 0, "业务类型数量应该大于0");
        assertTrue((Integer) statistics.get("totalDimensionLevels") > 0, "维度级别数量应该大于0");
    }
}
