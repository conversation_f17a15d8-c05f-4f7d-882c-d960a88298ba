# 前端API快速参考

## 🚀 快速开始

### 1. 枚举值速查

```javascript
// 业务类型 (目前只有STUDY可用)
BusinessType.STUDY = 'STUDY'

// 维度级别 (按层级排序)
DimensionLevel.COUNTRY = 'COUNTRY'    // 全国
DimensionLevel.PROVINCE = 'PROVINCE'  // 省份  
DimensionLevel.CITY = 'CITY'         // 城市
DimensionLevel.ALIAS = 'ALIAS'       // 体验中心
DimensionLevel.SHOP = 'SHOP'         // 专卖店
DimensionLevel.CLASS = 'CLASS'       // 班级
DimensionLevel.USER = 'USER'         // 用户

// 学习指标
StudyMetrics.QUIZ_AVG_SCORE = 'quizAvgScore'              // 测验平均分
StudyMetrics.IMPROVE_SCORE_RATE = 'improveScoreRate'      // 提分率
StudyMetrics.STUDY_EFFICIENCY_MEAN = 'studyEfficiencyMean' // 学习效率
StudyMetrics.STUDY_EFFECT_TIME_DAY = 'studyEffectTimeDay' // 日学习时长
```

### 2. 核心API接口

```javascript
// 趋势分析
GET /api/analysis/trend?businessType=STUDY&dimensionLevel=PROVINCE&metricName=quizAvgScore&startDate=2024-01-01&endDate=2024-12-31

// 排行分析  
GET /api/analysis/ranking?businessType=STUDY&dimensionLevel=CITY&rankBy=studyEfficiencyMean&limit=10&startDate=2024-01-01&endDate=2024-12-31

// 对比分析
GET /api/analysis/comparison?businessType=STUDY&dimensionLevel=PROVINCE&metricName=improveScoreRate&currentStartDate=2024-12-01&currentEndDate=2024-12-31&previousStartDate=2024-11-01&previousEndDate=2024-11-30

// 汇总统计
GET /api/analysis/summary?businessType=STUDY&dimensionLevel=COUNTRY&metricName=studyEffectTimeDay&aggregationType=AVG&startDate=2024-01-01&endDate=2024-12-31
```

### 3. 页面数据接口

```javascript
// 获取指标下拉框数据
POST /dashboard/metrics
Body: businessType=STUDY

// 获取维度值下拉框数据  
POST /dashboard/dimension-values
Body: businessType=STUDY&dimensionLevel=PROVINCE&dimensionField=province
```

## 📋 参数说明

### 必填参数
- `businessType`: 业务类型枚举 (目前只支持 'STUDY')
- `dimensionLevel`: 维度级别枚举
- `metricName`: 指标名称 (学习数据使用StudyMetrics枚举)
- `startDate`: 开始日期 (格式: YYYY-MM-DD)
- `endDate`: 结束日期 (格式: YYYY-MM-DD)

### 可选参数
- `provinceId`: 省份ID过滤
- `cityId`: 城市ID过滤  
- `aliasId`: 体验中心ID过滤
- `limit`: 返回数量限制 (排行分析)
- `aggregationType`: 汇总类型 SUM/AVG/MAX/MIN/COUNT (汇总统计)

## 🎯 常用组合

### 省份学习效率排行榜
```javascript
{
  businessType: 'STUDY',
  dimensionLevel: 'PROVINCE', 
  rankBy: 'studyEfficiencyMean',
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  limit: 10
}
```

### 城市测验成绩趋势
```javascript
{
  businessType: 'STUDY',
  dimensionLevel: 'CITY',
  metricName: 'quizAvgScore', 
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  provinceId: '110000'  // 限制在北京市
}
```

### 本月vs上月提分率对比
```javascript
{
  businessType: 'STUDY',
  dimensionLevel: 'PROVINCE',
  metricName: 'improveScoreRate',
  currentStartDate: '2024-12-01',
  currentEndDate: '2024-12-31', 
  previousStartDate: '2024-11-01',
  previousEndDate: '2024-11-30'
}
```

## ⚠️ 注意事项

1. **枚举值大小写敏感**: 必须使用准确的枚举值
2. **日期格式**: 统一使用 YYYY-MM-DD 格式
3. **业务类型限制**: 目前只有 STUDY 类型完全可用
4. **维度层级**: 注意维度的层级关系，避免无意义的组合
5. **数据权限**: 返回数据可能受用户权限限制

## 🔧 调试技巧

### 验证枚举值
```javascript
// 检查业务类型是否有效
const validBusinessTypes = ['STUDY', 'RENEWAL', 'TASK', 'MARKETING'];
if (!validBusinessTypes.includes(businessType)) {
  console.error('无效的业务类型:', businessType);
}
```

### 错误处理
```javascript
try {
  const response = await fetch(apiUrl);
  const result = await response.json();
  
  if (result.code !== 200) {
    throw new Error(result.msg || '请求失败');
  }
  
  return result.data;
} catch (error) {
  console.error('API调用失败:', error.message);
  // 显示用户友好的错误信息
}
```

## 📞 获取帮助

- 完整文档: `FRONTEND_API_GUIDE.md`
- 代码示例: `frontend-examples.js`  
- 配置查询: `GET /api/analysis/config/overview`
- 联系后端团队获取技术支持
