/**
 * 前端API调用示例
 * 展示如何使用枚举值调用通用数据分析框架的API
 */

// ==================== 枚举值定义 ====================

// 业务类型枚举
const BusinessType = {
    STUDY: 'STUDY',         // 学习数据
    RENEWAL: 'RENEWAL',     // 续费数据（预留）
    TASK: 'TASK',          // 任务数据（预留）
    MARKETING: 'MARKETING'  // 营销数据（预留）
};

// 维度级别枚举
const DimensionLevel = {
    USER: 'USER',           // 用户维度
    COUNTRY: 'COUNTRY',     // 全国维度
    PROVINCE: 'PROVINCE',   // 省份维度
    CITY: 'CITY',          // 城市维度
    ALIAS: 'ALIAS',        // 体验中心维度
    SHOP: 'SHOP',          // 专卖店维度
    CLASS: 'CLASS'         // 班级维度
};

// 分析类型枚举
const AnalysisType = {
    LIST: 'LIST',           // 列表查询
    TREND: 'TREND',         // 趋势分析
    RANKING: 'RANKING',     // 排行分析
    COMPARISON: 'COMPARISON', // 对比分析
    SUMMARY: 'SUMMARY'      // 汇总统计
};

// 学习数据指标枚举
const StudyMetrics = {
    QUIZ_AVG_SCORE: 'quizAvgScore',              // 测验平均分
    IMPROVE_SCORE_RATE: 'improveScoreRate',      // 提分率
    IMPROVE_SCORE_MEAN: 'improveScoreMean',      // 平均提分
    STUDY_EFFICIENCY_MEAN: 'studyEfficiencyMean', // 学习效率均值
    STUDY_EFFECT_TIME_DAY: 'studyEffectTimeDay', // 日有效学习时长
    STUDY_NEW_NUM_DAY: 'studyNewNumDay',         // 日新学数量
    STUDY_SPEED: 'studySpeed',                   // 学习速度
    REVIEW_FREQUENCY_DAY: 'reviewFrequencyDay',  // 日复习频次
    REVIEW_NUM_DAY: 'reviewNumDay',              // 日复习数量
    REVIEW_MULTIPLIER_MEAN: 'reviewMultiplierMean' // 复习倍数均值
};

// 汇总类型枚举
const AggregationType = {
    SUM: 'SUM',     // 求和
    AVG: 'AVG',     // 平均值
    MAX: 'MAX',     // 最大值
    MIN: 'MIN',     // 最小值
    COUNT: 'COUNT'  // 计数
};

// ==================== API调用示例 ====================

/**
 * 示例1: 获取省份维度的学习效率趋势分析
 */
async function getTrendAnalysis() {
    const params = {
        businessType: BusinessType.STUDY,                    // 使用枚举: 学习数据
        dimensionLevel: DimensionLevel.PROVINCE,             // 使用枚举: 省份维度
        metricName: StudyMetrics.STUDY_EFFICIENCY_MEAN,      // 使用枚举: 学习效率均值
        startDate: '2024-01-01',
        endDate: '2024-12-31'
    };
    
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`/api/analysis/trend?${queryString}`);
    const data = await response.json();
    
    console.log('趋势分析结果:', data);
    return data;
}

/**
 * 示例2: 获取城市维度的测验平均分排行榜
 */
async function getRankingAnalysis() {
    const params = {
        businessType: BusinessType.STUDY,           // 使用枚举: 学习数据
        dimensionLevel: DimensionLevel.CITY,        // 使用枚举: 城市维度
        rankBy: StudyMetrics.QUIZ_AVG_SCORE,        // 使用枚举: 测验平均分
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        limit: 10,
        provinceId: '110000'  // 可选: 只看北京市的城市排行
    };
    
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`/api/analysis/ranking?${queryString}`);
    const data = await response.json();
    
    console.log('排行分析结果:', data);
    return data;
}

/**
 * 示例3: 对比本月和上月的提分率
 */
async function getComparisonAnalysis() {
    const params = {
        businessType: BusinessType.STUDY,              // 使用枚举: 学习数据
        dimensionLevel: DimensionLevel.PROVINCE,       // 使用枚举: 省份维度
        metricName: StudyMetrics.IMPROVE_SCORE_RATE,   // 使用枚举: 提分率
        currentStartDate: '2024-12-01',
        currentEndDate: '2024-12-31',
        previousStartDate: '2024-11-01',
        previousEndDate: '2024-11-30'
    };
    
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`/api/analysis/comparison?${queryString}`);
    const data = await response.json();
    
    console.log('对比分析结果:', data);
    return data;
}

/**
 * 示例4: 获取全国日有效学习时长的平均值
 */
async function getSummaryAnalysis() {
    const params = {
        businessType: BusinessType.STUDY,                  // 使用枚举: 学习数据
        dimensionLevel: DimensionLevel.COUNTRY,            // 使用枚举: 全国维度
        metricName: StudyMetrics.STUDY_EFFECT_TIME_DAY,    // 使用枚举: 日有效学习时长
        aggregationType: AggregationType.AVG,              // 使用枚举: 平均值
        startDate: '2024-01-01',
        endDate: '2024-12-31'
    };
    
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`/api/analysis/summary?${queryString}`);
    const data = await response.json();
    
    console.log('汇总统计结果:', data);
    return data;
}

// ==================== 页面数据组装示例 ====================

/**
 * 示例5: 获取业务类型下拉框数据
 */
async function getBusinessTypeOptions() {
    const response = await fetch('/api/analysis/config/business-types');
    const businessTypes = await response.json();
    
    // 转换为前端下拉框需要的格式
    const options = businessTypes.map(item => ({
        value: item.code,
        label: item.description,
        disabled: item.code !== BusinessType.STUDY  // 只有学习数据可用，其他预留
    }));
    
    console.log('业务类型选项:', options);
    return options;
}

/**
 * 示例6: 获取维度级别下拉框数据
 */
async function getDimensionLevelOptions() {
    const response = await fetch('/api/analysis/config/dimension-levels');
    const dimensionLevels = await response.json();
    
    // 按层级排序并转换格式
    const options = dimensionLevels
        .sort((a, b) => a.hierarchyLevel - b.hierarchyLevel)
        .map(item => ({
            value: item.code,
            label: item.description,
            hierarchyLevel: item.hierarchyLevel,
            isGeographic: item.isGeographic,
            isOrganization: item.isOrganization
        }));
    
    console.log('维度级别选项:', options);
    return options;
}

/**
 * 示例7: 根据业务类型获取指标下拉框数据
 */
async function getMetricOptions(businessType = BusinessType.STUDY) {
    const formData = new FormData();
    formData.append('businessType', businessType);
    
    const response = await fetch('/dashboard/metrics', {
        method: 'POST',
        body: formData
    });
    
    const result = await response.json();
    if (result.code === 200) {
        console.log('指标选项:', result.data);
        return result.data;
    }
    
    throw new Error(result.msg || '获取指标失败');
}

/**
 * 示例8: 获取省份下拉框数据
 */
async function getProvinceOptions() {
    const formData = new FormData();
    formData.append('businessType', BusinessType.STUDY);
    formData.append('dimensionLevel', DimensionLevel.PROVINCE);
    formData.append('dimensionField', 'province');
    
    const response = await fetch('/dashboard/dimension-values', {
        method: 'POST',
        body: formData
    });
    
    const result = await response.json();
    if (result.code === 200) {
        console.log('省份选项:', result.data);
        return result.data;
    }
    
    throw new Error(result.msg || '获取省份列表失败');
}

// ==================== 实用工具函数 ====================

/**
 * 验证枚举值是否有效
 */
function validateEnumValue(value, enumObject, enumName) {
    const validValues = Object.values(enumObject);
    if (!validValues.includes(value)) {
        throw new Error(`无效的${enumName}值: ${value}，有效值为: ${validValues.join(', ')}`);
    }
    return true;
}

/**
 * 构建分析请求参数（带验证）
 */
function buildAnalysisParams(options) {
    const {
        businessType,
        dimensionLevel,
        metricName,
        analysisType,
        startDate,
        endDate,
        ...otherParams
    } = options;
    
    // 验证枚举值
    validateEnumValue(businessType, BusinessType, '业务类型');
    validateEnumValue(dimensionLevel, DimensionLevel, '维度级别');
    
    if (analysisType) {
        validateEnumValue(analysisType, AnalysisType, '分析类型');
    }
    
    // 验证学习数据指标
    if (businessType === BusinessType.STUDY && metricName) {
        validateEnumValue(metricName, StudyMetrics, '学习指标');
    }
    
    // 验证日期格式
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (startDate && !dateRegex.test(startDate)) {
        throw new Error('开始日期格式错误，应为YYYY-MM-DD');
    }
    if (endDate && !dateRegex.test(endDate)) {
        throw new Error('结束日期格式错误，应为YYYY-MM-DD');
    }
    
    return {
        businessType,
        dimensionLevel,
        metricName,
        analysisType,
        startDate,
        endDate,
        ...otherParams
    };
}

/**
 * 通用API调用函数
 */
async function callAnalysisAPI(endpoint, params) {
    try {
        // 验证参数
        const validatedParams = buildAnalysisParams(params);
        
        // 构建URL
        const queryString = new URLSearchParams(validatedParams).toString();
        const url = `/api/analysis/${endpoint}?${queryString}`;
        
        // 发起请求
        const response = await fetch(url);
        const result = await response.json();
        
        if (response.ok) {
            return result;
        } else {
            throw new Error(result.msg || `API调用失败: ${response.status}`);
        }
    } catch (error) {
        console.error(`调用${endpoint}接口失败:`, error);
        throw error;
    }
}

// ==================== 使用示例 ====================

// 页面加载时初始化下拉框数据
async function initPageData() {
    try {
        const [businessTypes, dimensionLevels, metrics] = await Promise.all([
            getBusinessTypeOptions(),
            getDimensionLevelOptions(),
            getMetricOptions(BusinessType.STUDY)
        ]);
        
        console.log('页面初始化数据加载完成');
        return { businessTypes, dimensionLevels, metrics };
    } catch (error) {
        console.error('页面初始化失败:', error);
        throw error;
    }
}

// 执行分析的完整示例
async function performAnalysis() {
    try {
        // 使用枚举值构建参数
        const analysisParams = {
            businessType: BusinessType.STUDY,
            dimensionLevel: DimensionLevel.PROVINCE,
            metricName: StudyMetrics.STUDY_EFFICIENCY_MEAN,
            startDate: '2024-01-01',
            endDate: '2024-12-31'
        };
        
        // 调用趋势分析
        const trendData = await callAnalysisAPI('trend', analysisParams);
        console.log('趋势分析完成:', trendData);
        
        // 调用排行分析
        const rankingData = await callAnalysisAPI('ranking', {
            ...analysisParams,
            rankBy: StudyMetrics.QUIZ_AVG_SCORE,
            limit: 10
        });
        console.log('排行分析完成:', rankingData);
        
    } catch (error) {
        console.error('分析执行失败:', error);
    }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        BusinessType,
        DimensionLevel,
        AnalysisType,
        StudyMetrics,
        AggregationType,
        callAnalysisAPI,
        buildAnalysisParams,
        validateEnumValue,
        initPageData
    };
}
