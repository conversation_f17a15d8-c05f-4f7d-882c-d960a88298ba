{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "RedBook Dashboard API Tests", "description": "通用数据分析框架API测试集合\n\n使用说明：\n1. 设置环境变量 {{baseUrl}} 为API基础地址\n2. 设置环境变量 {{authToken}} 为认证Token\n3. 运行整个集合或单个请求进行测试", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "配置信息接口", "item": [{"name": "获取业务类型列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "pm.test(\"Business types have required fields\", function () {", "    const jsonData = pm.response.json();", "    if (jsonData.length > 0) {", "        const firstItem = jsonData[0];", "        pm.expect(firstItem).to.have.property('code');", "        pm.expect(firstItem).to.have.property('description');", "        pm.expect(firstItem).to.have.property('tableNamePattern');", "        pm.expect(firstItem).to.have.property('handlerClass');", "        pm.expect(firstItem).to.have.property('defaultMetrics');", "    }", "});", "", "pm.test(\"Response time is less than 3000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/config/business-types", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "config", "business-types"]}}}, {"name": "获取维度级别列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "pm.test(\"Dimension levels have required fields\", function () {", "    const jsonData = pm.response.json();", "    if (jsonData.length > 0) {", "        const firstItem = jsonData[0];", "        pm.expect(firstItem).to.have.property('code');", "        pm.expect(firstItem).to.have.property('level');", "        pm.expect(firstItem).to.have.property('description');", "        pm.expect(firstItem).to.have.property('hierarchyLevel');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/config/dimension-levels", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "config", "dimension-levels"]}}}, {"name": "获取学习数据指标", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "pm.test(\"Metrics array is not empty\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.length).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/config/business-types/STUDY/metrics", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "config", "business-types", "STUDY", "metrics"]}}}, {"name": "获取配置概览", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Overview has required fields\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('businessTypes');", "    pm.expect(jsonData).to.have.property('dimensionLevels');", "    pm.expect(jsonData).to.have.property('statistics');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/config/overview", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "config", "overview"]}}}]}, {"name": "数据分析接口", "item": [{"name": "趋势分析", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "pm.test(\"Trend points have required fields\", function () {", "    const jsonData = pm.response.json();", "    if (jsonData.length > 0) {", "        const firstPoint = jsonData[0];", "        pm.expect(firstPoint).to.have.property('date');", "        pm.expect(firstPoint).to.have.property('value');", "        pm.expect(firstPoint).to.have.property('formattedValue');", "    }", "});", "", "pm.test(\"Response time is acceptable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/trend?businessType=STUDY&dimensionLevel=PROVINCE&metricName=quizAvgScore&startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "trend"], "query": [{"key": "businessType", "value": "STUDY"}, {"key": "dimensionLevel", "value": "PROVINCE"}, {"key": "metricName", "value": "quizAvgScore"}, {"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}, {"name": "排行分析", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "pm.test(\"Ranking items have required fields\", function () {", "    const jsonData = pm.response.json();", "    if (jsonData.length > 0) {", "        const firstItem = jsonData[0];", "        pm.expect(firstItem).to.have.property('rank');", "        pm.expect(firstItem).to.have.property('name');", "        pm.expect(firstItem).to.have.property('value');", "        pm.expect(firstItem).to.have.property('formattedValue');", "    }", "});", "", "pm.test(\"First item has rank 1\", function () {", "    const jsonData = pm.response.json();", "    if (jsonData.length > 0) {", "        pm.expect(jsonData[0].rank).to.equal(1);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/ranking?businessType=STUDY&dimensionLevel=CITY&rankBy=studyEfficiencyMean&startDate=2024-01-01&endDate=2024-01-31&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "ranking"], "query": [{"key": "businessType", "value": "STUDY"}, {"key": "dimensionLevel", "value": "CITY"}, {"key": "rankBy", "value": "studyEfficiencyMean"}, {"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}, {"key": "limit", "value": "10"}]}}}, {"name": "对比分析", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Comparison result has required fields\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('currentValue');", "    pm.expect(jsonData).to.have.property('previousValue');", "    pm.expect(jsonData).to.have.property('change');", "    pm.expect(jsonData).to.have.property('changeRate');", "    pm.expect(jsonData).to.have.property('trend');", "});", "", "pm.test(\"Trend value is valid\", function () {", "    const jsonData = pm.response.json();", "    const validTrends = ['UP', 'DOWN', 'STABLE'];", "    pm.expect(validTrends).to.include(jsonData.trend);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/comparison?businessType=STUDY&dimensionLevel=PROVINCE&metricName=improveScoreRate&currentStartDate=2024-02-01&currentEndDate=2024-02-29&previousStartDate=2024-01-01&previousEndDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "comparison"], "query": [{"key": "businessType", "value": "STUDY"}, {"key": "dimensionLevel", "value": "PROVINCE"}, {"key": "metricName", "value": "improveScoreRate"}, {"key": "currentStartDate", "value": "2024-02-01"}, {"key": "currentEndDate", "value": "2024-02-29"}, {"key": "previousStartDate", "value": "2024-01-01"}, {"key": "previousEndDate", "value": "2024-01-31"}]}}}, {"name": "汇总统计", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is a number\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.a('number');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/summary?businessType=STUDY&dimensionLevel=COUNTRY&metricName=studyEffectTimeDay&aggregationType=AVG&startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "summary"], "query": [{"key": "businessType", "value": "STUDY"}, {"key": "dimensionLevel", "value": "COUNTRY"}, {"key": "metricName", "value": "studyEffectTimeDay"}, {"key": "aggregationType", "value": "AVG"}, {"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}]}, {"name": "Web页面数据接口", "item": [{"name": "获取指标下拉框数据", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.code).to.equal(200);", "});", "", "pm.test(\"Data is array with options\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.be.an('array');", "    if (jsonData.data.length > 0) {", "        const firstOption = jsonData.data[0];", "        pm.expect(firstOption).to.have.property('value');", "        pm.expect(firstOption).to.have.property('label');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "businessType", "value": "STUDY", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/dashboard/metrics", "host": ["{{baseUrl}}"], "path": ["dashboard", "metrics"]}}}, {"name": "获取维度值下拉框数据", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.code).to.equal(200);", "});", "", "pm.test(\"Data is array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "businessType", "value": "STUDY", "type": "text"}, {"key": "dimensionLevel", "value": "PROVINCE", "type": "text"}, {"key": "dimensionField", "value": "province", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/dashboard/dimension-values", "host": ["{{baseUrl}}"], "path": ["dashboard", "dimension-values"]}}}]}, {"name": "异常场景测试", "item": [{"name": "无效业务类型", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 4xx or 5xx\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 404, 500]);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/trend?businessType=INVALID_TYPE&dimensionLevel=PROVINCE&metricName=quizAvgScore&startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "trend"], "query": [{"key": "businessType", "value": "INVALID_TYPE"}, {"key": "dimensionLevel", "value": "PROVINCE"}, {"key": "metricName", "value": "quizAvgScore"}, {"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}, {"name": "缺少必填参数", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 4xx\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 422]);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/analysis/trend?businessType=STUDY&metricName=quizAvgScore&startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "analysis", "trend"], "query": [{"key": "businessType", "value": "STUDY"}, {"key": "metricName", "value": "quizAvgScore"}, {"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 设置默认环境变量", "if (!pm.environment.get('baseUrl')) {", "    pm.environment.set('baseUrl', 'http://localhost:8080');", "}", "", "if (!pm.environment.get('authToken')) {", "    pm.environment.set('authToken', 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVlNTRiZDU4LWNkN2EtNGNjNS05NTU5LTQ4OTc2NjdlNmE4YSJ9.4UZMb3RwHNllCIvG9Zm6hbO5bix1-GrA22iMHkcITkOafYQULeZNLNNRsX1gavURVhKEUQV974cGcwvFjEh1aA');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局测试 - 记录响应时间", "pm.test(\"Response time is recorded\", function () {", "    console.log(`Response time: ${pm.response.responseTime}ms`);", "});"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "authToken", "value": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVlNTRiZDU4LWNkN2EtNGNjNS05NTU5LTQ4OTc2NjdlNmE4YSJ9.4UZMb3RwHNllCIvG9Zm6hbO5bix1-GrA22iMHkcITkOafYQULeZNLNNRsX1gavURVhKEUQV974cGcwvFjEh1aA", "type": "string"}]}