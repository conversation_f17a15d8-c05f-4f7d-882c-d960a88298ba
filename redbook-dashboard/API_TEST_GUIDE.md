# API测试指南

## 概述

本文档提供了通用数据分析框架API的完整测试方案，包括多种测试工具和脚本，帮助开发者验证API功能和性能。

## 认证信息

所有API请求都需要使用Bearer Token进行身份验证：

```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVlNTRiZDU4LWNkN2EtNGNjNS05NTU5LTQ4OTc2NjdlNmE4YSJ9.4UZMb3RwHNllCIvG9Zm6hbO5bix1-GrA22iMHkcITkOafYQULeZNLNNRsX1gavURVhKEUQV974cGcwvFjEh1aA
```

## 测试工具

### 1. Node.js 测试脚本

**文件**: `api-test-suite.js`

**安装依赖**:
```bash
npm install axios chalk
```

**运行测试**:
```bash
# 默认环境 (localhost:8080)
node api-test-suite.js

# 指定环境
BASE_URL=http://staging.redbook.com node api-test-suite.js
```

**特性**:
- ✅ 完整的API覆盖测试
- ✅ 响应时间性能监控
- ✅ 数据结构验证
- ✅ 异常场景测试
- ✅ 彩色输出和详细报告

### 2. Python 测试脚本

**文件**: `api_test_suite.py`

**安装依赖**:
```bash
pip install requests colorama
```

**运行测试**:
```bash
python api_test_suite.py
```

**特性**:
- ✅ 与Node.js版本功能一致
- ✅ Python开发者友好
- ✅ 详细的错误信息
- ✅ 性能统计分析

### 3. Postman 集合

**文件**: `RedBook_Dashboard_API_Tests.postman_collection.json`

**使用步骤**:
1. 打开Postman
2. 导入集合文件
3. 设置环境变量:
   - `baseUrl`: API基础地址 (如: http://localhost:8080)
   - `authToken`: 认证Token
4. 运行整个集合或单个请求

**特性**:
- ✅ 图形化界面
- ✅ 自动化测试脚本
- ✅ 环境变量管理
- ✅ 测试报告生成

## 快速测试命令 (curl)

### 配置信息接口

```bash
# 设置认证Token
TOKEN="eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVlNTRiZDU4LWNkN2EtNGNjNS05NTU5LTQ4OTc2NjdlNmE4YSJ9.4UZMb3RwHNllCIvG9Zm6hbO5bix1-GrA22iMHkcITkOafYQULeZNLNNRsX1gavURVhKEUQV974cGcwvFjEh1aA"
BASE_URL="http://localhost:8080"

# 获取业务类型列表
curl -H "Authorization: Bearer $TOKEN" \
     "$BASE_URL/api/analysis/config/business-types"

# 获取维度级别列表
curl -H "Authorization: Bearer $TOKEN" \
     "$BASE_URL/api/analysis/config/dimension-levels"

# 获取学习数据指标
curl -H "Authorization: Bearer $TOKEN" \
     "$BASE_URL/api/analysis/config/business-types/STUDY/metrics"

# 获取配置概览
curl -H "Authorization: Bearer $TOKEN" \
     "$BASE_URL/api/analysis/config/overview"
```

### 数据分析接口

```bash
# 趋势分析
curl -H "Authorization: Bearer $TOKEN" \
     "$BASE_URL/api/analysis/trend?businessType=STUDY&dimensionLevel=PROVINCE&metricName=quizAvgScore&startDate=2024-01-01&endDate=2024-01-31"

# 排行分析
curl -H "Authorization: Bearer $TOKEN" \
     "$BASE_URL/api/analysis/ranking?businessType=STUDY&dimensionLevel=CITY&rankBy=studyEfficiencyMean&startDate=2024-01-01&endDate=2024-01-31&limit=10"

# 对比分析
curl -H "Authorization: Bearer $TOKEN" \
     "$BASE_URL/api/analysis/comparison?businessType=STUDY&dimensionLevel=PROVINCE&metricName=improveScoreRate&currentStartDate=2024-02-01&currentEndDate=2024-02-29&previousStartDate=2024-01-01&previousEndDate=2024-01-31"

# 汇总统计
curl -H "Authorization: Bearer $TOKEN" \
     "$BASE_URL/api/analysis/summary?businessType=STUDY&dimensionLevel=COUNTRY&metricName=studyEffectTimeDay&aggregationType=AVG&startDate=2024-01-01&endDate=2024-01-31"
```

### Web页面数据接口

```bash
# 获取指标下拉框数据
curl -X POST \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "businessType=STUDY" \
     "$BASE_URL/dashboard/metrics"

# 获取维度值下拉框数据
curl -X POST \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "businessType=STUDY&dimensionLevel=PROVINCE&dimensionField=province" \
     "$BASE_URL/dashboard/dimension-values"
```

## 测试覆盖范围

### ✅ 配置信息接口 (5个)
- 获取业务类型列表
- 获取维度级别列表
- 获取分析类型列表
- 获取指定业务类型的指标
- 获取配置概览

### ✅ 数据分析接口 (4个)
- 趋势分析
- 排行分析
- 对比分析
- 汇总统计

### ✅ Web页面数据接口 (2个)
- 获取指标下拉框数据
- 获取维度值下拉框数据

### ✅ 异常场景测试 (3个)
- 无效业务类型
- 无效日期格式
- 缺少必填参数

## 测试结果示例

### Node.js 测试输出
```
🚀 开始运行API测试套件...

📋 配置信息接口测试
✓ 获取业务类型列表 (245ms)
    Found 4 business types
✓ 获取维度级别列表 (189ms)
    Found 7 dimension levels
✓ 获取分析类型列表 (156ms)
    Found 5 analysis types
✓ 获取学习数据指标 (178ms)
    Found 10 metrics for STUDY business type
✓ 获取配置概览 (234ms)
    Config overview loaded successfully

📊 数据分析接口测试
✓ 趋势分析 (1245ms)
    Found 31 trend points
✓ 排行分析 (987ms)
    Found 10 ranking items
✓ 对比分析 (1156ms)
    Comparison: UP, Change: 3.47%
✓ 汇总统计 (876ms)
    Summary result: 120.5

🌐 Web页面数据接口测试
✓ 获取指标下拉框数据 (234ms)
    Found 10 metric options
✓ 获取维度值下拉框数据 (345ms)
    Found 34 dimension values

⚠️  异常场景测试
✓ 无效业务类型 (123ms)
    Expected error: 400
✓ 无效日期格式 (145ms)
    Expected error: 400
✓ 缺少必填参数 (134ms)
    Expected error: 400

📈 测试结果摘要
==================================================
总测试数量: 14
通过测试: 14
失败测试: 0
成功率: 100.00%

⏱️  性能统计
==================================================
总耗时: 5847ms
平均响应时间: 417.64ms
最快响应时间: 123ms
最慢响应时间: 1245ms

🎉 测试完成!
```

## 性能基准

### 响应时间阈值
- **配置接口**: < 500ms
- **分析接口**: < 3000ms
- **页面数据接口**: < 1000ms

### 性能监控
所有测试脚本都包含性能监控功能：
- 记录每个请求的响应时间
- 计算平均、最大、最小响应时间
- 超过阈值时发出警告

## 错误处理

### 常见错误场景
1. **认证失败** (401): Token无效或过期
2. **参数错误** (400): 枚举值错误、日期格式错误
3. **缺少参数** (400): 必填参数未提供
4. **服务器错误** (500): 内部服务错误

### 调试技巧
1. **检查Token**: 确保Token有效且未过期
2. **验证参数**: 使用枚举值验证参数正确性
3. **查看日志**: 检查服务器端日志获取详细错误信息
4. **网络检查**: 确认网络连接和防火墙设置

## 环境配置

### 开发环境
```bash
BASE_URL=http://localhost:8080
```

### 测试环境
```bash
BASE_URL=http://test.redbook.com
```

### 生产环境
```bash
BASE_URL=http://api.redbook.com
```

## 持续集成

### GitHub Actions 示例
```yaml
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install axios chalk
      - run: BASE_URL=${{ secrets.API_BASE_URL }} node api-test-suite.js
```

## 注意事项

1. **Token安全**: 不要在代码中硬编码Token，使用环境变量
2. **数据隔离**: 测试环境使用独立的数据库
3. **并发限制**: 避免过高的并发请求影响服务性能
4. **数据清理**: 测试后及时清理测试数据

## 联系方式

如有测试相关问题，请联系：
- 后端开发团队
- API文档维护者
- 测试工程师
