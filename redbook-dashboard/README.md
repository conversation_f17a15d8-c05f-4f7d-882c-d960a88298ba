# RedBook Dashboard 模块 - 通用数据分析框架

## 概述

RedBook Dashboard 是一个基于配置驱动的通用数据分析框架，支持多种业务类型、多维度分析和多种分析方法。该框架采用策略模式和枚举配置，实现了高度的可扩展性和可维护性。

## 特性

- **多业务类型支持**: 学习数据、续费数据、任务数据、营销数据等
- **多维度分析**: 用户、省份、城市、体验中心、专卖店、班级等维度
- **多种分析类型**: 趋势分析、排行分析、对比分析、汇总统计等
- **配置驱动**: 通过枚举配置管理业务类型和维度映射
- **策略模式**: 不同业务类型使用不同的指标处理器
- **动态表名**: 支持根据业务类型和维度级别动态生成表名
- **统一API**: 提供统一的分析接口，支持多种分析场景

## 项目结构

```
redbook-dashboard/
├── src/main/java/com/redbook/dashboard/
│   ├── config/                           # 配置层
│   │   └── AnalysisConfig.java          # 分析配置管理
│   │
│   ├── controller/                       # 控制器层
│   │   ├── AnalysisConfigController.java # 配置信息API
│   │   └── UniversalAnalysisController.java # 通用分析API
│   │
│   ├── domain/                          # 领域模型层
│   │   ├── entity/                      # 实体类
│   │   │   ├── AnalysisDataEntity.java  # 通用分析数据实体
│   │   │   └── DashBoardBaseEntity.java # 基础实体类
│   │   │
│   │   ├── enums/                       # 枚举类
│   │   │   ├── AnalysisTypeEnum.java    # 分析类型枚举
│   │   │   ├── BusinessTypeEnum.java    # 业务类型枚举
│   │   │   └── DimensionLevelEnum.java  # 维度级别枚举
│   │   │
│   │   └── vo/                          # 值对象
│   │       ├── ComparisonResult.java    # 对比分析结果
│   │       ├── RankingItem.java         # 排行分析项
│   │       └── TrendPoint.java          # 趋势分析点
│   │
│   ├── handler/                         # 处理器层（策略模式）
│   │   ├── MetricsHandler.java          # 指标处理器接口
│   │   ├── StudyMetricsHandler.java     # 学习数据处理器
│   │   ├── RenewalMetricsHandler.java   # 续费数据处理器（预留）
│   │   ├── TaskMetricsHandler.java      # 任务数据处理器（预留）
│   │   └── MarketingMetricsHandler.java # 营销数据处理器（预留）
│   │
│   ├── mapper/                          # 数据访问层
│   │   └── AnalysisDataMapper.java      # 分析数据Mapper
│   │
│   └── service/                         # 服务层
│       ├── AnalysisDataService.java     # 数据访问服务
│       └── UniversalAnalysisService.java # 通用分析服务
│
├── UNIVERSAL_ANALYSIS_FRAMEWORK.md      # 框架使用文档
├── PROJECT_STRUCTURE.md                 # 项目结构说明
└── README.md                            # 模块说明
```

## 核心功能

### 1. 多业务类型支持

- **学习数据分析** (STUDY): 支持学习时长、成绩、效率等指标
- **续费数据分析** (RENEWAL): 支持续费率、续费金额等指标（预留）
- **任务数据分析** (TASK): 支持任务完成率、任务得分等指标（预留）
- **营销数据分析** (MARKETING): 支持转化率、点击率等指标（预留）

### 2. 多维度分析

- **用户维度** (USER): 按用户个体统计
- **全国维度** (COUNTRY): 全国汇总数据
- **省份维度** (PROVINCE): 按省份统计
- **城市维度** (CITY): 按城市统计
- **体验中心维度** (ALIAS): 按体验中心统计
- **专卖店维度** (SHOP): 按专卖店统计
- **班级维度** (CLASS): 按班级统计

### 3. 多种分析类型

- **趋势分析** (TREND): 时间序列数据的趋势变化分析
- **排行分析** (RANKING): 按指定指标进行排序的排行榜分析
- **对比分析** (COMPARISON): 不同时间段或不同维度的数据对比分析
- **汇总统计** (SUMMARY): 数据的汇总统计信息
- **列表查询** (LIST): 基础的数据列表展示

## API接口

### 趋势分析
```http
GET /api/analysis/trend?businessType=STUDY&dimensionLevel=PROVINCE&metricName=quizAvgScore&startDate=2024-01-01&endDate=2024-12-31
```

### 排行分析
```http
GET /api/analysis/ranking?businessType=STUDY&dimensionLevel=CITY&rankBy=studyEfficiencyMean&limit=10
```

### 对比分析
```http
GET /api/analysis/comparison?businessType=STUDY&dimensionLevel=PROVINCE&metricName=quizAvgScore&currentStartDate=2024-12-01&currentEndDate=2024-12-31&previousStartDate=2024-11-01&previousEndDate=2024-11-30
```

### 配置信息查询
```http
GET /api/analysis/config/business-types
GET /api/analysis/config/dimension-levels
GET /api/analysis/config/business-types/STUDY/metrics
```

## 快速开始

1. **确保数据库表结构**: 表名格式为 `intelligence_{level}_{business}_day`
2. **配置数据源**: 在 `application-dashboard.yml` 中配置数据库连接
3. **启动应用**: 框架会自动注册和初始化所有组件
4. **调用API**: 使用统一的分析接口进行数据分析

## 扩展指南

### 添加新的业务类型

1. 在 `BusinessTypeEnum` 中添加新的枚举值
2. 创建对应的 `MetricsHandler` 实现类
3. 在 `AnalysisDataEntity` 中添加相应的字段（如需要）

### 添加新的维度级别

1. 在 `DimensionLevelEnum` 中添加新的枚举值
2. 配置相应的字段映射关系
3. 在 `AnalysisDataEntity` 中添加对应字段

## 技术栈

- **后端框架**: Spring Boot 2.x
- **数据库**: MySQL 8.0+
- **ORM框架**: MyBatis Plus
- **API文档**: Swagger 2
- **日志框架**: Logback
- **构建工具**: Maven

## 文档

- [通用分析框架详细文档](UNIVERSAL_ANALYSIS_FRAMEWORK.md)
- [项目结构说明](PROJECT_STRUCTURE.md)

## 注意事项

1. 确保数据库表结构与 `AnalysisDataEntity` 字段对应
2. 新增Handler需要在Spring容器中注册为Bean
3. 枚举配置修改后需要重新编译部署
4. API接口变更需要更新Swagger文档
