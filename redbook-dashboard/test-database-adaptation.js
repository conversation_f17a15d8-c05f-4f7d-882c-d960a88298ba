/**
 * 数据库结构适配测试脚本
 * 验证基于实际数据库字段的查询功能
 * 
 * 运行方式：node test-database-adaptation.js
 */

const axios = require('axios');

// 配置信息
const CONFIG = {
    BASE_URL: 'http://localhost:8080',
    AUTH_TOKEN: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVlNTRiZDU4LWNkN2EtNGNjNS05NTU5LTQ4OTc2NjdlNmE4YSJ9.4UZMb3RwHNllCIvG9Zm6hbO5bix1-GrA22iMHkcITkOafYQULeZNLNNRsX1gavURVhKEUQV974cGcwvFjEh1aA'
};

// 枚举值
const BusinessType = { STUDY: 'STUDY' };
const DimensionLevel = {
    COUNTRY: 'COUNTRY',
    PROVINCE: 'PROVINCE', 
    CITY: 'CITY',
    ALIAS: 'ALIAS',
    SHOP: 'SHOP',
    CLASS: 'CLASS',
    USER: 'USER'
};
const StudyMetrics = {
    QUIZ_AVG_SCORE: 'quizAvgScore',
    IMPROVE_SCORE_RATE: 'improveScoreRate',
    STUDY_EFFICIENCY_MEAN: 'studyEfficiencyMean',
    STUDY_EFFECT_TIME_DAY: 'studyEffectTimeDay'
};

// 创建HTTP客户端
function createHttpClient() {
    return axios.create({
        baseURL: CONFIG.BASE_URL,
        timeout: 10000,
        headers: {
            'Authorization': `Bearer ${CONFIG.AUTH_TOKEN}`,
            'Content-Type': 'application/json'
        }
    });
}

// 测试用例
const testCases = [
    {
        name: '全国维度趋势分析',
        type: 'trend',
        params: {
            businessType: BusinessType.STUDY,
            dimensionLevel: DimensionLevel.COUNTRY,
            metricName: StudyMetrics.QUIZ_AVG_SCORE,
            startDate: '2024-01-01',
            endDate: '2024-01-31'
        }
    },
    {
        name: '省份维度趋势分析',
        type: 'trend',
        params: {
            businessType: BusinessType.STUDY,
            dimensionLevel: DimensionLevel.PROVINCE,
            metricName: StudyMetrics.STUDY_EFFICIENCY_MEAN,
            startDate: '2024-01-01',
            endDate: '2024-01-31',
            // 测试新的数据库结构
            dt: '2024-01-31'  // 可选的分区字段
        }
    },
    {
        name: '城市维度排行分析',
        type: 'ranking',
        params: {
            businessType: BusinessType.STUDY,
            dimensionLevel: DimensionLevel.CITY,
            rankBy: StudyMetrics.IMPROVE_SCORE_RATE,
            startDate: '2024-01-01',
            endDate: '2024-01-31',
            limit: 10,
            provinceId: '110000'  // 测试过滤条件
        }
    },
    {
        name: '体验中心维度排行分析',
        type: 'ranking',
        params: {
            businessType: BusinessType.STUDY,
            dimensionLevel: DimensionLevel.ALIAS,
            rankBy: StudyMetrics.QUIZ_AVG_SCORE,
            startDate: '2024-01-01',
            endDate: '2024-01-31',
            limit: 5,
            cityId: '110100'  // 测试城市过滤
        }
    },
    {
        name: '全国维度汇总统计',
        type: 'summary',
        params: {
            businessType: BusinessType.STUDY,
            dimensionLevel: DimensionLevel.COUNTRY,
            metricName: StudyMetrics.STUDY_EFFECT_TIME_DAY,
            aggregationType: 'AVG',
            startDate: '2024-01-01',
            endDate: '2024-01-31'
        }
    },
    {
        name: '省份维度对比分析',
        type: 'comparison',
        params: {
            businessType: BusinessType.STUDY,
            dimensionLevel: DimensionLevel.PROVINCE,
            metricName: StudyMetrics.IMPROVE_SCORE_RATE,
            currentStartDate: '2024-02-01',
            currentEndDate: '2024-02-29',
            previousStartDate: '2024-01-01',
            previousEndDate: '2024-01-31'
        }
    }
];

// 执行测试
async function runTest(testCase) {
    const client = createHttpClient();
    const startTime = Date.now();
    
    try {
        console.log(`\n🧪 测试: ${testCase.name}`);
        console.log(`   类型: ${testCase.type}`);
        console.log(`   参数: ${JSON.stringify(testCase.params, null, 2)}`);
        
        let response;
        switch (testCase.type) {
            case 'trend':
                response = await client.get('/api/analysis/trend', { params: testCase.params });
                break;
            case 'ranking':
                response = await client.get('/api/analysis/ranking', { params: testCase.params });
                break;
            case 'summary':
                response = await client.get('/api/analysis/summary', { params: testCase.params });
                break;
            case 'comparison':
                response = await client.get('/api/analysis/comparison', { params: testCase.params });
                break;
            default:
                throw new Error(`未知的测试类型: ${testCase.type}`);
        }
        
        const responseTime = Date.now() - startTime;
        
        // 验证响应
        if (response.status !== 200) {
            throw new Error(`HTTP状态码错误: ${response.status}`);
        }
        
        const data = response.data;
        
        // 根据测试类型验证数据结构
        switch (testCase.type) {
            case 'trend':
                if (!Array.isArray(data)) {
                    throw new Error('趋势分析结果应该是数组');
                }
                if (data.length > 0) {
                    const firstPoint = data[0];
                    if (!firstPoint.date || !('value' in firstPoint)) {
                        throw new Error('趋势数据点缺少必要字段');
                    }
                }
                console.log(`   ✅ 成功 - 返回${data.length}个数据点 (${responseTime}ms)`);
                break;
                
            case 'ranking':
                if (!Array.isArray(data)) {
                    throw new Error('排行分析结果应该是数组');
                }
                if (data.length > 0) {
                    const firstItem = data[0];
                    if (!('rank' in firstItem) || !firstItem.name || !('value' in firstItem)) {
                        throw new Error('排行数据缺少必要字段');
                    }
                }
                console.log(`   ✅ 成功 - 返回${data.length}个排行项 (${responseTime}ms)`);
                break;
                
            case 'summary':
                if (typeof data !== 'number') {
                    throw new Error('汇总统计结果应该是数字');
                }
                console.log(`   ✅ 成功 - 汇总结果: ${data} (${responseTime}ms)`);
                break;
                
            case 'comparison':
                if (!data || typeof data !== 'object') {
                    throw new Error('对比分析结果应该是对象');
                }
                if (!('currentValue' in data) || !('previousValue' in data) || !('trend' in data)) {
                    throw new Error('对比分析结果缺少必要字段');
                }
                console.log(`   ✅ 成功 - 趋势: ${data.trend}, 变化率: ${data.changeRate}% (${responseTime}ms)`);
                break;
        }
        
        return { success: true, responseTime, dataSize: Array.isArray(data) ? data.length : 1 };
        
    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.log(`   ❌ 失败 - ${error.message} (${responseTime}ms)`);
        return { success: false, responseTime, error: error.message };
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始数据库结构适配测试...\n');
    console.log(`📍 测试环境: ${CONFIG.BASE_URL}`);
    console.log(`🔑 认证Token: ${CONFIG.AUTH_TOKEN.substring(0, 20)}...`);
    
    const results = [];
    let totalTests = 0;
    let passedTests = 0;
    let totalTime = 0;
    
    for (const testCase of testCases) {
        const result = await runTest(testCase);
        results.push({ testCase, result });
        
        totalTests++;
        if (result.success) {
            passedTests++;
        }
        totalTime += result.responseTime;
        
        // 测试间隔
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // 输出测试摘要
    console.log('\n' + '='.repeat(60));
    console.log('📊 测试结果摘要');
    console.log('='.repeat(60));
    console.log(`总测试数量: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(2)}%`);
    console.log(`总耗时: ${totalTime}ms`);
    console.log(`平均响应时间: ${(totalTime / totalTests).toFixed(2)}ms`);
    
    // 输出失败的测试
    const failedTests = results.filter(r => !r.result.success);
    if (failedTests.length > 0) {
        console.log('\n❌ 失败的测试:');
        failedTests.forEach(({ testCase, result }) => {
            console.log(`   ${testCase.name}: ${result.error}`);
        });
    }
    
    console.log('\n🎉 测试完成!');
    
    // 退出码
    process.exit(failedTests.length > 0 ? 1 : 0);
}

// 启动测试
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('❌ 测试套件执行失败:', error);
        process.exit(1);
    });
}

module.exports = { runAllTests, testCases };
