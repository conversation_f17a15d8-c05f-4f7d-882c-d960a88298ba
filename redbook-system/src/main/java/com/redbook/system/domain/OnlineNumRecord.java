package com.redbook.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.system.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


@ApiModel("用户在线数量记录（每2分钟）")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OnlineNumRecord {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "onlineNum",value= "在线人数" )
    private Long id;
    @ApiModelProperty(name = "onlineNum",value= "在线人数" )
    private Long onlineNum;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "recordDate",value= "记录日期" )
    private Date recordDate;


    @ApiModelProperty(name = "minuteStr",value= "分钟时间" )
    private String minuteStr;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "recordTime",value= "记录时间" )
    private Date recordTime;

    public void setOnlineNum(Long onlineNum)
    {
        this.onlineNum = onlineNum;
    }

    public String getMinuteStr() {
        return minuteStr;
    }

    public void setMinuteStr(String minuteStr) {
        this.minuteStr = minuteStr;
    }

    public Long getOnlineNum()
    {
        return onlineNum;
    }
    public void setRecordDate(Date recordDate)
    {
        this.recordDate = recordDate;
    }

    public Date getRecordDate()
    {
        return recordDate;
    }
    public void setRecordTime(Date recordTime)
    {
        this.recordTime = recordTime;
        if(recordTime!=null){
            minuteStr= DateUtil.dateToString(recordTime,DateUtil.LONG_TIME_FORMAT);
        }
    }

    public Date getRecordTime()
    {
        return recordTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("onlineNum", getOnlineNum())
            .append("recordDate", getRecordDate())
            .append("recordTime", getRecordTime())
            .toString();
    }
}
