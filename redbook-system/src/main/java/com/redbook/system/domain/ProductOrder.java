package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 商品订单对象 product_order
 * 
 * <AUTHOR>
 * @date 2023-05-09
 */
@ApiModel("商品订单")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProductOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 用户 */
    @TableField(value = "user_id")
    @Excel(name = "用户")
    @ApiModelProperty(name = "userId",value= "用户" )
    private Long userId;


    /** 代理商id */
    @TableField(value = "agent_id")
    @Excel(name = "代理商id")
    @ApiModelProperty(name = "agentId",value= "代理商id" )
    private Long agentId;


    /** 订单号 */
    @TableField(value = "order_no")
    @Excel(name = "订单号")
    @ApiModelProperty(name = "orderNo",value= "订单号" )
    private String orderNo;


    /** 产品id */
    @TableField(value = "product_id")
    @Excel(name = "产品id")
    @ApiModelProperty(name = "productId",value= "产品id" )
    private Long productId;

    /** 产品价格 */
    @ApiModelProperty(name = "productPrice",value= "产品价格" )
    @TableField(exist = false)
    private BigDecimal productPrice;

    /** 产品顺序 */
    @ApiModelProperty(name = "productSort",value= "产品顺序" )
    @TableField(exist = false)
    private Integer productSort;

    /**
     * 满多少件免邮费
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "freeShippingCount", value = "满多少件免邮费 -1不免")
    private Integer freeShippingCount;

    /**
     * 运费
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "shippingFee", value = "运费")
    private BigDecimal shippingFee;
    /**
     * 运费类型 0 免邮 1 单件计费 2 多件计费
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "shippingType", value = "运费类型 0 免邮 1 单件计费 2 多件计费 3 到付")
    private Integer shippingType;

    @TableField(exist = false)
    @ApiModelProperty(name = "virtualGoods", value = "是否是虚拟商品")
    private Boolean virtualGoods;

    /** 产品类别id */
    @TableField(value = "product_category_id")
    @Excel(name = "产品类别id")
    @ApiModelProperty(name = "productCategoryId",value= "产品类别id" )
    private Long productCategoryId;

    /** 产品名称 */
    @TableField(value = "product_name")
    @Excel(name = "产品名称")
    @ApiModelProperty(name = "productName",value= "产品名称" )
    private String productName;

    /** 产品类别名称 */
    @TableField(value = "product_category_name")
    @Excel(name = "产品类别名称")
    @ApiModelProperty(name = "productCategoryName",value= "产品类别名称" )
    private String productCategoryName;


    /** 订购数量 */
    @TableField(value = "order_count")
    @Excel(name = "订购数量")
    @ApiModelProperty(name = "orderCount",value= "订购数量" )
    private Integer orderCount;


    /** 订购数量明细 */
    @TableField(value = "order_count_detail")
    @Excel(name = "订购数量明细")
    @ApiModelProperty(name = "orderCountDetail",value= "订购数量明细,web端拼接过来" )
    private String orderCountDetail;

    @TableField(exist = false)
    @ApiModelProperty(name = "orderCountDetailList",value= "订购数量明细，有规格的用这个" )
    private List<ProductSizeInventory> orderCountDetailList;

    /** 物流单号 */
    @TableField(value = "tracking_number")
    @Excel(name = "物流单号")
    @ApiModelProperty(name = "trackingNumber",value= "物流单号" )
    private String trackingNumber;


    /** 物流公司名 */
    @TableField(value = "tracking_name")
    @Excel(name = "物流公司名")
    @ApiModelProperty(name = "trackingName",value= "物流公司名" )
    private String trackingName;


    /** 收货人 */
    @TableField(value = "reciever")
    @Excel(name = "收货人")
    @ApiModelProperty(name = "reciever",value= "收货人" )
    private String reciever;


    /** 收货人电话 */
    @TableField(value = "reciever_phone")
    @Excel(name = "收货人电话")
    @ApiModelProperty(name = "recieverPhone",value= "收货人电话" )
    private String recieverPhone;


    /** 收货地址 */
    @TableField(value = "reciever_addess")
    @Excel(name = "收货地址")
    @ApiModelProperty(name = "recieverAddess",value= "收货地址" )
    private String recieverAddess;


    /** 1已完成 2取消（待发货0，待收货3） */
    @TableField(value = "status")
    @Excel(name = "1已完成 2取消", readConverterExp = "待发货0，待收货3")
    @ApiModelProperty(name = "status",value= "1已完成 2取消" )
    private Integer status;


    /** 付款金额 */
    @TableField(value = "pay_money")
    @Excel(name = "付款金额")
    @ApiModelProperty(name = "payMoney",value= "付款金额" )
    private BigDecimal payMoney;


    /** 款项类型 */
    @TableField(value = "pay_money_type")
    @Excel(name = "款项类型")
    @ApiModelProperty(name = "payMoneyType",value= "款项类型" )
    private Integer payMoneyType;


    /** 硬件id集合 */
    @TableField(value = "tablet_ids")
    @Excel(name = "硬件id集合")
    @ApiModelProperty(name = "tabletIds",value= "硬件id集合" )
    private String tabletIds;

    @TableField(value = "pay_exclusive_shop_id")
    @Excel(name = "付款专卖店id")
    @ApiModelProperty(name = "payExclusiveShopId",value= "付款专卖店id" )
    private Integer payExclusiveShopId;

    @TableField(value = "pay_exclusive_shop_money")
    @Excel(name = "专卖店支付金额")
    @ApiModelProperty(name = "payExclusiveShopMoney",value= "专卖店支付金额" )
    private BigDecimal payExclusiveShopMoney;


    /** 是否发送 N未发送  Y已发送 */
    @TableField(value = "is_sended")
    @Excel(name = "是否发送 N未发送  Y已发送")
    @ApiModelProperty(name = "isSended",value= "是否发送 N未发送  Y已发送" )
    private String isSended;


    /** 发货时间 */
    @TableField(value = "send_product_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发货时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "sendProductTime",value= "发货时间" )
    private Date sendProductTime;

    @TableField(value = "send_product_remarks")
    @Excel(name = "发货备注")
    @ApiModelProperty(name = "sendProductRemarks",value= "发货备注" )
    private String sendProductRemarks;


    /** 附件json格式 */
    @TableField(value = "extra")
    @Excel(name = "附件json格式")
    @ApiModelProperty(name = "extra",value= "附件json格式" )
    private String extra;
    @ApiModelProperty(name = "agentName", value = "代理商名称")
    private String agentName;
    @ApiModelProperty(name = "contactPerson", value = "代理商负责人")
    private String contactPerson;
    @Getter
    @ApiModelProperty(name = "signPerson", value = "代理商签约人")
    private String signPerson;
    @ApiModelProperty(name = "payInfo", value = "支付信息")
    private PayInfo payInfo;

    @ApiModelProperty(value = "查询专卖店订单记录", example = "false")
    private Boolean onlyExclusiveShopOrder;
    @ApiModelProperty(name = "freight", value = "运费（前端使用）")
    private Integer freight;


    public Integer getFreight() {
        return freight;
    }

    public void setFreight(Integer freight) {
        this.freight = freight;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setAgentId(Long agentId) 
    {
        this.agentId = agentId;
    }

    public Long getAgentId() 
    {
        return agentId;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    public void setOrderCount(Integer orderCount) 
    {
        this.orderCount = orderCount;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductCategoryName() {
        return productCategoryName;
    }

    public void setProductCategoryName(String productCategoryName) {
        this.productCategoryName = productCategoryName;
    }

    public Integer getOrderCount()
    {
        return orderCount;
    }
    public void setOrderCountDetail(String orderCountDetail) 
    {
        this.orderCountDetail = orderCountDetail;
    }

    public String getOrderCountDetail() 
    {
        return orderCountDetail;
    }
    public void setTrackingNumber(String trackingNumber) 
    {
        this.trackingNumber = trackingNumber;
    }

    public String getTrackingNumber() 
    {
        return trackingNumber;
    }
    public void setTrackingName(String trackingName) 
    {
        this.trackingName = trackingName;
    }

    public String getTrackingName() 
    {
        return trackingName;
    }
    public void setReciever(String reciever) 
    {
        this.reciever = reciever;
    }

    public String getReciever() 
    {
        return reciever;
    }
    public void setRecieverPhone(String recieverPhone) 
    {
        this.recieverPhone = recieverPhone;
    }

    public String getRecieverPhone() 
    {
        return recieverPhone;
    }
    public void setRecieverAddess(String recieverAddess) 
    {
        this.recieverAddess = recieverAddess;
    }

    public String getRecieverAddess() 
    {
        return recieverAddess;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setPayMoney(BigDecimal payMoney) 
    {
        this.payMoney = payMoney;
    }

    public BigDecimal getPayMoney() 
    {
        return payMoney;
    }
    public void setPayMoneyType(Integer payMoneyType) 
    {
        this.payMoneyType = payMoneyType;
    }

    public Integer getPayMoneyType() 
    {
        return payMoneyType;
    }
    public void setTabletIds(String tabletIds) 
    {
        this.tabletIds = tabletIds;
    }

    public String getTabletIds() 
    {
        return tabletIds;
    }
    public void setIsSended(String isSended) 
    {
        this.isSended = isSended;
    }

    public String getIsSended() 
    {
        return isSended;
    }

    public Date getSendProductTime() {
        return sendProductTime;
    }

    public void setSendProductTime(Date sendProductTime) {
        this.sendProductTime = sendProductTime;
    }

    public void setExtra(String extra)
    {
        this.extra = extra;
    }

    public String getExtra() 
    {
        return extra;
    }

    public Long getProductCategoryId() {
        return productCategoryId;
    }

    public void setProductCategoryId(Long productCategoryId) {
        this.productCategoryId = productCategoryId;
    }

    public List<ProductSizeInventory> getOrderCountDetailList() {
        return orderCountDetailList;
    }

    public void setOrderCountDetailList(List<ProductSizeInventory> orderCountDetailList) {
        this.orderCountDetailList = orderCountDetailList;
    }

    public PayInfo getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(PayInfo payInfo) {
        this.payInfo = payInfo;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public void setSignPerson(String signPerson) {
        this.signPerson = signPerson;
    }

    public String getSendProductRemarks() {
        return sendProductRemarks;
    }

    public void setSendProductRemarks(String sendProductRemarks) {
        this.sendProductRemarks = sendProductRemarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("agentId", getAgentId())
            .append("orderNo", getOrderNo())
            .append("productId", getProductId())
            .append("orderCount", getOrderCount())
            .append("orderCountDetail", getOrderCountDetail())
            .append("trackingNumber", getTrackingNumber())
            .append("trackingName", getTrackingName())
            .append("reciever", getReciever())
            .append("recieverPhone", getRecieverPhone())
            .append("recieverAddess", getRecieverAddess())
            .append("status", getStatus())
            .append("payMoney", getPayMoney())
            .append("payMoneyType", getPayMoneyType())
            .append("tabletIds", getTabletIds())
            .append("createTime", getCreateTime())
            .append("isSended", getIsSended())
            .append("sendProductTime", getSendProductTime())
            .append("extra", getExtra())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
