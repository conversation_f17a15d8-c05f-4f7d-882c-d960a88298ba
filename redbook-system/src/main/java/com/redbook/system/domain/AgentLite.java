package com.redbook.system.domain;

import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@ApiModel(value = "代理商简略信息", description = "AgentLite")
public class AgentLite extends BaseEntity {
    @ApiModelProperty(value = "代理商名称")
    private String agentName;
    @ApiModelProperty(value = "代理商等级")
    private String agentLevel;
    @ApiModelProperty(value = "代理商负责人")
    private String contactPerson;
    @ApiModelProperty(value = "代理商状态")
    private Integer status;
    @ApiModelProperty(value = "经理姓名列表 用“，”分割")
    private String managerNames;


    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentLevel() {
        return agentLevel;
    }

    public void setAgentLevel(String agentLevel) {
        this.agentLevel = agentLevel;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getManagerNames() {
        return managerNames;
    }

    public void setManagerNames(String managerNames) {
        this.managerNames = managerNames;
    }
}
