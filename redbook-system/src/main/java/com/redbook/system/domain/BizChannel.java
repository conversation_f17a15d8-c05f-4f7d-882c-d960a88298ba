package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 渠道对象 biz_channel
 * 
 * <AUTHOR>
 * @date 2024-03-22
 */
@ApiModel("渠道")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BizChannel extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 渠道id */
    @TableId(value = "channel_id", type = IdType.AUTO)
    private Long channelId;


    /** 区域代理商id */
    @TableField(value = "agent_id")
    @Excel(name = "区域代理商id")
    @ApiModelProperty(name = "agentId",value= "区域代理商id" )
    private Long agentId;


    /** 专卖店id */
    @TableField(value = "exclusive_shop_id")
    @Excel(name = "专卖店id")
    @ApiModelProperty(name = "exclusiveShopId",value= "专卖店id" )
    private Integer exclusiveShopId;


    /** 渠道名称 */
    @TableField(value = "channel_name")
    @Excel(name = "渠道名称")
    @ApiModelProperty(name = "channelName",value= "渠道名称" )
    private String channelName;


    /** 联系电话 */
    @TableField(value = "phone")
    @Excel(name = "联系电话")
    @ApiModelProperty(name = "phone",value= "联系电话" )
    private String phone;


    /** 状态：0停用，1正常 */
    @TableField(value = "status")
    @Excel(name = "状态：0停用，1正常")
    @ApiModelProperty(name = "status",value= "状态：0停用，1正常" )
    private Integer status;



    /** 创建者id */
    @TableField(value = "create_by_id")
    @Excel(name = "创建者id")
    @ApiModelProperty(name = "createById",value= "创建者id" )
    private Long createById;




    /** 更新者id */
    @TableField(value = "update_by_id")
    @Excel(name = "更新者id")
    @ApiModelProperty(name = "updateById",value= "更新者id" )
    private Long updateById;

}
