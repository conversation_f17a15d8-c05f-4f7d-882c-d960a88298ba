package com.redbook.system.domain.vo;

import com.redbook.common.core.domain.entity.AgentInfo;
import com.redbook.system.domain.Agent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
@ApiModel(value = "代理商详细信息", description = "AgentDetailVo")
@Data
@Builder
public class AgentDetailVo {
    @ApiModelProperty(value = "代理商信息")
    private Agent agent;
    @ApiModelProperty(value = "经营情况汇总")
    private AgentOperationVo operation;
    @ApiModelProperty(value = "其他签约区域")
    private List<AgentInfo> otherAgentList;

}
