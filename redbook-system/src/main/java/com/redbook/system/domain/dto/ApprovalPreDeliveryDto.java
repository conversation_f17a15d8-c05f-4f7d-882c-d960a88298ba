package com.redbook.system.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.redbook.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("提前发货申请审批")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalPreDeliveryDto {

    @ApiModelProperty(name = "productOrderList",value = "待发货的硬件订单列表")
    private List<OrderDetail> productOrderList;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderDetail{
        @ApiModelProperty("订单id")
        private Integer productOrderId;
        @ApiModelProperty("订单编号")
        private String productOrderNo;
        /** 代理商id */
        @TableField(value = "agent_id")
        @Excel(name = "代理商id")
        @ApiModelProperty(name = "agentId",value= "代理商id" )
        private Long agentId;
        @ApiModelProperty(name = "agentName", value = "代理商名称")
        private String agentName;
        /** 产品名称 */
        @TableField(value = "product_name")
        @Excel(name = "产品名称")
        @ApiModelProperty(name = "productName",value= "产品名称" )
        private String productName;
        /** 产品类别名称 */
        @TableField(value = "product_category_name")
        @Excel(name = "产品类别名称")
        @ApiModelProperty(name = "productCategoryName",value= "产品类别名称" )
        private String productCategoryName;
        /** 订购数量 */
        @TableField(value = "order_count")
        @Excel(name = "订购数量")
        @ApiModelProperty(name = "orderCount",value= "订购数量" )
        private Integer orderCount;

        /** 1已完成 2取消（待发货0，待收货3） */
        @TableField(value = "status")
        @Excel(name = "1已完成 2取消", readConverterExp = "待发货0，待收货3")
        @ApiModelProperty(name = "status",value= "1已完成 2取消" )
        private Integer status;
        @ApiModelProperty(name = "申请提前发货数量",value = "申请提前发货数量")
        //申请提前发货数量
        private Integer applyCount;

        @ApiModelProperty(name = "备注",value = "备注")
        private String remark;

    }
}

