package com.redbook.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;


/**
 * 会员手动延期
 * 
 * <AUTHOR>
 * @date 2023-06-13
 */
@ApiModel("会员手动延期")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class UserDeferredDto
{


    /** 用户id */
    @ApiModelProperty(name = "userId",value= "用户id" )
    private String userId;

    @ApiModelProperty(name = "userIds",value= "用户id列表" )
    private ArrayList<String> userIds;

    /** 会员类型 */
    @Excel(name = "会员类型")
    @ApiModelProperty(name = "memberType",value= "会员类型" )
    private Integer memberType;


    /** 小学学段到期日期，如果为空表示没有购买 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "小学学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage1ExpirationDate",value= "小学学段到期日期，如果为空表示没有购买" )
    private Date stage1ExpirationDate;


    /** 初中学段到期日期，如果为空表示没有购买 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初中学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage2ExpirationDate",value= "初中学段到期日期，如果为空表示没有购买" )
    private Date stage2ExpirationDate;


    /** 高中学段到期日期，如果为空表示没有购买 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "高中学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage3ExpirationDate",value= "高中学段到期日期，如果为空表示没有购买" )
    private Date stage3ExpirationDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "大学学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage4ExpirationDate", value = "大学学段到期日期，如果为空表示没有购买")
    private Date stage4ExpirationDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出国学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage5ExpirationDate", value = "出国学段到期日期，如果为空表示没有购买")
    private Date stage5ExpirationDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "小升初学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage11ExpirationDate", value = "小升初学段到期日期，如果为空表示没有购买")
    private Date stage11ExpirationDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初升高学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage21ExpirationDate", value = "初升高学段到期日期，如果为空表示没有购买")
    private Date stage21ExpirationDate;



    /** 到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "expirationDate",value= "到期日期" )
    private Date expirationDate;


    @ApiModelProperty(name = "remark",value= "备注" )
    private String remark;


    /** pc用户id */
    @ApiModelProperty(name = "pcUserId",value= "pc用户id" )
    private String pcUserId;
}
