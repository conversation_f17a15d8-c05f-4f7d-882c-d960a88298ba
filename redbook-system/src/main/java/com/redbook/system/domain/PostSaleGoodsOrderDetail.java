package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;


/**
 * 商品订单详情对象 post_sale_goods_order_detail
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@ApiModel("商品订单详情")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleGoodsOrderDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 订单id */
    @TableField(value = "order_id")
    @Excel(name = "订单id")
    @ApiModelProperty(name = "orderId",value= "订单id" )
    private Long orderId;


    /** 产品id */
    @TableField(value = "good_id")
    @Excel(name = "产品id")
    @ApiModelProperty(name = "goodId",value= "产品id" )
    private Integer goodId;

    /** 产品名称 */
    @TableField(value = "good_name")
    @Excel(name = "产品名称")
    @ApiModelProperty(name = "goodName",value= "产品名称" )
    private String goodName;


    /** 产品数量 */
    @TableField(value = "good_num")
    @Excel(name = "产品数量")
    @ApiModelProperty(name = "goodNum",value= "产品数量" )
    private Integer goodNum;


    @TableField(exist = false)
    @ApiModelProperty(name = "goodPrice",value= "单价" )
    private BigDecimal goodPrice;

    /** 金额，也就是总的价格 */
    @TableField(value = "total_money")
    @Excel(name = "金额，也就是总的价格")
    @ApiModelProperty(name = "totalMoney",value= "金额，也就是总的价格" )
    private BigDecimal totalMoney;

    @TableField(exist = false)
    @ApiModelProperty(name = "product",value= "商品明细" )
    private Product product;


    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setGoodId(Integer goodId)
    {
        this.goodId = goodId;
    }

    public Integer getGoodId()
    {
        return goodId;
    }
    public void setGoodNum(Integer goodNum)
    {
        this.goodNum = goodNum;
    }

    public Integer getGoodNum()
    {
        return goodNum;
    }
    public void setTotalMoney(BigDecimal totalMoney) 
    {
        this.totalMoney = totalMoney;
    }

    public BigDecimal getTotalMoney() 
    {
        return totalMoney;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("goodId", getGoodId())
            .append("goodNum", getGoodNum())
            .append("totalMoney", getTotalMoney())
            .toString();
    }
}
