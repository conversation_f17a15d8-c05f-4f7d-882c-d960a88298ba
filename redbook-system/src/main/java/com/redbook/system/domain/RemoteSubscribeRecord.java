package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 【请填写功能名称】对象 remote_subscribe_record
 *
 * <AUTHOR>
 * @date 2023-09-15
 */
@ApiModel(" 一对一订购记录")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RemoteSubscribeRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 使用区域id
     */
    @TableField(value = "area_id")
    @Excel(name = "使用区域id")
    @ApiModelProperty(name = "areaId", value = "使用区域id")
    private Long areaId;


    /**
     * 付款区域id
     */
    @TableField(value = "pay_area_id")
    @Excel(name = "付款区域id")
    @ApiModelProperty(name = "payAreaId", value = "付款区域id")
    private Long payAreaId;
    @ApiModelProperty(name = "payAreaName", value = "付款区域名称")
    @TableField(exist = false)
    private String payAreaName;

    /**
     * 签约人id
     */
    @TableField(value = "user_id")
    @Excel(name = "签约人id")
    @ApiModelProperty(name = "userId", value = "签约人id")
    private String userId;


    /**
     * 实付款
     */
    @TableField(value = "money")
    @Excel(name = "实付款")
    @ApiModelProperty(name = "money", value = "实付款")
    private BigDecimal money;


    /**
     * 购买月数
     */
    @TableField(value = "month")
    @Excel(name = "购买月数")
    @ApiModelProperty(name = "month", value = "购买月数")
    private Integer month;


    /**
     * 订单号
     */
    @TableField(value = "indent_number")
    @Excel(name = "订单号")
    @ApiModelProperty(name = "indentNumber", value = "订单号")
    private String indentNumber;


    /**
     * 购买之前
     */
    @TableField(value = "before_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "购买之前", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "beforeDate", value = "购买之前")
    private Date beforeDate;


    /**
     * 购买之后
     */
    @TableField(value = "after_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "购买之后", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "afterDate", value = "购买之后")
    private Date afterDate;


    /**
     * 交易时间
     */
    @TableField(value = "transaction_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "transactionTime", value = "交易时间")
    private Date transactionTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setPayAreaId(Long payAreaId) {
        this.payAreaId = payAreaId;
    }

    public Long getPayAreaId() {
        return payAreaId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public void setIndentNumber(String indentNumber) {
        this.indentNumber = indentNumber;
    }

    public String getIndentNumber() {
        return indentNumber;
    }

    public void setBeforeDate(Date beforeDate) {
        this.beforeDate = beforeDate;
    }

    public Date getBeforeDate() {
        return beforeDate;
    }

    public void setAfterDate(Date afterDate) {
        this.afterDate = afterDate;
    }

    public Date getAfterDate() {
        return afterDate;
    }

    public void setTransactionTime(Date transactionTime) {
        this.transactionTime = transactionTime;
    }

    public Date getTransactionTime() {
        return transactionTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("areaId", getAreaId())
                .append("payAreaId", getPayAreaId())
                .append("userId", getUserId())
                .append("money", getMoney())
                .append("month", getMonth())
                .append("indentNumber", getIndentNumber())
                .append("beforeDate", getBeforeDate())
                .append("afterDate", getAfterDate())
                .append("transactionTime", getTransactionTime())
                .toString();
    }
}
