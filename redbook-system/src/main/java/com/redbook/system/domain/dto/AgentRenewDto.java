package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@ApiModel("代理商续约")
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgentRenewDto {
    @ApiModelProperty(name = "id",value= "代理商id" )
    @NotNull(message = "代理商id不能为空")
    private Long id;
    @ApiModelProperty(name = "date",value= "续约日期" )
    @NotNull(message = "续约日期不能为空")
    private String date;
    @ApiModelProperty(name = "extraJson",value= "扩展信息" )
    private String extraJson;
}
