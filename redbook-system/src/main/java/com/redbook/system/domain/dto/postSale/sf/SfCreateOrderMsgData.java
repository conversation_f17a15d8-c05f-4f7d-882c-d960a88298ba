package com.redbook.system.domain.dto.postSale.sf;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel(value = "顺丰下单请求业务数据报文")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SfCreateOrderMsgData {

    @ApiModelProperty(value = "响应报文的语言", required = true)
    private String language = "zh-CN";
    @ApiModelProperty(value = "业务单id（寄修单、配件单id）", required = true)
    private Integer bizOrderId;
    @ApiModelProperty(value = "客户订单号", required = true)
    private String orderId="JX2025041500016";
    @ApiModelProperty(value = "客户订单操作标识: 1:确认  2:取消", required = true)
    private Integer dealType;
    @ApiModelProperty(value = "报关信息", required = false)
    private CustomsInfoBean customsInfo;
    @ApiModelProperty(value = "拖寄物类型描述,如： 文件，电子产品，衣服等", required = false)
    private String cargoDesc = "电子产品";
    @ApiModelProperty(value = "顺丰月结卡号 月结支付时传值，现结不需传值；沙箱联调可使用测试月结卡号7551234567（非正式，无须绑定，仅支持联调使用）", required = false)
    private String monthlyCard = "7551234567";
    @ApiModelProperty(value = "付款方式，支持以下值： 1:寄方付 2:收方付 3:第三方付", required = false)
    private int payMethod=1;
    @ApiModelProperty(value = "快件产品类别", required = true)
    private int expressTypeId = 1;
    @ApiModelProperty(value = "包裹数，一个包裹对应一个运单号；若包裹数大于1，则返回一个母运单号和N-1个子运单号", required = false)
    private int parcelQty = 1;
    @ApiModelProperty(value = "客户订单货物总长，单位厘米， 精确到小数点后3位， 包含子母件", required = false)
    private double totalLength;
    @ApiModelProperty(value = "客户订单货物总宽，单位厘米， 精确到小数点后3位， 包含子母件", required = false)
    private double totalWidth;
    @ApiModelProperty(value = "客户订单货物总高，单位厘米， 精确到小数点后3位， 包含子母件", required = false)
    private double totalHeight;
    @ApiModelProperty(value = "托寄物体积", required = false)
    private double volume;
    @ApiModelProperty(value = "订单货物总重量（郑州空港海关必填）， 若为子母件必填， 单位千克， 精确到小数点后3位，如果提供此值， 必须>0 (子母件需>6)", required = false)
    private double totalWeight;
    @ApiModelProperty(value = "商品总净重", required = false)
    private String totalNetWeight;
    @ApiModelProperty(value = "要求上门取件开始时间， 格式： YYYY-MM-DD HH24:MM:SS， 示例： 2012-7-30 09:30:00 （预约单传预约截止时间，不赋值默认按当前时间下发，1小时内取件）", required = false)
    private String sendStartTm;
    @ApiModelProperty(value = "是否通过手持终端 通知顺丰收派 员上门收件，支持以下值： 1：要求 0：不要求", required = false)
    private int isDocall = 0;
    @ApiModelProperty(value = "是否返回签回单 （签单返还）的运单号， 支持以下值： 1：要求 0：不要求", required = false)
    private int isSignBack = 0;
    @ApiModelProperty(value = "托寄物信息", required = true)
    private List<CargoDetailsBean> cargoDetails;
    @ApiModelProperty(value = "扩展属性", required = false)
    private List<ExtraInfoListBean> extraInfoList;
    @ApiModelProperty(value = "增值服务信息", required = false)
    private List<ServiceListBean> serviceList;
    @ApiModelProperty(value = "收寄双方信息", required = true)
    private List<ContactInfoListBean> contactInfoList;
    @ApiModelProperty(value = "是否返回路由标签： 默认1， 1：返回路由标签， 0：不返回；除部分特殊用户外，其余用户都默认返回", required = true)
    private Integer isReturnRoutelabel=1;
    @ApiModel(value = "报关信息")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CustomsInfoBean {
        @ApiModelProperty(value = "客户订单货物总声明价值， 包含子母件，精确到小数点 后3位。如果是跨境件，则必填", required = false)
        private double declaredValue;

        public double getDeclaredValue() {
            return declaredValue;
        }

        public void setDeclaredValue(double declaredValue) {
            this.declaredValue = declaredValue;
        }
    }

    @ApiModel(value = "托寄物信息")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CargoDetailsBean {
        @ApiModelProperty(value = "货物单价，精确到小数点后3位， 跨境件报关需要填写", required = false)
        private double amount;
        @ApiModelProperty(value = "货物数量 跨境件报关需要填写", required = false)
        private double count;
        @ApiModelProperty(value = "货物单价的币别", required = false)
        private String currency;
        @ApiModelProperty(value = "商品海关备案号", required = false)
        private String goodPrepardNo;
        @ApiModelProperty(value = "海关编码", required = false)
        private String hsCode;
        @ApiModelProperty(value = "货物名称，如果需要生成电子 运单，则为必填", required = true)
        private String name;
        @ApiModelProperty(value = "货物产品国检备案编号", required = false)
        private String productRecordNo;
        @ApiModelProperty(value = "原产地国别， 跨境件报关需要填写", required = false)
        private String sourceArea;
        @ApiModelProperty(value = "商品行邮税号", required = false)
        private String taxNo;
        @ApiModelProperty(value = "货物单位，如：个、台、本， 跨境件报关需要填写", required = false)
        private String unit;
        @ApiModelProperty(value = "订单货物单位重量，包含子母件， 单位千克，精确到小数点后3位 跨境件报关需要填写", required = false)
        private double weight;
    }

    @ApiModel(value = "扩展属性")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ExtraInfoListBean {

        @ApiModelProperty(value = "扩展字段说明：attrName为字段定义， 具体如下表，value存在attrVal", required = false)
        private String attrName;
        @ApiModelProperty(value = "扩展字段值", required = false)
        private String attrVal;
    }

    @ApiModel(value = "增值服务信息")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ServiceListBean {
        @ApiModelProperty(value = "增值服务名，如COD等", required = true)
        private String name;
        @ApiModelProperty(value = "增值服务扩展属性", required = false)
        private String value;

    }

    @ApiModel(value = "收寄双方信息")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ContactInfoListBean {
        @ApiModelProperty(value = "详细地址，若有四级行政区划，如镇/街道等信息可拼接至此字段，格式样例：镇/街道+详细地址。若province/city 字段的值不传，此字段必须包含省市信息，避免影响原寄地代码识别，如：广东省深圳市福田区新洲十一街万基商务大厦10楼；此字段地址必须详细，否则会影响目的地中转识别", required = true)
        private String address;
        @ApiModelProperty(value = "所在地级行政区名称，必须是标准的城市称谓 如：北京市、 深圳市、大理白族自治州等； 此字段影响原寄地代码识别， 建议尽可能传该字段的值", required = false)
        private String city;
        @ApiModelProperty(value = "联系人", required = false)
        private String contact;
        @ApiModelProperty(value = "地址类型： 1，寄件方信息 2，到件方信息", required = true)
        private int contactType;
        @ApiModelProperty(value = "国家或地区代码 例如：内地件CN 香港852", required = true)
        private String country = "CN";
        @ApiModelProperty(value = "所在县/区级行政区名称，必须 是标准的县/区称谓，如：福田区，南涧彝族自治县、准格尔旗等", required = false)
        private String county;
        @ApiModelProperty(value = "手机（tel和mobile字段必填其中一个）", required = false)
        private String mobile;
        @ApiModelProperty(value = "邮编，跨境件必填（中国内地， 港澳台互寄除外）", required = false)
        private String postCode;
        @ApiModelProperty(value = "所在省级行政区名称，必须是标准的省级行政区名称如：北 京、广东省、广西壮族自治区等；此字段影响原寄地代码识 别，建议尽可能传该字段的值", required = false)
        private String province;
        @ApiModelProperty(value = "联系电话（tel和mobile字段必填其中一个）", required = false)
        private String tel;
        @ApiModelProperty(value = "公司名称", required = false)
        private String company;
    }
}
