package com.redbook.system.domain.pay;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 统一支付对象 post_sale_pay_order
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@ApiModel("统一支付")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSalePayOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 业务类型 (REPAIR:寄修单, SHOP:商城单) */
    @TableField(value = "biz_type")
    @Excel(name = "业务类型 (REPAIR:寄修单, SHOP:商城单)")
    @ApiModelProperty(name = "bizType",value= "业务类型 (REPAIR:寄修单, SHOP:商城单)" )
    private String bizType;

    @TableField(value = "biz_order_id")
    @Excel(name = "业务订单id")
    @ApiModelProperty(name = "bizOrderId",value= "业务订单id" )
    private Integer bizOrderId;

    /** 业务订单号 */
    @TableField(value = "biz_order_no")
    @Excel(name = "业务订单号")
    @ApiModelProperty(name = "bizOrderNo",value= "业务订单号" )
    private String bizOrderNo;


    /** 支付金额 */
    @TableField(value = "amount")
    @Excel(name = "支付金额")
    @ApiModelProperty(name = "amount",value= "支付金额" )
    private BigDecimal amount;


    /** 支付状态 (0-待支付, 1-已支付, 2-支付失败, 3-已关闭) */
    @TableField(value = "status")
    @Excel(name = "支付状态 (0-待支付, 1-已支付, 2-支付失败, 3-已关闭)")
    @ApiModelProperty(name = "status",value= "支付状态 (0-待支付, 1-已支付, 2-支付失败, 3-已关闭)" )
    private Integer status;


    /** 微信支付单号 */
    @TableField(value = "transaction_id")
    @Excel(name = "微信支付单号")
    @ApiModelProperty(name = "transactionId",value= "微信支付单号" )
    private String transactionId;

    @TableField(value = "pay_time")
    @ApiModelProperty(name = "pay_time",value= "支付时间" )
    private Date payTime;


    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBizType(String bizType) 
    {
        this.bizType = bizType;
    }

    public String getBizType() 
    {
        return bizType;
    }
    public void setBizOrderNo(String bizOrderNo) 
    {
        this.bizOrderNo = bizOrderNo;
    }

    public String getBizOrderNo() 
    {
        return bizOrderNo;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setTransactionId(String transactionId) 
    {
        this.transactionId = transactionId;
    }

    public String getTransactionId() 
    {
        return transactionId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bizType", getBizType())
            .append("bizOrderNo", getBizOrderNo())
            .append("amount", getAmount())
            .append("status", getStatus())
            .append("transactionId", getTransactionId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
