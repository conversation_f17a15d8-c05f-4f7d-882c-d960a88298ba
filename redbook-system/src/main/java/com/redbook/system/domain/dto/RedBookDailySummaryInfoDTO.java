package com.redbook.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@ApiModel(value = "日汇总信息请求包")
@AllArgsConstructor
@NoArgsConstructor
public class RedBookDailySummaryInfoDTO {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    LocalDate startDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    LocalDate endDate;
}
