package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class FiveStarUserRecord extends BaseEntity {
    /** 用户姓名 */
    @ApiModelProperty(name = "userName",value= "用户姓名" )
    private String userName;

    /** 用户id */
    @ApiModelProperty(name = "userId",value= "用户id" )
    private String userId;

    /** 代理商id */
    @ApiModelProperty(name = "aid",value= "代理商id" )
    private String aid;

    /** 兑换前到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "赠送前到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "exchangeExpirationDateBefore",value= "赠送前到期日期" )
    private Date exchangeExpirationDateBefore;

    /** 兑换后到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "赠送后到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "exchangeExpirationDateAfter",value= "赠送后到期日期" )
    private Date exchangeExpirationDateAfter;

    @ApiModelProperty(name = "exchangeStage",value= "兑换阶段" )
    private Integer exchangeStage;

    @ApiModelProperty(name = "utime",value= "更新时间" )
    private Date utime;

    @ApiModelProperty(name = "timeStatus",value= "时间赠送状态" )
    private Integer timeStatus;


    /**
     * 小学学段到期日期，如果为空表示没有购买
     */
    @TableField(value = "stage1_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "小学学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage1ExpirationDate", value = "小学学段到期日期，如果为空表示没有购买")
    private Date stage1ExpirationDate;


    /**
     * 初中学段到期日期，如果为空表示没有购买
     */
    @TableField(value = "stage2_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初中学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage2ExpirationDate", value = "初中学段到期日期，如果为空表示没有购买")
    private Date stage2ExpirationDate;


    /**
     * 高中学段到期日期，如果为空表示没有购买
     */
    @TableField(value = "stage3_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "高中学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage3ExpirationDate", value = "高中学段到期日期，如果为空表示没有购买")
    private Date stage3ExpirationDate;

    @TableField(value = "stage4_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "大学学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage4ExpirationDate", value = "大学学段到期日期，如果为空表示没有购买")
    private Date stage4ExpirationDate;

    @TableField(value = "stage5_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出国学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage5ExpirationDate", value = "出国学段到期日期，如果为空表示没有购买")
    private Date stage5ExpirationDate;

    @TableField(value = "stage11_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "小升初学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage11ExpirationDate", value = "小升初学段到期日期，如果为空表示没有购买")
    private Date stage11ExpirationDate;

    @TableField(value = "stage21_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初升高学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage21ExpirationDate", value = "初升高学段到期日期，如果为空表示没有购买")
    private Date stage21ExpirationDate;


    /**
     * 所属学段（1小学/2初中/3高中）
     */
    @TableField(value = "stage")
    @Excel(name = "所属学段", readConverterExp = "1=小学/2初中/3高中/4大学/5出国/11小升初/21初升高")
    @ApiModelProperty(name = "stage", value = "所属学段")
    private Integer stage;

}
