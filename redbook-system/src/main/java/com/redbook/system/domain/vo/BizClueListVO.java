package com.redbook.system.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 线索列表对象 biz_clue
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
@ApiModel("线索列表VO")
@Data
public class BizClueListVO
{
    private static final long serialVersionUID = 1L;


    /** 线索id */
    @ApiModelProperty(name = "clueId",value= "线索id" )
    private Long clueId;

    /** 区域代理商id */
    @ApiModelProperty(name = "agentId",value= "区域代理商id" )
    private Long agentId;

    /** 区域代理商编码 */
    @ApiModelProperty(name = "aid",value= "区域代理商编码" )
    private String aid;

    /** 区域代理商名称 */
    @Excel(name = "区域",sort = 1)
    @ApiModelProperty(name = "agentName",value= "区域代理商名称" )
    private String agentName;

    /** 专卖店id */
    @ApiModelProperty(name = "exclusiveShopId",value= "专卖店id" )
    private Integer exclusiveShopId;

    /** 专卖店名称 */
    @Excel(name = "专卖店",sort = 2)
    @ApiModelProperty(name = "exclusiveShopName",value= "专卖店名称" )
    private String exclusiveShopName;

    /** 客户名称 */
    @Excel(name = "姓名",sort = 3)
    @ApiModelProperty(name = "customerName",value= "客户名称" )
    private String customerName;

    /** 联系电话 */
    @TableField(value = "phone")
    @Excel(name = "电话",sort = 4)
    @ApiModelProperty(name = "phone",value= "联系电话" )
    private String phone;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "录入日期",dateFormat = "yyyy-MM-dd HH:mm:ss",width = 20,sort = 5)
    @ApiModelProperty(name = "createTime",value= "创建时间" )
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty(name = "createBy",value= "创建人" )
    private String createBy;

    /** 更新时间 */
    @ApiModelProperty(name = "updateTime",value= "更新时间" )
    private Date updateTime;

    /** 更新人 */
    @ApiModelProperty(name = "updateBy",value= "更新人" )
    private String updateBy;

    /** 会员号-用户id */
    @TableField(value = "user_id")
    @ApiModelProperty(name = "userId",value= "会员号-用户id" )
    private String userId;

    /** 备用联系人 */
    @TableField(value = "reserve_name")
    @ApiModelProperty(name = "reserveName",value= "备用联系人" )
    private String reserveName;

    /** 联系人关系 */
    @TableField(value = "reserve_relation")
    @ApiModelProperty(name = "reserveRelation",value= "联系人关系" )
    private String reserveRelation;

    /** 备用联系人电话 */
    @TableField(value = "reserve_phone")
    @ApiModelProperty(name = "reservePhone",value= "备用联系人电话" )
    private String reservePhone;

    /** 性别:0女，1男 */
    @TableField(value = "sex")
    @ApiModelProperty(name = "sex",value= "性别:0女，1男" )
    private Integer sex;

    /** 年龄 */
    @TableField(value = "age")
    @ApiModelProperty(name = "age",value= "年龄" )
    private Integer age;

    /** 年级 */
    @TableField(value = "grade")
    @ApiModelProperty(name = "grade",value= "年级" )
    private String grade;

    /** 学校 */
    @TableField(value = "school")
    @ApiModelProperty(name = "school",value= "学校" )
    private String school;

    /** 班级 */
    @TableField(value = "class_name")
    @ApiModelProperty(name = "class_name",value= "班级" )
    private String className;

    /** 渠道id */
    @TableField(value = "channel_id")
    @ApiModelProperty(name = "channelId",value= "渠道id" )
    private Long channelId;

    /** 渠道名称 */
    @TableField(value = "channel_name")
    @ApiModelProperty(name = "channelName",value= "渠道名称" )
    private String channelName;

    /** 渠道名称-获取方式 */
    @Excel(name = "渠道",sort = 6,width = 30)
    @ApiModelProperty(name = "channelNameSource",value= "渠道名称-获取方式" )
    private String channelNameSource;

    /** 获取方式：1电销，2地推，3其他 */
    @TableField(value = "customer_source")
    @ApiModelProperty(name = "customerSource",value= "获取方式：1电销，2地推，3其他" )
    private Integer customerSource;

    /** 线索池：1 共有池，2跟进池，3 成交池 */
    @TableField(value = "clue_pool_type")
    @Excel(name = "线索池",readConverterExp = "1=共有池,2=跟进池,3=成交池",sort = 7)
    @ApiModelProperty(name = "cluePoolType",value= "线索池：1 共有池，2跟进池，3 成交池" )
    private Integer cluePoolType;

    /** 状态：0无效，1未跟进，2跟进中，3已邀约，4已到店，5已体验，6已成交，7已续费，8已流失 */
    @TableField(value = "status")
    @Excel(name = "状态",readConverterExp = "0=无效,1=未跟进,2=跟进中,3=已邀约,4=已到店,5=已体验,6=已成交,7=已续费,8=已流失",sort = 8)
    @ApiModelProperty(name = "status",value= "状态：0无效，1未跟进，2跟进中，3已邀约，4已到店，5已体验，6已成交，7已续费，8已流失" )
    private Integer status;

    /** 质量标签：0空关停，1未接或占线，2缺乏购买力，3意向度中高，4有购买力 */
    @TableField(value = "customer_label")
    @Excel(name = "质量标签",readConverterExp = "0=空关停,1=未接或占线,2=缺乏购买力,3=意向度中高,4=有购买力",sort = 9)
    @ApiModelProperty(name = "customerLabel",value= "质量标签：0空关停，1未接或占线，2缺乏购买力，3意向度中高，4有购买力" )
    private Integer customerLabel;

    /** 跟进次数 */
    @TableField(value = "follow_number")
    @Excel(name = "跟进次数",sort = 10)
    @ApiModelProperty(name = "followNumber",value= "跟进次数" )
    private Integer followNumber;

    /** 最后跟进时间 */
    @TableField(value = "finally_follow_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后跟进", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss",sort = 11)
    @ApiModelProperty(name = "finallyFollowDate",value= "最后跟进时间" )
    private Date finallyFollowDate;

    /** 最后跟进内容 */
    @TableField(value = "finally_follow_content")
    @ApiModelProperty(name = "finallyFollowContent",value= "最后跟进内容" )
    private String finallyFollowContent;

    /** 下次跟进时间 */
    @TableField(value = "next_follow_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下次跟进", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss",sort = 12)
    @ApiModelProperty(name = "nextFollowDate",value= "下次跟进时间" )
    private Date nextFollowDate;

    /** 学管师id */
    @TableField(value = "agent_user_id")
    @ApiModelProperty(name = "agentUserId",value= "学管师id" )
    private String agentUserId;

    /** 学管师姓名 */
    @TableField(value = "agent_user_name")
    @Excel(name = "负责人",sort = 13)
    @ApiModelProperty(name = "agentUserName",value= "学管师姓名" )
    private String agentUserName;

    /** 成交日期 */
    @TableField(value = "deal_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "dealDate",value= "成交日期" )
    private Date dealDate;

    /** 最后续费日期 */
    @TableField(value = "finally_renew_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "finallyRenewDate",value= "最后续费日期" )
    private Date finallyRenewDate;

    /** 收费金额 */
    @TableField(value = "amount")
    @ApiModelProperty(name = "amount",value= "收费金额" )
    private BigDecimal amount;


    /** 创建者id */
    @TableField(value = "create_by_id")
    @ApiModelProperty(name = "createById",value= "创建者id" )
    private Long createById;

    /** 更新者id */
    @TableField(value = "update_by_id")
    @ApiModelProperty(name = "updateById",value= "更新者id" )
    private String updateById;
    /** 更新者系统 */
    @TableField(value = "update_user_system")
    @ApiModelProperty(name = "updateUserSystem",value= "更新者系统" )
    private String updateUserSystem;

    @TableField(value = "remark")
    @ApiModelProperty(name = "remark",value= "备注" )
    private String remark;

}
