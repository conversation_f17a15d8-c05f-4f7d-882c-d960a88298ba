package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 寄修单图片视频文件对象 post_sale_repair_order_file
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@ApiModel("寄修单图片视频文件")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleRepairOrderFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(name = "id",value= "主键" )
    private Long id;


    /** 寄修单id */
    @TableField(value = "order_id")
    @Excel(name = "寄修单id")
    @ApiModelProperty(name = "orderId",value= "寄修单id" )
    private Integer orderId;


    /** 寄修单条目id，为空代表所属整个寄修单 */
    @TableField(value = "item_id")
    @Excel(name = "寄修单条目id，为空代表所属整个寄修单")
    @ApiModelProperty(name = "itemId",value= "寄修单条目id，为空代表所属整个寄修单" )
    private Integer itemId;


    /** 文件类型 1:客户装箱 2:签收 3:打包回寄 */
    @TableField(value = "file_type")
    @ApiModelProperty(name = "fileType",value= "文件类型 1:客户装箱 2:签收 3:打包回寄 4:故障文件" )
    private Integer fileType;


    /** 以list<String>json串形式存储 */
    @TableField(value = "extra")
    @Excel(name = "以list<String>json串形式存储")
    @ApiModelProperty(name = "extra",value= "以list<String>json串形式存储" )
    private String extra;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(Integer orderId)
    {
        this.orderId = orderId;
    }

    public Integer getOrderId()
    {
        return orderId;
    }

    public void setExtra(String extra) 
    {
        this.extra = extra;
    }

    public String getExtra() 
    {
        return extra;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("itemId", getItemId())
            .append("fileType", getFileType())
            .append("extra", getExtra())
            .toString();
    }
}
