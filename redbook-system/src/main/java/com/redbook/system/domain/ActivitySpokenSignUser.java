package com.redbook.system.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ActivitySpokenSignUser {
    private Integer id;
    private Integer activityBaseId;
    private String userId;
    private String aid;
    private Integer stage;
    private String stageDesc;
    private String userName;
    private String userAddr;
    private String userAlias;
    private Integer turntableCount;
    @ApiModelProperty(value = "第一周打卡总数")
    private Integer firstWeekSignCout;
    @ApiModelProperty(value = "第二周打卡总数")
    private Integer secondWeekSignCout;
    @ApiModelProperty(value = "第三周打卡总数")
    private Integer thirdWeekSignCout;
    @ApiModelProperty(value = "第四周打卡总数")
    private Integer fourWeekSignCout;
    private Date createTime;
    @ApiModelProperty(value = "排名 null代表未上榜")
    private Integer rankNum;
    @ApiModelProperty(value = "当天成绩")
    private String dayScore;
    @ApiModelProperty(value = "总打卡天数")
    private Integer totalSignCount;
    @ApiModelProperty(value = "当周打卡数")
    private Integer weekSignCount;
    private Integer top100MaxScore;
    private Date top100MaxScoreTime;
    private String top100MaxScoreTimeStr;
    private Integer top100Assist;
    private Float top100TotalScore;
    private Boolean isEnter100;
    private Integer top10MaxScore;
    private Date top10MaxScoreTime;
    private String top10MaxScoreTimeStr;
    private Integer top10Assist;
    private Float top10TotalScore;
    private Float finalScore;
    private Boolean isEnter10;
    private Integer finalRank;

    private String headImageUrl;
    private String headPendantImg;

    private Integer top100MaxScoreArticleId;
    private Integer top100MaxScoreUnitId;
    private Integer top10MaxScoreArticleId;
    private Integer top10MaxScoreUnitId;
}