package com.redbook.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "代理商交易统计", description = "代理商交易统计")
@Builder
public class TransactionStatisticsVo {
    @ApiModelProperty(value = "代理商id")
    private Long agentId;
    @ApiModelProperty(name = "name",value= "代理商名称" )
    private String agentName;
    @ApiModelProperty(name = "transactionStartDate",value = "交易开始日期")
    private String transactionStartDate;
    @ApiModelProperty(name = "transactionEndDate",value = "交易截至日期")
    private String transactionEndDate;

    @ApiModelProperty(name = "hardwareMoney",value= "硬件款收入" )
    private BigDecimal hardwareMoneyIn;
    @ApiModelProperty(name = "hardwareMoney",value= "硬件款支出" )
    private BigDecimal hardwareMoneyOut;

    @ApiModelProperty(name = "memberMoney",value= "会员款收入" )
    private BigDecimal memberMoneyIn;
    @ApiModelProperty(name = "memberMoney",value= "会员款支出" )
    private BigDecimal memberMoneyOut;

    @ApiModelProperty(name = "voucherMoney",value= "代金券收入" )
    private BigDecimal voucherMoneyIn;
    @ApiModelProperty(name = "voucherMoney",value= "代金券支出" )
    private BigDecimal voucherMoneyOut;


    @ApiModelProperty(name = "newVoucherMoney",value= "期初会员款代金券充值" )
    private BigDecimal newVoucherMoneyIn;
    @ApiModelProperty(name = "newVoucherMoney",value= "期初会员款代金券出账" )
    private BigDecimal newVoucherMoneyOut;

    @ApiModelProperty(name = "newMemberMoney",value= "期初会员款充值" )
    private BigDecimal newMemberMoneyIn;
    @ApiModelProperty(name = "newMemberMoney",value= "期初会员款出账" )
    private BigDecimal newMemberMoneyOut;

    @ApiModelProperty(name = "totalMoney",value= "汇总收入" )
    private BigDecimal totalMoneyIn;
    @ApiModelProperty(name = "totalMoney",value= "汇总支出" )
    private BigDecimal totalMoneyOut;

    @ApiModelProperty(value = "经理姓名列表 用“，”分割")
    private String managerNames;
}
