package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel(value = "设置代理商赠送硬件数量")
@AllArgsConstructor
@NoArgsConstructor
public class SetAgentGiftCountDto {
    @ApiModelProperty(name = "id", value = "代理商id")
    private Long id;

    @ApiModelProperty(name = "id", value = "配送数量")
    private Integer giftCount;
}
