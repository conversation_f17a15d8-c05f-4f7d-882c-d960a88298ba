package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import com.redbook.system.domain.dto.postSale.bean.PostSaleRepairOrderItemFileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 寄修单条目对象 post_sale_repair_order_item
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@ApiModel("寄修单条目")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleRepairOrderItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(exist = false)
    @Excel(name = "设备型号")
    private String modelName;
    @TableField(exist = false)
    @Excel(name = "设备型号")
    private String model2Name;

    @TableField(value = "product_sn")
    @Excel(name = "产品sn码")
    @ApiModelProperty(name = "productSn", value = "产品sn码")
    private String productSn;

    @TableField(exist = false)
    @Excel(name = "生产批次")
    private String batchName;

    @TableField(exist = false)
    @Excel(name = "1级分类")
    private String level1FaultClassifyName;

    @TableField(exist = false)
    @Excel(name = "二级分类")
    private String level2FaultClassifyName;

    @TableField(exist = false)
    @Excel(name = "故障分类数据")
    @ApiModelProperty(name = "itemFaultClassifyList", value = "故障分类数据")
    private List<PostSaleRepairOrderItemFaults> itemFaultClassifyList;

    @TableField(exist = false)
    @Excel(name = "维修物料")
    @ApiModelProperty(name = "itemSparePartList", value = "维修物料")
    private List<PostSaleRepairOrderItemSparePart> itemSparePartList;

    @TableField(exist = false)
    @Excel(name = "维修物料1")
    @ApiModelProperty(name = "itemSparePartListAdd", value = "追加的维修物料")
    private List<PostSaleRepairOrderItemSparePart> itemSparePartListAdd;

    @TableField(exist = false)
    @Excel(name = "价格记录")
    @ApiModelProperty(name = "itemFeeList", value = "条目费用列表")
    private List<PostSaleRepairOrderItemFee> itemFeeList;
/*    @TableField(exist = false)
    @Excel(name = "维修物料")
    @ApiModelProperty(name = "itemSparePartList", value = "维修物料--异议/修改")
    private List<PostSaleRepairOrderItemSparePart> itemSparePartListUpdate;

    @TableField(exist = false)
    @Excel(name = "维修物料")
    @ApiModelProperty(name = "itemSparePartList", value = "维修物料")
    private List<PostSaleRepairOrderItemSparePart> itemSparePartListAdd;*/

    @TableField(exist = false)
    private Integer faultClassifyParentId;
    @TableField(exist = false)
    private Integer faultClassifyId;

    @TableField(value = "repair_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "维修日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "repairDate", value = "维修日期")
    private Date repairDate;

/*    @TableField(exist = false)
    @Excel(name = "保修范围")
    @ApiModelProperty(name = "rejudgmentResultTypeStr", value = "保修范围汉字描述")
    private String rejudgmentResultTypeStr;*/

    @TableField(value = "activation_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "activationDate", value = "激活日期(首次用户登录日期)")
    private Date activationDate;

    @TableField(value = "repair_activation_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "repairActivationDate", value = "维修日期距离激活日期时间")
    private Long repairActivationDate;

    @TableField(value = "order_id")
    @ApiModelProperty(name = "orderId", value = "寄修单id，post_sale_repair_order主表id")
    private Integer orderId;

    @TableField(exist = false)
    @Excel(name = "寄修单号")
    @ApiModelProperty(name = "sendRepairNo", value = "寄修单号")
    private String sendRepairNo;

    @TableField(value = "product_no")
    @Excel(name = "物品编号")
    @ApiModelProperty(name = "productNo", value = "物品编号")
    private String productNo;

    @TableField(value = "alias")
    @Excel(name = "体验中心")
    @ApiModelProperty(name = "alias", value = "体验中心")
    private String alias;

    @TableField(value = "owner_name")
    @ApiModelProperty(name = "ownerName", value = "机主姓名")
    private String ownerName;

    @TableField(value = "device_type")
    @ApiModelProperty(name = "deviceType", value = "设备类型，1:整套（平板+键盘） 2:仅平板 3:仅键盘 4:耳机 5:其他")
    private Integer deviceType;

    @TableField(value = "product_types")
    @ApiModelProperty(name = "productTypes", value = "物品清单，用|分割，1:平板主机 2:键盘 3:流量卡 4:充电器 5:充电线 6:彩色包装盒 7:耳机  99:其他")
    private String productTypes;

    @TableField(exist = false)
    @ApiModelProperty(name = "actionType", value = "操作类型 1:签收 2:复判 3:付款 4:维修 5:老化 6:品控 7:回寄 8:客户签收 21:处理报价异议")
    private Integer actionType;

    @TableField(value = "repair_status")
    @ApiModelProperty(name = "itemRepairStatus", value = "流程控制状态 0:待签收 1:已签收,待复判 2:已复判,待付款 3:已付款,待维修 4:已维修,待老化 5:已老化,待品控 6:已品控,待回寄 7:已回寄,待签收 8：已签收，待评价  9：已完结")
    private Integer repairStatus;


    @TableField(value = "scrap_status")
    @ApiModelProperty(name = "scrapStatus", value = "报废状态 1：已复判，待报废，2：已报废，待付款，3：付款，待回寄  4：报废的就地报废")
    private Integer scrapStatus;

    @TableField(value = "abandon_status")
    @ApiModelProperty(name = "abandonStatus", value = "放弃维修状态 2：已放弃，待付款，3：付款后待回寄，4：报废的就地报废")
    private Integer abandonStatus;

    @TableField(value = "is_confirm_scrap")
    @ApiModelProperty(name = "isConfirmScrap", value = "是否确认报废")
    private Boolean isConfirmScrap;

    @TableField(value = "is_confirm_abandon")
    @ApiModelProperty(name = "isConfirmAbandon", value = "是否确认放弃维修")
    private Boolean isConfirmAbandon;
    /*@TableField(value = "is_delete")
    @ApiModelProperty(name = "isDelete", value = "是否删除")
    private Boolean isDelete;*/

    @TableField(value = "is_invalid")
    @ApiModelProperty(name = "isInvalid", value = "是否是无效清单")
    private Boolean isInvalid;

    @TableField(value = "price_dissent_status")
    @ApiModelProperty(name = "priceDissentStatus", value = "报价异议状态 0：默认无异议 1：用户提出异议 2：确认有异议，重新报价 3：确认无异议")
    private Integer priceDissentStatus;

    @TableField(value = "product_other_text")
    @ApiModelProperty(name = "productOtherText", value = "物品选择其他时的说明")
    private String productOtherText;

    @TableField(value = "material_fee")
    @ApiModelProperty(name = "itemMaterialFee", value = "物料费用")
    private BigDecimal materialFee;

/*    @TableField(value = "express_fee")
    @ApiModelProperty(name = "itemExpressFee", value = "快递费用")
    private BigDecimal expressFee;*/

    @TableField(value = "send_express_fee")
    @ApiModelProperty(name = "itemSendExpressFee", value = "快递费用")
    private BigDecimal sendExpressFee;

    @TableField(value = "return_express_fee")
    @ApiModelProperty(name = "itemReturnExpressFee", value = "快递费用")
    private BigDecimal returnExpressFee;

    @TableField(value = "labor_fee")
    @ApiModelProperty(name = "itemLaborFee", value = "工时费用")
    private BigDecimal laborFee;

    @TableField(value = "total_fee")
    @ApiModelProperty(name = "itemTotalFee", value = "总费用")
    private BigDecimal totalFee;

    @TableField(value = "fault_desc")
    @ApiModelProperty(name = "faultDesc", value = "故障描述")
    private String faultDesc;

    @TableField(value = "judge_fault_desc")
    @ApiModelProperty(name = "judgeFaultDesc", value = "复判故障描述")
    private String judgeFaultDesc;

    @TableField(value = "repair_plan")
    @ApiModelProperty(name = "repairPlan", value = "维修方案,  1:维修 2:换新 3:报废 4:转寄")
    private Integer repairPlan;


    @TableField(value = "abnormal_handler_type")
    @ApiModelProperty(name = "abnormalHandlerType", value = "异常处理方式 1:不良件寄回 2:就地报废")
    private Integer abnormalHandlerType;

    @TableField(value = "abnormal_statement")
    @ApiModelProperty(name = "abnormalStatement", value = "异常说明")
    private String abnormalStatement;

    @TableField(value = "rejudgment_result_type")
    @Excel(name = "复判结果类型 1:保内质量 2:保内人为 3：保外")
    @ApiModelProperty(name = "rejudgmentResultType", value = "复判结果类型 1:保内质量 2:保内人为 3：保外")
    private Integer rejudgmentResultType;

    @TableField(value = "renew_type")
    @ApiModelProperty(name = "renewType", value = "换新类型 1:整套平板换新 2:单独平板换新 3:单独键盘换新")
    private Integer renewType;

    @TableField(value = "renew_sn")
    @ApiModelProperty(name = "renewSn", value = "换新sn码")
    private String renewSn;

    @TableField(value = "batch_id")
    @ApiModelProperty(name = "batchId", value = "产品型号")
    private Integer batchId;

    @TableField(value = "model_id")
    @ApiModelProperty(name = "modelId", value = "产品型号")
    private Long modelId;

    @TableField(value = "expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "保修期到期日，销售日期+1年", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "expirationDate", value = "保修期到期日，销售日期+1年")
    private Date expirationDate;


    @TableField(value = "sale_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "saleDate", value = "销售日期")
    private Date saleDate;

    @TableField(value = "batch2_id")
    @ApiModelProperty(name = "batch2Id", value = "产品型号2")
    private Integer batch2Id;

    @TableField(value = "model2_id")
    @ApiModelProperty(name = "model2Id", value = "device_type=1时使用，键盘型号")
    private Long model2Id;

    @TableField(value = "product_sn2")
    @ApiModelProperty(name = "productSn2", value = "device_type=1时使用，键盘sn码")
    private String productSn2;

    @TableField(value = "repair_user_id")
    @Excel(name = "维修人员")
    @ApiModelProperty(name = "repairUserId", value = "维修人员")
    private String repairUserId;
    @TableField(exist = false)
    @ApiModelProperty(name = "repairUserName", value = "产品sn码")
    private String repairUserName;
    @TableField(value = "transfer_factory_id")
    @ApiModelProperty(name = "transferFactoryId", value = "转寄的工厂")
    private String transferFactoryId;

    @TableField(value = "transfer_worker_id")
    @ApiModelProperty(name = "transferWorkerId", value = "转寄的工人，复用sys_user")
    private String transferWorkerId;

    @TableField(value = "is_pay")
    @ApiModelProperty(name = "isPay", value = "是否付款")
    private Integer isPay;

    @TableField(value = "pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "payTime", value = "支付时间")
    private Date payTime;

    @TableField(value = "return_product_types")
    @ApiModelProperty(name = "returnProductTypes", value = "回寄的物品清单，用|分割，1:平板主机 2:键盘 3:流量卡 4:充电器 5:充电线 6:彩色包装盒 7:耳机  99:其他")
    private String returnProductTypes;

    @TableField(value = "return_other_text")
    @ApiModelProperty(name = "returnOtherText", value = "回寄选择其他时的说明")
    private String returnOtherText;

    @TableField(exist = false)
    @ApiModelProperty(name = "orderItemFileBean", value = "寄修单文件")
    private List<PostSaleRepairOrderItemFileEntity> orderItemFileBeanList;

    @TableField(exist = false)
    @ApiModelProperty(name = "itemFlows", value = "条目流程")
    private List<PostSaleRepairOrderItemFlow> itemFlows;


    @TableField(exist = false)
    @Excel(name = "未读消息数量")
    @ApiModelProperty(name = "unReadMessageCount", value = "未读消息数量")
    private Integer unReadMessageCount = 0;

    @TableField(value = "return_express_order_id")
    @Excel(name = "回寄快递单id")
    @ApiModelProperty(name = "returnExpressOrderId", value = "回寄快递单id")
    private String returnExpressOrderId;

    @TableField(value = "return_express_no")
    @Excel(name = "回寄快递单号")
    @ApiModelProperty(name = "returnExpressNo", value = "回寄快递单号")
    private String returnExpressNo;


    @TableField(value = "return_express_company_id")
    @Excel(name = "回寄快递公司")
    @ApiModelProperty(name = "returnExpressCompanyId", value = "回寄快递公司")
    private Integer returnExpressCompanyId;

    @TableField(value = "return_time")
    @ApiModelProperty(name = "returnTime", value = "回寄时间")
    private Date returnTime;

    @TableField(value = "is_screen_bad")
    @ApiModelProperty(name = "isScreenBad", value = "屏幕是否异常")
    private Integer isScreenBad;

    @TableField(value = "is_back_cover_bad")
    @ApiModelProperty(name = "isBackCoverBad", value = "后壳是否异常")
    private Integer isBackCoverBad;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "paySmsSendTime", value = "提醒付款短信发送时间")
    private Date paySmsSendTime;


    @Excel(name = "是否需要发送支付短信提醒")
    @ApiModelProperty(name = "isNeedSendPaySms", value = "是否需要发送支付短信提醒")
    private Boolean isNeedSendPaySms;


    @TableField(exist = false)
    private String sendUserName;

    @TableField(exist = false)
    private String sendUserPhone;

    @TableField(value = "is_repaired")
    @ApiModelProperty(name = "isRepaired", value = "是否维修过")
    private Boolean isRepaired = false;

    @TableField(exist = false)
    @ApiModelProperty(name = "itemFeeDetails", value = "费用明细")
    private String itemFeeDetails;

    /*    @TableField(exist = false)
        @ApiModelProperty(name = "scrapRemark", value = "报废说明")
        private String scrapRemark;

        @TableField(exist = false)
        @ApiModelProperty(name = "abandonRepairRemark", value = "放弃维修说明")
        private String abandonRepairRemark;*/
    @TableField(exist = false)
    @ApiModelProperty(name = "itemExceptionRemark", value = "异常说明")
    private PostSaleRepairOrderItemRemark itemExceptionRemark;
    @TableField(exist = false)
    @ApiModelProperty(name = "itemBack", value = "条目备份")
    private PostSaleRepairOrderItemBack  itemBack;
   /*   @TableField(exist = false)
    @ApiModelProperty(name = "itemPriceDissentRemark", value = "价格异议说明")
    private PostSaleRepairOrderItemRemark itemPriceDissentRemark;
    @TableField(exist = false)
    @ApiModelProperty(name = "itemAbandonRemark", value = "放弃维修说明")
    private PostSaleRepairOrderItemRemark itemAbandonRemark;*/

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerName() {
        return ownerName;
    }


    public void setProductTypes(String productTypes) {
        this.productTypes = productTypes;
    }

    public String getProductTypes() {
        return productTypes;
    }

    public void setProductOtherText(String productOtherText) {
        this.productOtherText = productOtherText;
    }

    public String getProductOtherText() {
        return productOtherText;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }


    public void setFaultDesc(String faultDesc) {
        this.faultDesc = faultDesc;
    }

    public String getFaultDesc() {
        return faultDesc;
    }


    public void setAbnormalStatement(String abnormalStatement) {
        this.abnormalStatement = abnormalStatement;
    }

    public String getAbnormalStatement() {
        return abnormalStatement;
    }

    public void setProductSn(String productSn) {
        this.productSn = productSn;
    }

    public String getProductSn() {
        return productSn;
    }

    public void setActivationDate(Date activationDate) {
        this.activationDate = activationDate;
    }

    public Date getActivationDate() {
        return activationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setProductSn2(String productSn2) {
        this.productSn2 = productSn2;
    }

    public String getProductSn2() {
        return productSn2;
    }

    public void setRepairUserId(String repairUserId) {
        this.repairUserId = repairUserId;
    }

    public String getRepairUserId() {
        return repairUserId;
    }

    public void setTransferFactoryId(String transferFactoryId) {
        this.transferFactoryId = transferFactoryId;
    }

    public String getTransferFactoryId() {
        return transferFactoryId;
    }

    public void setTransferWorkerId(String transferWorkerId) {
        this.transferWorkerId = transferWorkerId;
    }

    public String getTransferWorkerId() {
        return transferWorkerId;
    }

    public void setIsPay(Integer isPay) {
        this.isPay = isPay;
    }

    public Integer getIsPay() {
        return isPay;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getPayTime() {
        return payTime;
    }


    public void setReturnProductTypes(String returnProductTypes) {
        this.returnProductTypes = returnProductTypes;
    }

    public String getReturnProductTypes() {
        return returnProductTypes;
    }

    public void setReturnOtherText(String returnOtherText) {
        this.returnOtherText = returnOtherText;
    }

    public String getReturnOtherText() {
        return returnOtherText;
    }


    public Date getRepairDate() {
        return repairDate;
    }

    public void setRepairDate(Date repairDate) {
        this.repairDate = repairDate;
    }

    public List<PostSaleRepairOrderItemFlow> getItemFlows() {
        return itemFlows;
    }


}
