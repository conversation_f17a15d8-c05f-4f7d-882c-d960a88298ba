package com.redbook.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public  class RedBookDailySummaryData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "日期")
    String dateStr;
    @ApiModelProperty(value = "高峰人数")
    Long onLineNum;
    @ApiModelProperty(value = "登录数")
    Long loginNum;
}
