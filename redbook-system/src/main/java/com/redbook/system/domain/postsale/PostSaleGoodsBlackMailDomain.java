package com.redbook.system.domain.postsale;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-04-21 15:08
 */
@Data
public class PostSaleGoodsBlackMailDomain {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(name = "id",value= "订单id" )
    private Long id;

    @ApiModelProperty(name = "expressType",value= "发货类型：打单发货 blackMail、手工发货 handwork" )
    private String expressType;
    @ApiModelProperty(name = "sendExpressPickTime",value= "预计取件时间" )
    private Date sendExpressPickTime;

    /** 快递单号 */
    @ApiModelProperty(name = "expressNo",value= "快递单号（手工发货传参）" )
    private String expressNo;
    @ApiModelProperty(name = "expressCompanyId",value= "快递公司id（手工发货传参）" )
    private Integer expressCompanyId;

}
