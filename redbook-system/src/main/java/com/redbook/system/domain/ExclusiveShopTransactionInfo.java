package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 专卖店交易记录对象 exclusive_shop_transaction_info
 * 
 * <AUTHOR>
 * @date 2023-12-21
 */
@ApiModel("专卖店交易记录")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ExclusiveShopTransactionInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /**  */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 专卖店id */
    @TableField(value = "exclusive_shop_id")
    @Excel(name = "专卖店id")
    @ApiModelProperty(name = "exclusiveShopId",value= "专卖店id" )
    private Integer exclusiveShopId;


    /** 订单号 */
    @TableField(value = "indent_number")
    @Excel(name = "订单号")
    @ApiModelProperty(name = "indentNumber",value= "订单号" )
    private String indentNumber;


    /** 交易类型 */
    @TableField(value = "transaction_type")
    @Excel(name = "交易类型")
    @ApiModelProperty(name = "transactionType",value= "交易类型" )
    private String transactionType;


    /** 金额 */
    @TableField(value = "money")
    @Excel(name = "金额")
    @ApiModelProperty(name = "money",value= "金额" )
    private BigDecimal money;


    /** 收支类型 0收入 1支出 */
    @TableField(value = "payment_type")
    @Excel(name = "收支类型 0收入 1支出")
    @ApiModelProperty(name = "paymentType",value= "收支类型 0收入 1支出" )
    private Integer paymentType;


    /** 余额 */
    @TableField(value = "balance")
    @Excel(name = "余额")
    @ApiModelProperty(name = "balance",value= "余额" )
    private BigDecimal balance;


    /** 交易时间 */
    @TableField(value = "transaction_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交易时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "transactionTime",value= "交易时间" )
    private Date transactionTime;



    /** 截图文件地址列表 */
    @TableField(value = "screenshots")
    @Excel(name = "截图文件地址列表")
    @ApiModelProperty(name = "screenshots",value= "截图文件地址列表" )
    private String screenshots;

    @TableField(value = "remark")
    @Excel(name = "备注")
    @ApiModelProperty(name = "remark",value= "备注" )
    private String remark;


    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setExclusiveShopId(Integer exclusiveShopId) 
    {
        this.exclusiveShopId = exclusiveShopId;
    }

    public Integer getExclusiveShopId() 
    {
        return exclusiveShopId;
    }
    public void setIndentNumber(String indentNumber) 
    {
        this.indentNumber = indentNumber;
    }

    public String getIndentNumber() 
    {
        return indentNumber;
    }
    public void setTransactionType(String transactionType) 
    {
        this.transactionType = transactionType;
    }

    public String getTransactionType() 
    {
        return transactionType;
    }
    public void setMoney(BigDecimal money) 
    {
        this.money = money;
    }

    public BigDecimal getMoney() 
    {
        return money;
    }
    public void setPaymentType(Integer paymentType) 
    {
        this.paymentType = paymentType;
    }

    public Integer getPaymentType() 
    {
        return paymentType;
    }
    public void setBalance(BigDecimal balance) 
    {
        this.balance = balance;
    }

    public BigDecimal getBalance() 
    {
        return balance;
    }
    public void setTransactionTime(Date transactionTime) 
    {
        this.transactionTime = transactionTime;
    }

    public Date getTransactionTime() 
    {
        return transactionTime;
    }
    public void setScreenshots(String screenshots) 
    {
        this.screenshots = screenshots;
    }

    public String getScreenshots() 
    {
        return screenshots;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("exclusiveShopId", getExclusiveShopId())
            .append("indentNumber", getIndentNumber())
            .append("transactionType", getTransactionType())
            .append("money", getMoney())
            .append("paymentType", getPaymentType())
            .append("balance", getBalance())
            .append("transactionTime", getTransactionTime())
            .append("remark", getRemark())
            .append("screenshots", getScreenshots())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
