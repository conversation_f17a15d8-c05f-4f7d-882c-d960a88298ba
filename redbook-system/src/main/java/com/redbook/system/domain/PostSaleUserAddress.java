package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户地址对象 post_sale_user_address
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@ApiModel("用户地址")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleUserAddress extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 用户id */
    @TableField(value = "user_id")
    @Excel(name = "用户id")
    @ApiModelProperty(name = "userId",value= "用户id" )
    private Long userId;


    /** 用户名称 */
    @TableField(value = "user_name")
    @Excel(name = "用户名称")
    @ApiModelProperty(name = "userName",value= "用户名称" )
    private String userName;


    /** 用户联系方式 */
    @TableField(value = "user_phone")
    @Excel(name = "用户联系方式")
    @ApiModelProperty(name = "userPhone",value= "用户联系方式" )
    private String userPhone;


    /** 用户分机号 */
    @TableField(value = "user_extension_no")
    @Excel(name = "用户分机号")
    @ApiModelProperty(name = "userExtensionNo",value= "用户分机号" )
    private String userExtensionNo;

    @TableField(value = "organization")
    @Excel(name = "机构名")
    @ApiModelProperty(name = "organization",value= "机构名" )
    private String organization;


    /** 省份 */
    @TableField(value = "user_province")
    @Excel(name = "省份")
    @ApiModelProperty(name = "userProvince",value= "省份" )
    private String userProvince;


    /** 城市 */
    @TableField(value = "user_city")
    @Excel(name = "城市")
    @ApiModelProperty(name = "userCity",value= "城市" )
    private String userCity;


    /** 县区 */
    @TableField(value = "user_county")
    @Excel(name = "县区")
    @ApiModelProperty(name = "userCounty",value= "县区" )
    private String userCounty;


    /** 详细地址 */
    @TableField(value = "user_address")
    @Excel(name = "详细地址")
    @ApiModelProperty(name = "userAddress",value= "详细地址" )
    private String userAddress;


    /** 是否是默认地址 */
    @TableField(value = "is_default")
    @Excel(name = "是否是默认地址")
    @ApiModelProperty(name = "isDefault",value= "是否是默认地址" )
    private Long isDefault;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setUserPhone(String userPhone) 
    {
        this.userPhone = userPhone;
    }

    public String getUserPhone() 
    {
        return userPhone;
    }
    public void setUserExtensionNo(String userExtensionNo) 
    {
        this.userExtensionNo = userExtensionNo;
    }

    public String getUserExtensionNo() 
    {
        return userExtensionNo;
    }
    public void setUserProvince(String userProvince) 
    {
        this.userProvince = userProvince;
    }

    public String getUserProvince() 
    {
        return userProvince;
    }
    public void setUserCity(String userCity) 
    {
        this.userCity = userCity;
    }

    public String getUserCity() 
    {
        return userCity;
    }
    public void setUserCounty(String userCounty) 
    {
        this.userCounty = userCounty;
    }

    public String getUserCounty() 
    {
        return userCounty;
    }
    public void setUserAddress(String userAddress) 
    {
        this.userAddress = userAddress;
    }

    public String getUserAddress() 
    {
        return userAddress;
    }
    public void setIsDefault(Long isDefault) 
    {
        this.isDefault = isDefault;
    }

    public Long getIsDefault() 
    {
        return isDefault;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("userPhone", getUserPhone())
            .append("userExtensionNo", getUserExtensionNo())
            .append("userProvince", getUserProvince())
            .append("userCity", getUserCity())
            .append("userCounty", getUserCounty())
            .append("userAddress", getUserAddress())
            .append("isDefault", getIsDefault())
            .toString();
    }
}
