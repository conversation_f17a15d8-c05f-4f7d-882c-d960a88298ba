package com.redbook.system.mq;

import com.redbook.system.enums.PushActionEnum;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
public class JpushMessage implements Serializable {
    /**
     * 用户id,定义为集合，因为可能有全选操作
     */
    private List<String> userIds;
    /**
     * 时间
     */
    private Date createTime;
    /**
     * 标题
     */

    private String title;
    /**
     * 发送的信息
     */
    private String msg;
    /**
     * 推送类型
     */
    private PushActionEnum pushActionEnum;

    /**
     * 再学一遍时app用到
     */
    private Integer courseId;
    private Integer unidId;

//    private RedBookConstant.StudyModule studyModule;

//    private RedBookContentTypeEnum contentType;
//    private PrivateMessage privateMessage;
}
