package com.redbook.system.mapper;

import com.redbook.system.domain.old.GoodsBean;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -11-25 14:48
 */
public interface IStoreManagerGoodsDao {

    //根据商品的goods_number 获取商品
    GoodsBean selectGoodsByGoodsNumber(String goodsNumber);

    //根据商品的goods_number 获取商品
    Map selectMapByGoodsNumber(String goodsNumber);

    //修改商品的库存
    int updateCountById(@Param("id") Integer id,
                        @Param("count") Integer count,
                        @Param("isAdd") Integer isAdd);

    //校长获取商品-正式课程
    Map selectByIdOrAid(@Param("userIdFK") Integer userIdFK,
                        @Param("id") Integer id,
                        @Param("type") int type);

    //获取课程列表
    List<Map> selectSecondCourseList(@Param("type") Integer type);

    //二级校-根据商品id修改价格
    Integer updatePriceById(@Param("id") Integer id,
                            @Param("lastPrice") BigDecimal lastPrice);

    //二级校-获取商品列表，
    List<Map> getGoodsList(@Param("packagingForm") Integer packagingForm);

    /**
     * 上传商品
     *
     * @param name
     * @param price
     * @param desc
     * @param imageUrl
     */
    void upload(GoodsBean goods);

    /**
     * 修改商品
     *
     * @param goodsId
     * @param name
     * @param price
     * @param desc
     * @param stock
     */
    Integer modify(@Param("goodsId") Integer goodsId, @Param("name") String name, @Param("price") Integer price,@Param("diamonds") Integer diamonds, @Param("desc") String desc, @Param("stock") Integer stock, @Param("imgUrl")String imgUrl,@Param("label")Integer label);
    Integer setSort(@Param("goodsId") Integer goodsId, @Param("sort") Integer sort);

    /**
     * 修改图片地址
     *
     * @param goodsId
     * @param imageUrl
     * @return
     */
    Integer modifyImg(@Param("goodsId") Integer goodsId, @Param("imageUrl") String imageUrl);

    /**
     * 往关联表里插入数据
     *
     * @param hmid
     * @param goodsId
     * @return
     */
    void insertGoodsAgent(@Param("Aid") String Aid,@Param("exclusiveShopId")Integer exclusiveShopId, @Param("goodsId") Integer goodsId,@Param("userIdFk")Integer userIdFk);

    /**
     * 查询校长底下自己上传的商品
     *
     * @param aid
     * @return
     */
    List<Map<String, Object>> selectGoodsList(@Param("aid") String aid);

    /**
     * 获取商品信息
     *
     * @param aid
     * @param goodsId
     * @return
     */
    Map<String, Object> selectGoods( @Param("goodsId") Integer goodsId);

    /**
     * 删除关联表里商品
     *
     * @param id
     * @return
     */
    //boolean deleteGoodsAgent(@Param("aid") String aid, @Param("goodsId") Integer goodsId);
    boolean deleteGoodsAgent(@Param("aid") String aid,@Param("exclusiveShopId")Integer exclusiveShopId, @Param("goodsId") Integer goodsId);

    /**
     * 删除该商品
     *
     * @param goodsId
     */
    boolean deleteGoods(@Param("goodsId") Integer goodsId);

    /**
     * 下架该商品
     *
     * @param id
     * @return
     */
    boolean offShelf( @Param("id") Integer id,@Param("exclusiveShopId")Integer exclusiveShopId,@Param("aid")String aid);

    /**
     * 上架该商品
     *
     * @param id
     * @return
     */
    boolean onShelf(@Param("id") Integer id,@Param("exclusiveShopId")Integer exclusiveShopId, @Param("aid") String aid);

    /**
     * 查询该商品在关联表里的数量,如果大于1则删除该商品
     *
     * @param goodsId
     * @return
     */
    Integer selectGoodsAgentNum(@Param("goodsId") Integer goodsId);

    /**
     * 查询登录用户校区下的 查询商品库存记录集合
     *
     * @param goodsName 商品名称
     * @param startDate 开始时间 (入库时间)
     * @param endDate   结束时间 (入库时间)
     * @param aid       校区id
     * @param pageIndex 分页起始索引
     * @param pageSize  每页条数
     * @return List<Map> 库存记录集合
     * <AUTHOR>
     */
    List<Map> findStockRecordList(@Param("aid")String aid,@Param("exclusiveShopId")Integer exclusiveShopId,@Param("goodsName") String goodsName, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("pageIndex") Integer pageIndex, @Param("pageSize") Integer pageSize);

    /**
     * 查询登录用户校区下的 查询商品库存记录总数
     *
     * @param goodsName 商品名称
     * @param startDate 开始时间 (入库时间)
     * @param endDate   结束时间 (入库时间)
     * @param aid       校区id
     * @return int 库存记录总数
     * <AUTHOR>
     */
    int findStockRecordCount(@Param("aid")String aid,@Param("exclusiveShopId")Integer exclusiveShopId,@Param("goodsName") String goodsName, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 更新商品表库存
     *
     * @param goodsId        商品ID
     * @param insertStockNum 添加库存数
     * @return Boolean 成功/失败
     * <AUTHOR>
     */
    Boolean updateGoodsStock(@Param("goodsId") Integer goodsId, @Param("insertStockNum") Integer insertStockNum);

    /**
     * 保存库存记录
     *
     * @param goodsId        商品ID
     * @param insertStockNum 添加库存数
     * @param userId         当前操作人账号
     * @return Boolean 成功/失败
     * <AUTHOR>
     */
    Boolean insertStockRecord(@Param("goodsId") Integer goodsId, @Param("insertStockNum") Integer insertStockNum,@Param("time")String time, @Param("userId") String userId);

    /**
     * 查询登录用户校区下的 商品订单记录集合
     *
     * @param orderId   订单编号
     * @param goodsName 商品名称
     * @param startDate 开始时间 (入库时间)
     * @param endDate   结束时间 (入库时间)
     * @param pageIndex 分页起始索引
     * @param pageSize  每页条数
     * @return List<Map> 订单记录集合
     * <AUTHOR>
     */
    List<Map> findOrderRecordList(@Param("orderId") Integer orderId, @Param("goodsName") String goodsName, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("aid") String aid, @Param("pageIndex") Integer pageIndex, @Param("pageSize") Integer pageSize);

    /**
     * 查询登录用户校区下的 商品订单记录总条数
     *
     * @param orderId   订单编号
     * @param goodsName 商品名称
     * @param startDate 开始时间 (入库时间)
     * @param endDate   结束时间 (入库时间)
     * @return int 订单记录总条数
     * <AUTHOR>
     */
    int findOrderRecordCount(@Param("orderId") Integer orderId, @Param("goodsName") String goodsName, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("aid") String aid);

    List<Map<String, Object>> listGoodsList(@Param("aid")String aid, @Param("exclusiveShopId")Integer exclusiveShopId, @Param("storeType")Integer storeType);

}
