package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleRepairOrderItemRemark;
import org.apache.ibatis.annotations.Param;

/**
 * 寄修单条目备注Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface PostSaleRepairOrderItemRemarkMapper extends BaseMapper<PostSaleRepairOrderItemRemark> {

    int insertPostSaleRepairOrderItemRemark(PostSaleRepairOrderItemRemark postSaleRepairOrderItemRemark);

    //根据条目id和类型查询记录
    PostSaleRepairOrderItemRemark selectByItemIdAndType(@Param("itemId") Integer itemId, @Param("remarkType") int remarkType);

}
