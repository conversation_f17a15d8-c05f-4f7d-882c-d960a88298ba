package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleRepairOrder;
import com.redbook.system.domain.pay.PostSaleRefundOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 寄修单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleRepairOrderMapper extends BaseMapper<PostSaleRepairOrder> {
    /**
     * 查询寄修单
     *
     * @param id 寄修单主键
     * @return 寄修单
     */
    PostSaleRepairOrder selectPostSaleRepairOrderById(Integer id);

    /**
     * 查询寄修单列表
     *
     * @param postSaleRepairOrder 寄修单
     * @return 寄修单集合
     */
//    List<PostSaleRepairOrder> selectPostSaleRepairOrderList(PostSaleRepairOrder postSaleRepairOrder);

    List<PostSaleRepairOrder> summaryList(PostSaleRepairOrder postSaleRepairOrder);

    Map<String, Object> summaryCount();

    /**
     * 新增寄修单
     *
     * @param postSaleRepairOrder 寄修单
     * @return 结果
     */
    int insertPostSaleRepairOrder(PostSaleRepairOrder postSaleRepairOrder);

    /**
     * 修改寄修单
     *
     * @param postSaleRepairOrder 寄修单
     * @return 结果
     */
    int updatePostSaleRepairOrder(PostSaleRepairOrder postSaleRepairOrder);

    void updateNoUserIdRecordByPhone(@Param("userId") Long userId, @Param("phoneNumber") String phoneNumber);

    //更新寄修单快递单号
    void updatePostSaleRepairOrderSendExpressNoById(@Param("id") Integer id, @Param("sendExpressOrderNo") String sendExpressOrderNo);


    /**
     * 新增统一退款
     *
     * @param postSaleRefundOrder 统一退款
     * @return 结果
     */
    int insertPostSaleRefundOrder(PostSaleRefundOrder postSaleRefundOrder);

    /**
     * 修改统一退款
     *
     * @param postSaleRefundOrder 统一退款
     * @return 结果
     */
    int updatePostSaleRefundOrder(PostSaleRefundOrder postSaleRefundOrder);

    /**
     * 修改统一支付订单状态为：申请退款
     *
     * @param payOrderId
     * @return
     */
    int updatePayOrder(@Param("refundStatus") Integer refundStatus, @Param("payOrderId") Long payOrderId);


    List<PostSaleRepairOrder> selectPostSaleRepairOrderListByParam(@Param("orderSearchValue") String orderSearchValue,
                                                                   @Param("itemSearchValue") String itemSearchValue,
                                                                   @Param("params") Map<String, Object> params,
                                                                   @Param("repairStatus") Integer repairStatus,
                                                                   @Param("faultId") Integer faultId,@Param("sortField") String sortField,@Param("sortDirection") String sortDirection);
}
