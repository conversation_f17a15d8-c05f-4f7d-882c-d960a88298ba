package com.redbook.system.mapper;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022 -02-28 14:36
 */
public interface ITydRenewCardDao {
    /**
     * 续费小红本userbean
     * @param userId
     * @param memberType
     * @param expirationDate
     * @param stage1ExpirationDate
     * @param stage2ExpirationDate
     * @param stage3ExpirationDate
     * @return
     */
    boolean renewRedBookUser(@Param("userId") String userId,
                             @Param("memberType") Integer memberType,
                             @Param("expirationDate") String expirationDate,
                             @Param("stage1ExpirationDate") String stage1ExpirationDate,
                             @Param("stage2ExpirationDate") String stage2ExpirationDate,
                             @Param("stage3ExpirationDate") String stage3ExpirationDate,
                             @Param("stage4ExpirationDate") String stage4ExpirationDate,
                             @Param("stage5ExpirationDate") String stage5ExpirationDate,
                             @Param("stage11ExpirationDate") String stage11ExpirationDate,
                             @Param("stage21ExpirationDate") String stage21ExpirationDate,
                             @Param("firstPurchaseDate")String firstPurchaseDate,
                             @Param("lastPurchaseDate")String lastPurchaseDate);
    //退费还原
    boolean renewRedBookUserBack(@Param("userId") String userId,
                             @Param("memberType") Integer memberType,
                             @Param("stage") Integer stage,
                             @Param("expirationDate") String expirationDate,
                             @Param("stage1ExpirationDate") String stage1ExpirationDate,
                             @Param("stage2ExpirationDate") String stage2ExpirationDate,
                             @Param("stage3ExpirationDate") String stage3ExpirationDate,
                             @Param("stage4ExpirationDate") String stage4ExpirationDate,
                             @Param("stage5ExpirationDate") String stage5ExpirationDate,
                             @Param("stage11ExpirationDate") String stage11ExpirationDate,
                             @Param("stage21ExpirationDate") String stage21ExpirationDate,
                             @Param("firstPurchaseDate")String firstPurchaseDate,
                             @Param("lastPurchaseDate")String lastPurchaseDate);
    /**
     * 重置小红本用户主课程相关信息。
     * @param userId
     * @param stage
     */
    boolean resetRedBookUserMainCourseInfo(@Param("userId") String userId, @Param("stage") int stage);
    /**
     * 更新用户所属校区
     * @param userId
     * @param aid
     * @return
     */
    boolean updateRedBookUserAgentId(@Param("userId") String userId, @Param("aid") String aid);
    boolean updateRedBookUserAid(@Param("userId") String userId, @Param("aid") String aid);
}
