package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleQuestionClassify;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后问题分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleQuestionClassifyMapper extends BaseMapper<PostSaleQuestionClassify>
{
    /**
     * 查询售后问题分类
     * 
     * @param id 售后问题分类主键
     * @return 售后问题分类
     */
        PostSaleQuestionClassify selectPostSaleQuestionClassifyById(Long id);

    /**
     * 查询售后问题分类列表
     * 
     * @param postSaleQuestionClassify 售后问题分类
     * @return 售后问题分类集合
     */
    List<PostSaleQuestionClassify> selectPostSaleQuestionClassifyList(PostSaleQuestionClassify postSaleQuestionClassify);

    /**
     * 新增售后问题分类
     * 
     * @param postSaleQuestionClassify 售后问题分类
     * @return 结果
     */
    int insertPostSaleQuestionClassify(PostSaleQuestionClassify postSaleQuestionClassify);

    /**
     * 修改售后问题分类
     * 
     * @param postSaleQuestionClassify 售后问题分类
     * @return 结果
     */
    int updatePostSaleQuestionClassify(PostSaleQuestionClassify postSaleQuestionClassify);

    /**
     * 删除售后问题分类
     *
     * @param id     售后问题分类主键
     * @param userId
     * @return 结果
     */
    int deletePostSaleQuestionClassifyById(@Param("id") Long id, @Param("userId") String userId);

    /**
     * 批量删除售后问题分类
     *
     * @param ids    需要删除的数据主键集合
     * @param userId
     * @return 结果
     */
    int deletePostSaleQuestionClassifyByIds(@Param("ids") Long[] ids,@Param("userId") String userId);

    /**
     * 修改文章数量
     *
     * @param id
     * @param num
     * @param userId
     */
    void updateArticleNumById(@Param("id") Long id, @Param("num") int num, @Param("userId") String userId);
}
