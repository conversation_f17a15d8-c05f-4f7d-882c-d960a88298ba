package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.AgentActivityItem;

import java.util.List;
/**
 * 代理商活动-商品Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-11
 */
public interface AgentActivityItemMapper extends BaseMapper<AgentActivityItem>
{
    /**
     * 查询代理商活动-商品
     * 
     * @param id 代理商活动-商品主键
     * @return 代理商活动-商品
     */
        AgentActivityItem selectAgentActivityItemById(Long id);

    /**
     * 查询代理商活动-商品列表
     * 
     * @param agentActivityItem 代理商活动-商品
     * @return 代理商活动-商品集合
     */
    List<AgentActivityItem> selectAgentActivityItemList(AgentActivityItem agentActivityItem);

    /**
     * 新增代理商活动-商品
     * 
     * @param agentActivityItem 代理商活动-商品
     * @return 结果
     */
    int insertAgentActivityItem(AgentActivityItem agentActivityItem);

    /**
     * 修改代理商活动-商品
     * 
     * @param agentActivityItem 代理商活动-商品
     * @return 结果
     */
    int updateAgentActivityItem(AgentActivityItem agentActivityItem);

    int deleteByAgentActivityItem(AgentActivityItem agentActivityItem);


    /**
     * 删除代理商活动-商品
     * 
     * @param id 代理商活动-商品主键
     * @return 结果
     */
    int deleteAgentActivityItemById(Long id);
    int deleteAgentActivityItemByActivityId(Integer activityId);

    /**
     * 批量删除代理商活动-商品
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAgentActivityItemByIds(Long[] ids);
}
