package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.RedbookAgentRebate;

import java.util.List;
/**
 * 代理商动态价格优惠关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface RedbookAgentRebateMapper extends BaseMapper<RedbookAgentRebate>
{
    /**
     * 查询代理商动态价格优惠关系
     * 
     * @param id 代理商动态价格优惠关系主键
     * @return 代理商动态价格优惠关系
     */
        RedbookAgentRebate selectRedbookAgentRebateById(Long id);

    /**
     * 查询代理商动态价格优惠关系列表
     * 
     * @param redbookAgentRebate 代理商动态价格优惠关系
     * @return 代理商动态价格优惠关系集合
     */
    List<RedbookAgentRebate> selectRedbookAgentRebateList(RedbookAgentRebate redbookAgentRebate);

    /**
     * 新增代理商动态价格优惠关系
     * 
     * @param redbookAgentRebate 代理商动态价格优惠关系
     * @return 结果
     */
    int insertRedbookAgentRebate(RedbookAgentRebate redbookAgentRebate);

    /**
     * 修改代理商动态价格优惠关系
     * 
     * @param redbookAgentRebate 代理商动态价格优惠关系
     * @return 结果
     */
    int updateRedbookAgentRebate(RedbookAgentRebate redbookAgentRebate);

    /**
     * 删除代理商动态价格优惠关系
     * 
     * @param id 代理商动态价格优惠关系主键
     * @return 结果
     */
    int deleteRedbookAgentRebateById(Long id);

    /**
     * 批量删除代理商动态价格优惠关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRedbookAgentRebateByIds(Long[] ids);
}
