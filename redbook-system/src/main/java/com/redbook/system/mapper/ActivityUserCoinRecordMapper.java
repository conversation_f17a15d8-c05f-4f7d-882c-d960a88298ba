package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 用户火星币/纪念币记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
public interface ActivityUserCoinRecordMapper extends BaseMapper<ActivityUserCoinRecord> {

    /**
     * 查询用户火星币/纪念币记录列表
     *
     * @param activityUserCoinRecord 用户火星币/纪念币记录
     * @return 用户火星币/纪念币记录集合
     */
    List<ActivityUserCoinRecord> selectActivityUserCoinRecordList(ActivityUserCoinRecord activityUserCoinRecord);
    /**
     * 查询用户火星币/纪念币记录
     *
     * @param id 用户火星币/纪念币记录主键
     * @return 用户火星币/纪念币记录
     */
    ActivityUserCoinRecord selectActivityUserCoinRecordById(@Param("id") Long id);


    /**
     * 修改用户火星币/纪念币记录
     *
     * @param activityUserCoinRecord 用户火星币/纪念币记录
     * @return 结果
     */
    int updateActivityUserCoinRecord(ActivityUserCoinRecord activityUserCoinRecord);

    int getAgentByAid(@Param("aid") String aid);


    /**
     * 获取活动用户数据
     * @param activityBaseId
     * @return
     */
    List<ActivityUser> selectActivityUserList(@Param("activityBaseId") Integer activityBaseId);

    /**
     * 查询代理商信息
     * @param aid
     * @return
     */
    Agent getAgentFromDb(@Param("aid") String aid);

    ActivityBase selectActivityBaseById(@Param("activityBaseId") Integer activityBaseId);

    /**
     * 活动期间续费人数
     * @param startTime
     * @param endTime
     * @param len
     * @return
     */
    List<ActivityTydRenewRecord> selectRenewRecordByTimeAndLength(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("len") int len,@Param("agentId") int agentId);

}
