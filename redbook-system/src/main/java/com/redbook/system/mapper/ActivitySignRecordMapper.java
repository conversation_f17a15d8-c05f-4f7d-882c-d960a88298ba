package com.redbook.system.mapper;

import com.redbook.system.domain.ActivitySignRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 打卡相关数据操作接口
 */
public interface ActivitySignRecordMapper {

    /**
     * 根据活动主表id、活动内容id、所属学段、日期，获取前100名
     *
     * @param activityBaseId
     * @param activityContentId
     * @param stage
     * @param date
     * @return
     */
    List<ActivitySignRecord> selectTopHundred(@Param("activityBaseId") Integer activityBaseId, @Param("activityContentId") Integer activityContentId, @Param("stage") Integer stage, @Param("date") String date);

    /**
     * 根据用户id、活动id、日期获取打卡记录
     *
     * @param userId
     * @param activityBaseId
     * @param date
     */
    ActivitySignRecord selectRecord(
            @Param("activityBaseId") Integer activityBaseId,
            @Param("userId") String userId,
            @Param("date") String date);
}