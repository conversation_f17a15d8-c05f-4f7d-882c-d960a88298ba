package com.redbook.system.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动内容配置相关数据接口
 */
public interface ActivityContentConfigMapper {

    /**
     * 查询配置
     *
     * @param activityContentId
     * @param accType
     * @param key
     * @return
     */
    String selectConfigByTypeAndKey(@Param("activityContentId") int activityContentId, @Param("accType") String accType, @Param("key") String key);

    /**
     * 根据类型查询配置
     *
     * @param activityContentId
     * @param type
     * @return
     */
    List<String> selectByType(@Param("activityContentId") Integer activityContentId, @Param("type") String type);
}