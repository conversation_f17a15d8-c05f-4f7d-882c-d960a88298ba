package com.redbook.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.enums.TransactionFundType;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.AgentRechargeOrder;
import com.redbook.system.domain.AgentTransactionInfo;
import com.redbook.system.domain.ProductOrder;
import com.redbook.system.domain.dto.StatisticsInvoiceInfoDto;
import com.redbook.system.domain.dto.TransactionStatisticsDto;
import com.redbook.system.domain.vo.InvoiceDetailExport;
import com.redbook.system.domain.vo.StatisticsInvoiceInfoVo;
import com.redbook.system.domain.vo.TransactionStatisticsVo;
import com.redbook.system.mapper.AgentTransactionInfoMapper;
import com.redbook.system.service.*;
import com.redbook.system.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 代理商交易记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
@Service
public class AgentTransactionInfoServiceImpl extends ServiceImpl<AgentTransactionInfoMapper, AgentTransactionInfo> implements IAgentTransactionInfoService
{
    @Autowired
    private AgentTransactionInfoMapper agentTransactionInfoMapper;
    @Autowired
    private IProductOrderService productOrderService;
    @Autowired
    IAgentService agentService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IAgentRechargeOrderService agentRechargeOrderService;
    /**
     * 查询代理商交易记录
     * 
     * @param id 代理商交易记录主键
     * @return 代理商交易记录
     */
    @Override
    public AgentTransactionInfo selectAgentTransactionInfoById(Long id)
    {
        return agentTransactionInfoMapper.selectAgentTransactionInfoById(id);
    }

    /**
     * 查询代理商交易记录列表
     * 
     * @param agentTransactionInfo 代理商交易记录
     * @return 代理商交易记录
     */
    @Override
    public List<AgentTransactionInfo> selectAgentTransactionInfoList(AgentTransactionInfo agentTransactionInfo)
    {
        return agentTransactionInfoMapper.selectAgentTransactionInfoList(agentTransactionInfo);
    }

    /**
     * 新增代理商交易记录
     * 
     * @param agentTransactionInfo 代理商交易记录
     * @return 结果
     */
    @Override
    public int insertAgentTransactionInfo(AgentTransactionInfo agentTransactionInfo)
    {
        return agentTransactionInfoMapper.insertAgentTransactionInfo(agentTransactionInfo);
    }

    /**
     * 修改代理商交易记录
     * 
     * @param agentTransactionInfo 代理商交易记录
     * @return 结果
     */
    @Override
    public int updateAgentTransactionInfo(AgentTransactionInfo agentTransactionInfo)
    {
        return agentTransactionInfoMapper.updateAgentTransactionInfo(agentTransactionInfo);
    }

    /**
     * 批量删除代理商交易记录
     * 
     * @param ids 需要删除的代理商交易记录主键
     * @return 结果
     */
    @Override
    public int deleteAgentTransactionInfoByIds(Long[] ids)
    {
        return agentTransactionInfoMapper.deleteAgentTransactionInfoByIds(ids);
    }

    /**
     * 删除代理商交易记录信息
     * 
     * @param id 代理商交易记录主键
     * @return 结果
     */
    @Override
    public int deleteAgentTransactionInfoById(Long id)
    {
        return agentTransactionInfoMapper.deleteAgentTransactionInfoById(id);
    }

    @Override
    public boolean addTransactionInfo(Long agentId, String transactionType, TransactionFundType fundType, String indentNumber, BigDecimal money,BigDecimal balance, Integer paymentType, Integer topUpWayId, Integer topUpTypeId,String remark) {
        AgentTransactionInfo build = AgentTransactionInfo.builder().agentId(agentId).transactionType(transactionType).fundType(fundType.code).indentNumber(indentNumber).money(money).balance(balance).paymentType(paymentType).topUpWayId(topUpWayId).topUpTypeId(topUpTypeId).build();
        build.setRemark(remark);
        return insertAgentTransactionInfo(build) > 0;
    }

    @Override
    public StatisticsInvoiceInfoVo statisticsInvoiceInfo(StatisticsInvoiceInfoDto dto) {
        StatisticsInvoiceInfoVo statisticsInvoiceInfoVo = StatisticsInvoiceInfoVo.builder().agentId(dto.getAgentId()).type(dto.getType()).build();
        //查询未退费、未开票的消费记录。
        AgentTransactionInfo build = AgentTransactionInfo.builder()
                .agentId(dto.getAgentId()).paymentType(1).refundStatus(false).invoiceStatus(false)
                .startTime(DateUtil.dateToString(dto.getStartDate())).endTime(DateUtil.dateToString(dto.getEndDate())).build();
        List<AgentTransactionInfo> transactionInfoList = new ArrayList<>();
        Date startDate = dto.getStartDate()==null?new Date(): dto.getStartDate();
        Date endDate = dto.getEndDate()==null?new Date(): dto.getEndDate();
        if("prodOrder".equals(dto.getType())){
            //硬件款
            build.setFundType(TransactionFundType.HARDWARE.code);
            transactionInfoList.addAll(this.selectAgentTransactionInfoList(build));

            BigDecimal totalMoney = BigDecimal.ZERO;

            Map<String,StatisticsInvoiceInfoVo.InvoiceDetail> invoiceDetailMap = new HashMap<>();
            StatisticsInvoiceInfoVo.InvoiceDetail invoiceDetail;
            String consumptionType;
            for (AgentTransactionInfo agentTransactionInfo : transactionInfoList) {
                if(agentTransactionInfo.getTransactionTime().getTime()<startDate.getTime()){
                    startDate = agentTransactionInfo.getTransactionTime();
                }
                if(agentTransactionInfo.getTransactionTime().getTime()>endDate.getTime()){
                    endDate = agentTransactionInfo.getTransactionTime();
                }
                totalMoney = totalMoney.add(agentTransactionInfo.getMoney());
                //硬件款
                if(agentTransactionInfo.getFundType().intValue()==TransactionFundType.HARDWARE.code){
                    consumptionType="硬件款：";
                    //查询硬件订单
                    List<ProductOrder> productOrderList = productOrderService.selectProductOrderByOrderNo(agentTransactionInfo.getIndentNumber());
                    Integer orderCount = 0;
                    if(CollectionUtil.isNotEmpty(productOrderList)){
                        ProductOrder productOrder = productOrderList.get(0);
                        int sum = productOrderList.stream().mapToInt(ProductOrder::getOrderCount).sum();
                        consumptionType += productOrder.getProductCategoryName()+"-"+productOrder.getProductName();
                        orderCount = sum;
                    }else{
                        consumptionType += agentTransactionInfo.getTransactionType();
                    }
                    invoiceDetail = invoiceDetailMap.get(consumptionType);
                    if(invoiceDetail==null){
                        invoiceDetail = StatisticsInvoiceInfoVo.InvoiceDetail.builder().name(consumptionType).num(0).money(BigDecimal.ZERO).build();
                    }
                    invoiceDetail.setNum(invoiceDetail.getNum()+orderCount);
                    invoiceDetail.setMoney(invoiceDetail.getMoney().add(agentTransactionInfo.getMoney()));
                    invoiceDetailMap.put(consumptionType,invoiceDetail);
                }
            }
            statisticsInvoiceInfoVo.setTransactionIdList(transactionInfoList.stream().map(agentTransactionInfo -> agentTransactionInfo.getId()).collect(Collectors.toList()));
            statisticsInvoiceInfoVo.setInvoiceDetailList(invoiceDetailMap.values().stream().collect(Collectors.toList()));
            statisticsInvoiceInfoVo.setMoney(totalMoney);
            statisticsInvoiceInfoVo.setProdOrderIdList(transactionInfoList.stream().map(agentTransactionInfo -> agentTransactionInfo.getIndentNumber()).collect(Collectors.toList()));
        }else {
            //会员款
//            build.setFundType(TransactionFundType.MEMBER.code);
//            transactionInfoList.addAll(this.selectAgentTransactionInfoList(build));
//
//            statisticsInvoiceInfoVo.setStartDate(dto.getStartDate());
//            statisticsInvoiceInfoVo.setEndDate(dto.getEndDate());
//            List<AgentRechargeOrder> agentRechargeOrders = new ArrayList<>();
//            for (AgentTransactionInfo agentTransactionInfo : transactionInfoList) {
//                AgentRechargeOrder agentRechargeOrder = agentRechargeOrderService.selectAgentRechargeOrderByOrderId(agentTransactionInfo.getIndentNumber());
//                if(agentRechargeOrder != null){
//                    agentRechargeOrders.add(agentRechargeOrder);
//                }
//            }
            AgentRechargeOrder agentRechargeOrder = new AgentRechargeOrder();
            agentRechargeOrder.setAgentId(dto.getAgentId());
            agentRechargeOrder.setStartDate(DateUtil.dateToString(dto.getStartDate()));
            agentRechargeOrder.setEndDate(DateUtil.dateToString(dto.getEndDate()));
            //会员款
            agentRechargeOrder.setFundType(TransactionFundType.MEMBER.code);
            agentRechargeOrder.setInvoiceStatus(0);
            List<AgentRechargeOrder> agentRechargeOrders = agentRechargeOrderService.selectAgentRechargeOrderList(agentRechargeOrder);
            if(CollectionUtil.isNotEmpty(agentRechargeOrders)){
                statisticsInvoiceInfoVo.setRechargeIdList(agentRechargeOrders.stream().map(AgentRechargeOrder::getId).collect(Collectors.toList()));
                int sum = agentRechargeOrders.stream().mapToInt(data -> data.getMoney().intValue()).sum();
                statisticsInvoiceInfoVo.setMoney(new BigDecimal(sum));
                List<StatisticsInvoiceInfoVo.InvoiceDetail> invoiceDetailList = agentRechargeOrders.stream().map(data -> {
                    StatisticsInvoiceInfoVo.InvoiceDetail invoiceDetail = StatisticsInvoiceInfoVo.InvoiceDetail.builder().name(StrUtil.isEmpty(data.getOrderDesc()) ? "充值" : data.getOrderDesc()).money(data.getMoney()).num(1).build();
                    return invoiceDetail;
                }).collect(Collectors.toList());
                statisticsInvoiceInfoVo.setInvoiceDetailList(invoiceDetailList);
            }else {
                statisticsInvoiceInfoVo.setMoney(new BigDecimal(0));
            }
        }
        statisticsInvoiceInfoVo.setStartDate(startDate);
        statisticsInvoiceInfoVo.setEndDate(endDate);
        return statisticsInvoiceInfoVo;
    }

    @Override
    public List<InvoiceDetailExport> statisticsInvoiceByOrder(List<String> prodOrderIdList) {
        List<InvoiceDetailExport> invoiceDetailExportList = new ArrayList<>();
        for (String orderNo : prodOrderIdList) {
            //查询硬件订单
            List<ProductOrder> productOrderList = productOrderService.selectProductOrderByOrderNo(orderNo);
            if(CollectionUtil.isNotEmpty(productOrderList)){
                for(ProductOrder productOrder : productOrderList){
                    InvoiceDetailExport invoiceDetail = new InvoiceDetailExport();
                    invoiceDetail.setType("硬件款");
                    invoiceDetail.setOrderNo(productOrder.getOrderNo());
                    invoiceDetail.setProductName("【"+productOrder.getProductCategoryName()+"】"+productOrder.getProductName());
                    invoiceDetail.setOrderCountDetail(productOrder.getOrderCountDetail());
                    invoiceDetail.setPayMoney(productOrder.getPayMoney());
                    invoiceDetail.setStatus(String.valueOf(productOrder.getStatus()));
                    invoiceDetail.setCreateTime(productOrder.getCreateTime());
                    invoiceDetailExportList.add(invoiceDetail);
                }
            }
        }
        return invoiceDetailExportList;
    }

    @Override
    public PageInfo<TransactionStatisticsVo> transactionStatistics(TransactionStatisticsDto dto) {
        PageInfo<TransactionStatisticsVo> pageInfo = new PageInfo<>();
        List<Integer> agentIdList = agentService.getAgentIdList(dto.getmId(), dto.getSearchValue(),dto.getAgentId());
        List<TransactionStatisticsVo> list = new ArrayList<>();
        for (Integer agentId : agentIdList) {
            list.add(getTransactionStatistics(agentId.longValue(), dto.getTransactionStartDate(), dto.getTransactionEndDate()));
        }
        pageInfo.setList(list);
        pageInfo.setTotal(new PageInfo(agentIdList).getTotal());
        return pageInfo;
    }

    /**
     * 获取代理商交易统计，将历史统计缓存起来（缓存12小时，避免会手动调整交易记录）
     * @param agentId
     * @param transactionStartDate
     * @param transactionEndDate
     * @return
     */
    private TransactionStatisticsVo getTransactionStatistics(Long agentId,String transactionStartDate,String transactionEndDate){
        String redisKey = "transactionStatistics:" + agentId + ":transactionStartDate:" + transactionStartDate + ":transactionEndDate:" + transactionEndDate;
        Object object = redisCache.getCacheObject(redisKey);
        if (object!=null){
            return (TransactionStatisticsVo)object;
        }
        Agent agent = agentService.selectAgentById(agentId);
        TransactionStatisticsVo statisticsVo = TransactionStatisticsVo.builder().agentId(agentId).agentName(agent.getName()).build();
        List<AgentTransactionInfo> transactionInfoList = this.selectAgentTransactionInfoList(AgentTransactionInfo.builder().agentId(agentId).startTime(transactionStartDate).endTime(transactionEndDate).build());
        statisticsVo.setHardwareMoneyIn(transactionInfoList.stream().filter(t -> t.getPaymentType() == 0 && t.getFundType().equals(TransactionFundType.HARDWARE.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setHardwareMoneyOut(transactionInfoList.stream().filter(t -> t.getPaymentType() == 1 && t.getFundType().equals(TransactionFundType.HARDWARE.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setMemberMoneyIn(transactionInfoList.stream().filter(t -> t.getPaymentType() == 0 && t.getFundType().equals(TransactionFundType.MEMBER.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setMemberMoneyOut(transactionInfoList.stream().filter(t -> t.getPaymentType() == 1 && t.getFundType().equals(TransactionFundType.MEMBER.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setVoucherMoneyIn(transactionInfoList.stream().filter(t -> t.getPaymentType() == 0 && t.getFundType().equals(TransactionFundType.COUPON.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setVoucherMoneyOut(transactionInfoList.stream().filter(t -> t.getPaymentType() == 1 && t.getFundType().equals(TransactionFundType.COUPON.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setNewVoucherMoneyIn(transactionInfoList.stream().filter(t -> t.getPaymentType() == 0 && t.getFundType().equals(TransactionFundType.NEW_COUPON.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setNewVoucherMoneyOut(transactionInfoList.stream().filter(t -> t.getPaymentType() == 1 && t.getFundType().equals(TransactionFundType.NEW_COUPON.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setNewMemberMoneyIn(transactionInfoList.stream().filter(t -> t.getPaymentType() == 0 && t.getFundType().equals(TransactionFundType.NEW_MEMBER.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setNewMemberMoneyOut(transactionInfoList.stream().filter(t -> t.getPaymentType() == 1 && t.getFundType().equals(TransactionFundType.NEW_MEMBER.code)).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        statisticsVo.setTotalMoneyIn(statisticsVo.getHardwareMoneyIn().add(statisticsVo.getMemberMoneyIn()).add(statisticsVo.getVoucherMoneyIn()).add(statisticsVo.getNewVoucherMoneyIn()).add(statisticsVo.getNewMemberMoneyIn()));
        statisticsVo.setTotalMoneyOut(statisticsVo.getHardwareMoneyOut().add(statisticsVo.getMemberMoneyOut()).add(statisticsVo.getVoucherMoneyOut()).add(statisticsVo.getNewVoucherMoneyOut()).add(statisticsVo.getNewMemberMoneyOut()));
        statisticsVo.setManagerNames(agentService.getManagerNamesByAgentId(agentId));
        //如果查询的日期范围是历史日期，则缓存数据
        if(DateUtil.dayDiffCurr(transactionEndDate)>0){
            redisCache.setCacheObject(redisKey,statisticsVo,12, TimeUnit.HOURS);
        }
        return statisticsVo;
    }
}
