package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.system.domain.RemoteSubscribeRecord;
import com.redbook.system.mapper.RemoteSubscribeRecordMapper;
import com.redbook.system.service.IRemoteSubscribeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-15
 */
@Service
public class RemoteSubscribeRecordServiceImpl extends ServiceImpl<RemoteSubscribeRecordMapper, RemoteSubscribeRecord> implements IRemoteSubscribeRecordService
{
    @Autowired
    private RemoteSubscribeRecordMapper remoteSubscribeRecordMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public RemoteSubscribeRecord selectRemoteSubscribeRecordById(Long id)
    {
        return remoteSubscribeRecordMapper.selectRemoteSubscribeRecordById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param remoteSubscribeRecord 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<RemoteSubscribeRecord> selectRemoteSubscribeRecordList(RemoteSubscribeRecord remoteSubscribeRecord)
    {
        return remoteSubscribeRecordMapper.selectRemoteSubscribeRecordList(remoteSubscribeRecord);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param remoteSubscribeRecord 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertRemoteSubscribeRecord(RemoteSubscribeRecord remoteSubscribeRecord)
    {
        return remoteSubscribeRecordMapper.insertRemoteSubscribeRecord(remoteSubscribeRecord);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param remoteSubscribeRecord 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateRemoteSubscribeRecord(RemoteSubscribeRecord remoteSubscribeRecord)
    {
        return remoteSubscribeRecordMapper.updateRemoteSubscribeRecord(remoteSubscribeRecord);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteRemoteSubscribeRecordByIds(Long[] ids)
    {
        return remoteSubscribeRecordMapper.deleteRemoteSubscribeRecordByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteRemoteSubscribeRecordById(Long id)
    {
        return remoteSubscribeRecordMapper.deleteRemoteSubscribeRecordById(id);
    }
}
