package com.redbook.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.system.domain.AgentSalesDay;
import com.redbook.system.domain.dto.AgentSalesListDto;

import java.util.List;

/**
 * 代理商销售统计Service接口
 *
 * <AUTHOR>
 * @date 2022-11-25
 */
public interface IAgentSalesDayService extends IService<AgentSalesDay> {
    /**
     * 查询代理商销售统计
     *
     * @param id 代理商销售统计主键
     * @return 代理商销售统计
     */
    AgentSalesDay selectAgentSalesDayById(Long id);

    /**
     * 查询代理商销售统计列表
     *
     * @param agentSalesDay 代理商销售统计
     * @return 代理商销售统计集合
     */
    List<AgentSalesDay> selectAgentSalesDayList(AgentSalesDay agentSalesDay);


    /**
     * 更新代理商销量数据
     * @param agentId
     * @param isZeroTransfer 是否是0元转
     * @param isFirstRenew 是否是第一次续费
     * @param renewTimeLen 续费时长（月）
     * @param renewStage 续费学段（1、2、3）
     * @param updateNum 1:+1 -1:-1
     * @return
     */
    boolean updateAgentSalesDay(long agentId,Integer exclusiveShopId,String salesDate,boolean isZeroTransfer,boolean isFirstRenew,int renewTimeLen,int renewStage,int updateNum);


//    /**
//     * 批量删除代理商销售统计
//     *
//     * @param ids 需要删除的代理商销售统计主键集合
//     * @return 结果
//     */
//    int deleteAgentSalesDayByIds(Long[] ids);
//
//    /**
//     * 删除代理商销售统计信息
//     *
//     * @param id 代理商销售统计主键
//     * @return 结果
//     */
//    int deleteAgentSalesDayById(Long id);

    TableDataInfo<AgentSalesDay> selectSaleList(AgentSalesListDto dto);
//    TableDataInfo<AgentSalesDay> selectShopSaleList(AgentSalesListDto dto);

}
