package com.redbook.system.service;

import com.redbook.system.domain.market.ConsultPictureFolder;
import com.redbook.system.domain.market.ConsultPictureFolderDTO;

import java.util.List;

/**
 * 咨询夹图片Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-22
 */
public interface IConsultPictureFolderService 
{
    /**
     * 查询咨询夹图片
     * 
     * @param id 咨询夹图片主键
     * @return 咨询夹图片
     */
     ConsultPictureFolder selectConsultPictureFolderById(Integer id);

    /**
     * 查询咨询夹图片列表
     * 
     * @param consultPictureFolder 咨询夹图片
     * @return 咨询夹图片集合
     */
     List<ConsultPictureFolder> selectConsultPictureFolderList(ConsultPictureFolder consultPictureFolder);
    List<ConsultPictureFolder> systemPictureList();

    /**
     * 新增咨询夹图片
     * 
     * @param consultPictureFolder 咨询夹图片
     * @return 结果
     */
     int insertConsultPictureFolder(ConsultPictureFolder consultPictureFolder);
     int batchUpdateSort(ConsultPictureFolderDTO consultPictureFolderDTO);

    /**
     * 修改咨询夹图片
     * 
     * @param consultPictureFolder 咨询夹图片
     * @return 结果
     */
     int updateConsultPictureFolder(ConsultPictureFolder consultPictureFolder);

    /**
     * 批量删除咨询夹图片
     * 
     * @param ids 需要删除的咨询夹图片主键集合
     * @return 结果
     */
     int deleteConsultPictureFolderByIds(Long[] ids);

    /**
     * 删除咨询夹图片信息
     * 
     * @param id 咨询夹图片主键
     * @return 结果
     */
     int deleteConsultPictureFolderById(Integer id);
}
