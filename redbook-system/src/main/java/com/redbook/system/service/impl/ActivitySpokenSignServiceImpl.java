package com.redbook.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.utils.ActivityExcleUtil;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.ActivityDrawRecord;
import com.redbook.system.domain.ActivitySignRecord;
import com.redbook.system.domain.ActivitySpokenSignUser;
import com.redbook.system.domain.ActivityStageConfig;
import com.redbook.system.domain.dto.ActivitySpokenSignIndexListDto;
import com.redbook.system.domain.dto.ActivityUserDrawRecordListDto;
import com.redbook.system.mapper.ActivityContentConfigMapper;
import com.redbook.system.mapper.ActivitySignRecordMapper;
import com.redbook.system.mapper.ActivitySpokenSignMapper;
import com.redbook.system.service.IActivitySpokenSignService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ActivitySpokenSignServiceImpl extends ServiceImpl<ActivitySpokenSignMapper, ActivitySpokenSignUser> implements IActivitySpokenSignService {
    @Autowired
    ActivitySpokenSignMapper activitySpokenSignMapper;
    @Autowired
    ActivityContentConfigMapper activityContentConfigMapper;
    @Autowired
    ActivitySignRecordMapper activitySignRecordMapper;
    @Autowired
    private RedisCache redisCache;
    @Override
    public List<ActivitySpokenSignUser> fourthList(ActivitySpokenSignIndexListDto activitySpokenSignIndexListDto) {
        Integer gameStage = activitySpokenSignIndexListDto.getGameStage();
        Integer activityBaseId = activitySpokenSignIndexListDto.getActivityBaseId();
        Integer activityContentId = activitySpokenSignIndexListDto.getActivityContentId();
        Integer stage = activitySpokenSignIndexListDto.getStage();
        Integer stageMode = activitySpokenSignIndexListDto.getStageMode();
        List<ActivitySpokenSignUser> activitySpokenSignUsers  =getActivitySignUserList(activitySpokenSignIndexListDto,gameStage,stageMode);
//        List<ActivitySpokenSignUser> activitySpokenSignUsers  = activitySpokenSignMapper.selectUserList(activitySpokenSignIndexListDto);
        if(gameStage==1){
            String dateStr = activitySpokenSignIndexListDto.getDateStr();
            if (StringUtils.isEmpty(dateStr)) {
                dateStr = DateUtils.format(new Date(), DateUtils.YYYY_MM_DD);
            }
            String selectDateStr = dateStr;
            List<ActivitySignRecord> dailyStarList = getDailyStarList(activityBaseId, activityContentId, stage, selectDateStr);
            Map<String, ActivitySignRecord> userIdMap = dailyStarList.stream().collect(Collectors.toMap(ActivitySignRecord::getUserId, Function.identity()));
            Integer weekNum = getWeekNum(activityBaseId, activityContentId, gameStage, dateStr);
//            activitySpokenSignUsers = activitySpokenSignMapper.selectUserList(activitySpokenSignIndexListDto);
            Optional.ofNullable(activitySpokenSignUsers).orElse(new ArrayList<>()).forEach(activitySpokenSignUser -> {
                String userId = activitySpokenSignUser.getUserId();
                if (userIdMap.containsKey(userId)) {
                    ActivitySignRecord activitySignRecord = userIdMap.get(userId);
                    activitySpokenSignUser.setRankNum(dailyStarList.indexOf(activitySignRecord) + 1);
                    activitySpokenSignUser.setDayScore(activitySignRecord.getAprScore() + "");
                } else {
                    ActivitySignRecord activitySignRecord = activitySignRecordMapper.selectRecord(activityBaseId, userId, selectDateStr);
                    if (activitySignRecord == null) {
                        activitySpokenSignUser.setDayScore("-");
                    } else {
                        activitySpokenSignUser.setDayScore(activitySignRecord.getAprScore() + "");
                    }
                }
                int weekSignCount = 0;
                switch (weekNum) {
                    case 1:
                        weekSignCount = activitySpokenSignUser.getFirstWeekSignCout();
                        break;
                    case 2:
                        weekSignCount = activitySpokenSignUser.getSecondWeekSignCout();
                        break;
                    case 3:
                        weekSignCount = activitySpokenSignUser.getThirdWeekSignCout();
                        break;
                    case 4:
                        weekSignCount = activitySpokenSignUser.getFourWeekSignCout();
                        break;
                }
                activitySpokenSignUser.setWeekSignCount(weekSignCount);
                activitySpokenSignUser.setTotalSignCount(activitySpokenSignUser.getFirstWeekSignCout() + activitySpokenSignUser.getSecondWeekSignCout() + activitySpokenSignUser.getThirdWeekSignCout() + activitySpokenSignUser.getFourWeekSignCout());
            });
            if (CollectionUtils.isNotEmpty(activitySpokenSignUsers)) {
                List<ActivitySpokenSignUser> rankNumList = activitySpokenSignUsers.stream().filter(item -> item.getRankNum() != null).sorted(Comparator.comparing(ActivitySpokenSignUser::getRankNum)).collect(Collectors.toList());
                List<ActivitySpokenSignUser> noNumList = activitySpokenSignUsers.stream().filter(item -> item.getRankNum() == null).collect(Collectors.toList());
                activitySpokenSignUsers.clear();
                activitySpokenSignUsers.addAll(rankNumList);
                activitySpokenSignUsers.addAll(noNumList);
            }
        }else if(gameStage==2){
            List<ActivitySpokenSignUser> topUserList = new ArrayList<>();
            switch (stageMode) {
                case 100:
                    topUserList = activitySpokenSignMapper.selectTop100ByStage(activityBaseId, stage);
                    break;
                case 10:
                    topUserList = activitySpokenSignMapper.selectTop10ByStage(activityBaseId, stage);
                    break;
            }
            Map<String, Integer> topUserMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(topUserList)) {
                topUserMap = topUserList.stream().collect(Collectors.toMap(ActivitySpokenSignUser::getUserId, ActivitySpokenSignUser::getRankNum));
            }
           /* if(stageMode==100){
                activitySpokenSignUsers= activitySpokenSignMapper.selectUserList(activitySpokenSignIndexListDto);
            }else if(stageMode==10){
                activitySpokenSignUsers= activitySpokenSignMapper.selectEnter100UserList(activitySpokenSignIndexListDto);
            }*/

            if(CollectionUtils.isNotEmpty(activitySpokenSignUsers)){
                for(ActivitySpokenSignUser activitySpokenSignUser:activitySpokenSignUsers){
                    String userId = activitySpokenSignUser.getUserId();
                    if(topUserMap.containsKey(userId)){
                        activitySpokenSignUser.setRankNum(topUserMap.get(userId));
                    }
                }
                List<ActivitySpokenSignUser> rankNumList = activitySpokenSignUsers.stream().filter(item -> item.getRankNum() != null).sorted(Comparator.comparing(ActivitySpokenSignUser::getRankNum)).collect(Collectors.toList());
                List<ActivitySpokenSignUser> noNumList = activitySpokenSignUsers.stream().filter(item -> item.getRankNum() == null).collect(Collectors.toList());
                List<ActivitySpokenSignUser> scoreList=null;
                List<ActivitySpokenSignUser> noScoreList=null;
                if(stageMode==100){
                    scoreList = noNumList.stream().filter(item -> item.getTop100TotalScore() != null).sorted(Comparator.comparing(ActivitySpokenSignUser::getTop100TotalScore).reversed()).collect(Collectors.toList());
                    noScoreList = noNumList.stream().filter(item -> item.getTop100TotalScore() == null).collect(Collectors.toList());
                }else if(stageMode==10){
                    scoreList = noNumList.stream().filter(item -> item.getTop10TotalScore() != null).sorted(Comparator.comparing(ActivitySpokenSignUser::getTop10TotalScore).reversed()).collect(Collectors.toList());
                    noScoreList = noNumList.stream().filter(item -> item.getTop10TotalScore() == null).collect(Collectors.toList());
                }
                activitySpokenSignUsers.clear();
                activitySpokenSignUsers.addAll(rankNumList);
                activitySpokenSignUsers.addAll(scoreList);
                activitySpokenSignUsers.addAll(noScoreList);
            }
        }else if(gameStage==3){
//            activitySpokenSignUsers = activitySpokenSignMapper.selectEnter10UserList(activitySpokenSignIndexListDto);
            if(CollectionUtils.isNotEmpty(activitySpokenSignUsers)){
                List<ActivitySpokenSignUser> rankNumList = activitySpokenSignUsers.stream().filter(item -> item.getFinalRank() != -1).sorted(Comparator.comparing(ActivitySpokenSignUser::getFinalRank)).collect(Collectors.toList());
                List<ActivitySpokenSignUser> noNumList = activitySpokenSignUsers.stream().filter(item -> item.getFinalRank() == -1).collect(Collectors.toList());
                activitySpokenSignUsers.clear();
                activitySpokenSignUsers.addAll(rankNumList);
                activitySpokenSignUsers.addAll(noNumList);
            }
        }

        return activitySpokenSignUsers;
    }

    private List<ActivitySpokenSignUser> getActivitySignUserList(ActivitySpokenSignIndexListDto activitySpokenSignIndexListDto,Integer gameStage, Integer stageMode) {
        if(gameStage==1){
            return activitySpokenSignMapper.selectUserList(activitySpokenSignIndexListDto);
        }
        if(gameStage==2){
            if(stageMode==100){
               return activitySpokenSignMapper.selectUserList(activitySpokenSignIndexListDto);
            }else if(stageMode==10){
               return activitySpokenSignMapper.selectEnter100UserList(activitySpokenSignIndexListDto);
            }
            return activitySpokenSignMapper.selectUserList(activitySpokenSignIndexListDto);
        }
        if(gameStage==3){
            return activitySpokenSignMapper.selectEnter10UserList(activitySpokenSignIndexListDto);
        }
        return null;
    }

    private List<ActivitySignRecord> getDailyStarList(Integer activityBaseId, Integer activityContentId, Integer stage, String dateStr) {
        if (stage == 11) {
            stage = 1;
        }
        if (stage == 21) {
            stage = 2;
        }
        String todayStr = DateUtils.format(new Date(), DateUtils.YYYY_MM_DD);
        List<ActivitySignRecord> resultList=new ArrayList<>();
        if(todayStr.equals(dateStr)){
            resultList= activitySignRecordMapper.selectTopHundred(activityBaseId, activityContentId, stage, dateStr);
        }else {
            Object cacheObject = redisCache.getCacheObject("dailyStarList_" + activityBaseId + "_" + activityContentId + "_" + stage + "_" + dateStr);
            if (cacheObject != null) {
                resultList= JSONObject.parseArray(cacheObject.toString(), ActivitySignRecord.class);
            }else {
                resultList= activitySignRecordMapper.selectTopHundred(activityBaseId, activityContentId, stage, dateStr);
                redisCache.setCacheObject("dailyStarList_" + activityBaseId + "_" + activityContentId + "_" + stage + "_" + dateStr,JSONObject.toJSONString(resultList),60, TimeUnit.DAYS);
            }
        }
        return resultList;
    }

    private Integer getWeekNum(Integer activityBaseId, Integer activityContentId, Integer activityGameStage, String dateStr) {
        dateStr = dateStr + " 00:00:02";
        Date date = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, dateStr);
        List<ActivityStageConfig> activityGameStageWeekConfigList = getActivityGameStageWeekConfigList(activityBaseId, activityContentId, activityGameStage);
        if (CollectionUtils.isNotEmpty(activityGameStageWeekConfigList)) {
            ActivityStageConfig activityStageConfig = activityGameStageWeekConfigList.stream().filter(item -> item.getStartTime().before(date) && item.getEndTime().after(date)).findFirst().orElse(null);
            if (activityStageConfig != null) {
                return activityStageConfig.getWeekNum();
            }
        }
        return 1;
    }

    private List<ActivityStageConfig> getActivityGameStageWeekConfigList(Integer activityBaseId, Integer activityContentId, Integer activityGameStage) {
        List<ActivityStageConfig> activityStageConfigs = new ArrayList<>();
        String type = "stage" + activityGameStage + "WeekConfig";
        List<String> dbList = activityContentConfigMapper.selectByType(activityContentId, type);
        if (CollectionUtils.isNotEmpty(dbList)) {
            for (String string : dbList) {
                ActivityStageConfig activityStageConfig = JSONObject.parseObject(string, ActivityStageConfig.class);
                activityStageConfigs.add(activityStageConfig);
            }
        }
        return activityStageConfigs;
    }

    @Override
    public void fourthExport(List<ActivitySpokenSignUser> list, HttpServletResponse response, ActivitySpokenSignIndexListDto activitySpokenSignIndexListDto) {
        List<ActivitySpokenSignUser> activitySpokenSignUsers = fourthList(activitySpokenSignIndexListDto);
        //excel工作空间名
        String sheetName = "信息表";
        List<String[]> rows = new ArrayList<>();
        String fileName = "";
        String fileNamePre = "2024第四届口语打卡争霸赛-";
        String fileNameMiddle = "";
        String fileNameEnd = ".xlsx";
        if (CollectionUtils.isNotEmpty(activitySpokenSignUsers)) {
            switch (activitySpokenSignIndexListDto.getGameStage()) {
                case 1:
                    fileNameMiddle = "全国口语训练赛";
                    String[] headers1 = {"姓名", "账号", "学段", "当天成绩", "当天排名", "本周打卡数", "累计打卡数"};
                    for (ActivitySpokenSignUser activitySpokenSignUser : activitySpokenSignUsers) {
                        String[] row = new String[7];
                        row[0] = activitySpokenSignUser.getUserName();
                        row[1] = activitySpokenSignUser.getUserId();
                        row[2] = activitySpokenSignUser.getStageDesc();
                        row[3] = activitySpokenSignUser.getDayScore();
                        row[4] = activitySpokenSignUser.getRankNum()==null?"未上榜":(activitySpokenSignUser.getRankNum()+"");
                        row[5] = activitySpokenSignUser.getWeekSignCount() + "";
                        row[6] = activitySpokenSignUser.getTotalSignCount() + "";
                        rows.add(row);
                    }
                    fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                    ActivityExcleUtil.exportExcel(rows, headers1, fileName, sheetName, response);
                    break;
                case 2:
                    Integer stageMode = activitySpokenSignIndexListDto.getStageMode();
                    if(stageMode==100){
                        fileNameMiddle = "全国口语晋级赛100强争霸赛";
                    }else if(stageMode==10){
                        fileNameMiddle = "全国口语训练赛10强争霸赛";
                    }
                    String[] headers2 = {"姓名", "账号", "学段", "当前赛段", "最高总分", "当前排名"};
                    for (ActivitySpokenSignUser activitySpokenSignUser : activitySpokenSignUsers) {
                        String[] row = new String[6];
                        row[0] = activitySpokenSignUser.getUserName();
                        row[1] = activitySpokenSignUser.getUserId();
                        row[2] = activitySpokenSignUser.getStageDesc();
                        row[3] = stageMode==100?"全国100强晋级赛":"全国10强晋级赛";
                        row[4] = stageMode==100?(activitySpokenSignUser.getTop100TotalScore()==null?"未测":activitySpokenSignUser.getTop100TotalScore()+""):(activitySpokenSignUser.getTop10TotalScore()==null?"未测":activitySpokenSignUser.getTop10TotalScore()+"");
                        row[5] = activitySpokenSignUser.getRankNum()==null?"未上榜":(activitySpokenSignUser.getRankNum()+"");
                        rows.add(row);
                    }
                    fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                    ActivityExcleUtil.exportExcel(rows, headers2, fileName, sheetName, response);
                    break;
                case 3:
                    fileNameMiddle = "全国口语总决赛";
                    String[] headers3 = {"姓名", "账号", "学段", "当前赛段", "最高总分", "当前排名"};
                    for (ActivitySpokenSignUser activitySpokenSignUser : activitySpokenSignUsers) {
                        String[] row = new String[6];
                        row[0] = activitySpokenSignUser.getUserName();
                        row[1] = activitySpokenSignUser.getUserId();
                        row[2] = activitySpokenSignUser.getStageDesc();
                        row[3] = "全国口语总决赛";
                        row[4] = activitySpokenSignUser.getIsEnter10()?activitySpokenSignUser.getFinalScore()+"":"未晋级";
                        row[5] = activitySpokenSignUser.getIsEnter10()?activitySpokenSignUser.getFinalRank()+"":"未晋级";
                        rows.add(row);
                    }
                    fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                    ActivityExcleUtil.exportExcel(rows, headers3, fileName, sheetName, response);
                    break;
            }
        }
    }

    @Override
    public List<ActivityDrawRecord> fourthUserDrawRecordList(ActivityUserDrawRecordListDto activityUserDrawRecordListDto) {
        Integer activityBaseId = activityUserDrawRecordListDto.getActivityBaseId();
        String userId = activityUserDrawRecordListDto.getUserId();
        List<ActivityDrawRecord> activityDrawRecords = activitySpokenSignMapper.selectUserDrawRecordList(activityBaseId, userId);
        if(CollectionUtils.isNotEmpty(activityDrawRecords)){
            activityDrawRecords.forEach(item->{
                item.setCreateTimeStr(DateUtils.format(item.getCreateTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            });
        }
        return activityDrawRecords;
    }
}
