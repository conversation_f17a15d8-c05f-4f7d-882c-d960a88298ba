package com.redbook.system.service;

import com.redbook.system.domain.BizClueLog;
import com.redbook.system.domain.vo.BizClueLogVO;

import java.util.List;

/**
 * 线索动态日志Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
public interface IBizClueLogService 
{
    /**
     * 查询线索动态日志
     * 
     * @param clueLogId 线索动态日志主键
     * @return 线索动态日志
     */
     BizClueLog selectBizClueLogByClueLogId(Long clueLogId);

    /**
     * 查询线索动态日志列表
     * 
     * @param bizClueLog 线索动态日志
     * @return 线索动态日志集合
     */
     List<BizClueLog> selectBizClueLogList(BizClueLog bizClueLog);

     /**
     * 查询线索动态日志VO列表
     *
     * @param bizClueLog 线索动态日志
     * @return 线索动态日志集合
     */
     List<BizClueLogVO> selectBizClueLogVOList(BizClueLog bizClueLog);

    /**
     * 新增线索动态日志
     * 
     * @param bizClueLog 线索动态日志
     * @return 结果
     */
     int insertBizClueLog(BizClueLog bizClueLog);

     /**
     * 批量新增线索动态日志
     *
     * @param list 线索动态日志列表
     * @return 结果
     */
     int batchInsertBizClueLog(List<BizClueLog> list);

    /**
     * 修改线索动态日志
     * 
     * @param bizClueLog 线索动态日志
     * @return 结果
     */
     int updateBizClueLog(BizClueLog bizClueLog);

    /**
     * 批量删除线索动态日志
     * 
     * @param clueLogIds 需要删除的线索动态日志主键集合
     * @return 结果
     */
     int deleteBizClueLogByClueLogIds(Long[] clueLogIds);

    /**
     * 删除线索动态日志信息
     * 
     * @param clueLogId 线索动态日志主键
     * @return 结果
     */
     int deleteBizClueLogByClueLogId(Long clueLogId);
}
