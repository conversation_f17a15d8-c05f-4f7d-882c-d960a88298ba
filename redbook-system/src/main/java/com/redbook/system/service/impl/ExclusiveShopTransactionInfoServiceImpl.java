package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.ExclusiveShopTransactionInfo;
import com.redbook.system.mapper.ExclusiveShopTransactionInfoMapper;
import com.redbook.system.service.IExclusiveShopTransactionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 专卖店交易记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-21
 */
@Service
public class ExclusiveShopTransactionInfoServiceImpl extends ServiceImpl<ExclusiveShopTransactionInfoMapper, ExclusiveShopTransactionInfo> implements IExclusiveShopTransactionInfoService
{
    @Autowired
    private ExclusiveShopTransactionInfoMapper exclusiveShopTransactionInfoMapper;

    /**
     * 查询专卖店交易记录
     * 
     * @param id 专卖店交易记录主键
     * @return 专卖店交易记录
     */
    @Override
    public ExclusiveShopTransactionInfo selectExclusiveShopTransactionInfoById(Long id)
    {
        return exclusiveShopTransactionInfoMapper.selectExclusiveShopTransactionInfoById(id);
    }

    /**
     * 查询专卖店交易记录列表
     * 
     * @param exclusiveShopTransactionInfo 专卖店交易记录
     * @return 专卖店交易记录
     */
    @Override
    public List<ExclusiveShopTransactionInfo> selectExclusiveShopTransactionInfoList(ExclusiveShopTransactionInfo exclusiveShopTransactionInfo)
    {
        return exclusiveShopTransactionInfoMapper.selectExclusiveShopTransactionInfoList(exclusiveShopTransactionInfo);
    }

    /**
     * 新增专卖店交易记录
     * 
     * @param exclusiveShopTransactionInfo 专卖店交易记录
     * @return 结果
     */
    @Override
    public int insertExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo exclusiveShopTransactionInfo)
    {
        exclusiveShopTransactionInfo.setCreateTime(DateUtils.getNowDate());
        return exclusiveShopTransactionInfoMapper.insertExclusiveShopTransactionInfo(exclusiveShopTransactionInfo);
    }

    /**
     * 修改专卖店交易记录
     * 
     * @param exclusiveShopTransactionInfo 专卖店交易记录
     * @return 结果
     */
    @Override
    public int updateExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo exclusiveShopTransactionInfo)
    {
        exclusiveShopTransactionInfo.setUpdateTime(DateUtils.getNowDate());
        return exclusiveShopTransactionInfoMapper.updateExclusiveShopTransactionInfo(exclusiveShopTransactionInfo);
    }

    /**
     * 批量删除专卖店交易记录
     * 
     * @param ids 需要删除的专卖店交易记录主键
     * @return 结果
     */
    @Override
    public int deleteExclusiveShopTransactionInfoByIds(Long[] ids)
    {
        return exclusiveShopTransactionInfoMapper.deleteExclusiveShopTransactionInfoByIds(ids);
    }

    /**
     * 删除专卖店交易记录信息
     * 
     * @param id 专卖店交易记录主键
     * @return 结果
     */
    @Override
    public int deleteExclusiveShopTransactionInfoById(Long id)
    {
        return exclusiveShopTransactionInfoMapper.deleteExclusiveShopTransactionInfoById(id);
    }
}
