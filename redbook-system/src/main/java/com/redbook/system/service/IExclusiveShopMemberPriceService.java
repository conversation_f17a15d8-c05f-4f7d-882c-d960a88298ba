package com.redbook.system.service;

import com.redbook.system.domain.ExclusiveShopMemberPrice;

import java.util.List;

/**
 * 专卖店会员价格Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-21
 */
public interface IExclusiveShopMemberPriceService 
{
    /**
     * 查询专卖店会员价格
     * @return 专卖店会员价格
     */
     ExclusiveShopMemberPrice selectExclusiveShopMemberPrice(Integer exclusiveShopId,Integer stage,Integer timeLen);

    /**
     * 查询专卖店会员价格列表
     * 
     * @param exclusiveShopMemberPrice 专卖店会员价格
     * @return 专卖店会员价格集合
     */
     List<ExclusiveShopMemberPrice> selectExclusiveShopMemberPriceList(ExclusiveShopMemberPrice exclusiveShopMemberPrice);

    /**
     * 保存专卖店会员价格
     * @return 结果
     */
     boolean saveExclusiveShopMemberPrice(List<ExclusiveShopMemberPrice> priceList);

    /**
     * 批量删除专卖店会员价格
     * 
     * @param ids 需要删除的专卖店会员价格主键集合
     * @return 结果
     */
     int deleteExclusiveShopMemberPriceByIds(Long[] ids);

    /**
     * 删除专卖店会员价格信息
     * 
     * @param id 专卖店会员价格主键
     * @return 结果
     */
     int deleteExclusiveShopMemberPriceById(Long id);
}
