package com.redbook.system.service.impl.activity;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.ActivityExcleUtil;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.StringUtils;
import com.redbook.system.domain.ActivityDcwThirdUser;
import com.redbook.system.domain.dto.ActivityDcwThirdIndexListDto;
import com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank;
import com.redbook.system.mapper.ActivityDcwFourthUserMapper;
import com.redbook.system.mapper.ActivityDcwThirdMapper;
import com.redbook.system.service.activity.IActivityDcwFourthService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ActivityDcwFourthServiceImpl extends ServiceImpl<ActivityDcwThirdMapper, ActivityDcwThirdUser> implements IActivityDcwFourthService {
    //    @Autowired
//    ActivityDcwThirdMapper activityDcwThirdMapper;
    @Autowired
    ActivityDcwFourthUserMapper activityDcwFourthUserMapper;

    @Override
    public List<ActivityDcwThirdUser> selectRecordList(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto) {
        Integer activityBaseId = activityDcwThirdIndexListDto.getActivityBaseId();
        Integer gameStage = activityDcwThirdIndexListDto.getGameStage();
        Integer jinJiSaiMode = activityDcwThirdIndexListDto.getJinJiSaiMode();
        Integer stage = activityDcwThirdIndexListDto.getStage();
        List<ActivityDcwThirdUser> activityDcwThirdUsers = new ArrayList<>();
        List<ActivityDcwThirdPersonalRank> topUserList = new ArrayList<>();
        Map<String, ActivityDcwThirdPersonalRank> rankMap = new HashMap<>();
        switch (gameStage) {
            case 1:
                activityDcwThirdUsers = activityDcwFourthUserMapper.selectGameStage1List(activityDcwThirdIndexListDto);
                break;
            case 2:
                activityDcwThirdUsers = activityDcwFourthUserMapper.selectGameStage2List(activityDcwThirdIndexListDto);
                break;
            case 3:
                if (jinJiSaiMode == 100) {
                    activityDcwThirdUsers = activityDcwFourthUserMapper.selectJinJiSaiMode100List(activityDcwThirdIndexListDto);
                    if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                        topUserList = activityDcwFourthUserMapper.selectJinJiSaiMode100TopList(activityBaseId, stage, 100);
                        rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                        for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                            activityDcwThirdUser.setRank("未上榜");
                            if (activityDcwThirdUser.getMaxScore() != null && rankMap.containsKey(activityDcwThirdUser.getUserId())) {
                                activityDcwThirdUser.setRank(rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString());
                            }
                        }
                    }
                } else if (jinJiSaiMode == 32) {
                    activityDcwThirdUsers = activityDcwFourthUserMapper.selectJinJiSaiMode32List(activityDcwThirdIndexListDto);
                    if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                        topUserList = activityDcwFourthUserMapper.selectJinJiSaiMode32TopList(activityBaseId, stage, 32);
                        rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                        for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                            activityDcwThirdUser.setRank("未上榜");
                            if (activityDcwThirdUser.getMaxScore() != null && rankMap.containsKey(activityDcwThirdUser.getUserId())) {
                                activityDcwThirdUser.setRank(rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString());
                            }
                        }
                    }

                } else if (jinJiSaiMode == 16) {
                    activityDcwThirdUsers = activityDcwFourthUserMapper.selectJinJiSaiMode16List(activityDcwThirdIndexListDto);
                    if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                        topUserList = activityDcwFourthUserMapper.selectJinJiSaiMode16TopList(activityBaseId, stage, 16);
                        rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                        for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                            activityDcwThirdUser.setRank("未上榜");
                            if (activityDcwThirdUser.getMaxScore() != null && rankMap.containsKey(activityDcwThirdUser.getUserId())) {
                                activityDcwThirdUser.setRank(rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString());
                            }
                        }
                    }

                } else if (jinJiSaiMode == 8) {
                    activityDcwThirdUsers = activityDcwFourthUserMapper.selectJinJiSaiMode8List(activityDcwThirdIndexListDto);
                    if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                        topUserList = activityDcwFourthUserMapper.selectJinJiSaiMode8TopList(activityBaseId, stage, 8);
                        rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                        for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                            activityDcwThirdUser.setRank("未上榜");
                            if (activityDcwThirdUser.getMaxScore() != null && rankMap.containsKey(activityDcwThirdUser.getUserId())) {
                                activityDcwThirdUser.setRank(rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString());
                            }
                        }
                    }
                }

                break;
            case 5:
                activityDcwThirdUsers = activityDcwFourthUserMapper.selectFinalList(activityDcwThirdIndexListDto);
                break;
        }
        return activityDcwThirdUsers;
    }

    @Override
    public void export(List<ActivityDcwThirdUser> list, HttpServletResponse response, ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto) {
        try {
            Integer gameStage = activityDcwThirdIndexListDto.getGameStage();
            Integer gameStage4Mode = activityDcwThirdIndexListDto.getJinJiSaiMode();
            //excel工作空间名
            String sheetName = "信息表";
            List<String[]> rows = new ArrayList<>();
            String fileName = "";
            String fileNamePre = "2025第四届单词王争霸赛";
            String fileNameMiddle = "";
            String fileNameEnd = ".xlsx";
            if (CollectionUtil.isNotEmpty(list)) {
                switch (gameStage) {
                    case 1:
                    case 2:
                        fileNameMiddle = gameStage == 1 ? "预赛" : "初赛";
                        String[] headers1 = {"姓名", "账号", "学段", "最高成绩"};
                        for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                            String[] row = new String[4];
                            row[0] = activityDcwThirdUser.getUserName();
                            row[1] = activityDcwThirdUser.getUserId();
                            row[2] = activityDcwThirdUser.getStage() == 1 ? "小学" : "初中";
                            row[3] = activityDcwThirdUser.getMaxScore() + "";
                            rows.add(row);
                        }
                        fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                        ActivityExcleUtil.exportExcel(rows, headers1, fileName, sheetName, response);
                        break;
                    case 3:
                        if (gameStage4Mode == 100) {
                            fileNameMiddle = "全国100强";
                        } else if (gameStage4Mode == 32) {
                            fileNameMiddle = "全国32强";
                        } else if (gameStage4Mode == 16) {
                            fileNameMiddle = "全国16强";
                        } else if (gameStage4Mode == 8) {
                            fileNameMiddle = "全国8强";
                        }
                        String[] headers3 = {"姓名", "账号", "学段", "当前赛程", "最高成绩", "当前排名"};
                        for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                            String[] row = new String[6];
                            row[0] = activityDcwThirdUser.getUserName();
                            row[1] = activityDcwThirdUser.getUserId();
                            row[2] = activityDcwThirdUser.getStage() == 1 ? "小学" : "初中";
                            row[3] = fileNameMiddle;
                            row[4] = activityDcwThirdUser.getMaxScore() + "";
                            row[5] = activityDcwThirdUser.getRank() + "";
                            rows.add(row);
                        }
                        fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                        ActivityExcleUtil.exportExcel(rows, headers3, fileName, sheetName, response);
                        break;
                    case 5:
                        fileNameMiddle = "总决赛";
                        String[] headers5 = {"姓名", "账号", "学段", "成绩", "效率", "排名"};
                        for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                            String[] row = new String[6];
                            row[0] = activityDcwThirdUser.getUserName();
                            row[1] = activityDcwThirdUser.getUserId();
                            row[2] = activityDcwThirdUser.getStage() == 1 ? "小学" : "初中";
                            row[3] = activityDcwThirdUser.getMaxScore() + "";
                            row[4] = activityDcwThirdUser.getEfficiency() + "";
                            row[5] = activityDcwThirdUser.getRank() + "";
                            rows.add(row);
                        }
                        fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                        ActivityExcleUtil.exportExcel(rows, headers5, fileName, sheetName, response);
                }
            }
        } catch (Exception e) {

        }
    }
}
