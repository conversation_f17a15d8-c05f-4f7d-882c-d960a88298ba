package com.redbook.system.service.kids.impl;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.redbook.common.annotation.DataSource;
import com.redbook.common.constant.HttpStatus;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.DataSourceType;
import com.redbook.common.utils.IdentityGenerator;
import com.redbook.common.utils.StringUtils;
import com.redbook.common.utils.sign.Md5Utils;
import com.redbook.system.domain.AgentUser;
import com.redbook.system.domain.UserInfo;
import com.redbook.system.domain.dto.UserClassChangeDto;
import com.redbook.system.domain.vo.ClassInfo;
import com.redbook.system.domain.vo.TableDataSummaryInfoVo;
import com.redbook.system.service.*;
import com.redbook.system.service.kids.IKidUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import com.redbook.system.mapper.KidUserInfoMapper;
import com.redbook.system.domain.kids.KidUserInfo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
/**
 * 棒棒糖用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class KidUserInfoServiceImpl extends ServiceImpl<KidUserInfoMapper, KidUserInfo> implements IKidUserInfoService
{
    @Autowired
    private KidUserInfoMapper kidUserInfoMapper;
    @Autowired
    private IAgentUserService agentUserService;
    @Autowired
    IAgentService agentService;
    @Autowired
    IExclusiveShopService exclusiveShopService;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 查询棒棒糖用户
     * 
     * @param id 棒棒糖用户主键
     * @return 棒棒糖用户
     */
    @Override
    public KidUserInfo selectKidUserInfoById(Long id)
    {
        return kidUserInfoMapper.selectKidUserInfoById(id);
    }

    /**
     * 查询棒棒糖用户列表
     * 
     * @param kidUserInfo 棒棒糖用户
     * @return 棒棒糖用户
     */
    @Override
    public List<KidUserInfo> selectKidUserInfoList(KidUserInfo kidUserInfo)
    {
        return kidUserInfoMapper.selectKidUserInfoList(kidUserInfo);
    }

    /**
     * 新增棒棒糖用户
     * 
     * @param kidUserInfo 棒棒糖用户
     * @return 结果
     */
    @Override
    public int insertKidUserInfo(KidUserInfo kidUserInfo)
    {
        return kidUserInfoMapper.insertKidUserInfo(kidUserInfo);
    }

    /**
     * 修改棒棒糖用户
     * 
     * @param kidUserInfo 棒棒糖用户
     * @return 结果
     */
    @Override
    public int updateKidUserInfo(KidUserInfo kidUserInfo)
    {
        return kidUserInfoMapper.updateKidUserInfo(kidUserInfo);
    }

    /**
     * 批量删除棒棒糖用户
     * 
     * @param ids 需要删除的棒棒糖用户主键
     * @return 结果
     */
    @Override
    public int deleteKidUserInfoByIds(Long[] ids)
    {
        return kidUserInfoMapper.deleteKidUserInfoByIds(ids);
    }

    /**
     * 删除棒棒糖用户信息
     * 
     * @param id 棒棒糖用户主键
     * @return 结果
     */
    @Override
    public int deleteKidUserInfoById(Long id)
    {
        return kidUserInfoMapper.deleteKidUserInfoById(id);
    }

    @Override
    public TableDataInfo<KidUserInfo> selectUserInfoList(KidUserInfo userInfo) {
        List<KidUserInfo> userInfoList = kidUserInfoMapper.selectUserInfoList(userInfo);
        userInfoList.forEach(item -> {
            if (item.getAid() != null) {
                item.setAgentName(agentService.getAgentName(item.getAid()));
            }
            if (item.getExclusiveShopId() != null) {
                item.setExclusiveShopName(exclusiveShopService.getExclusiveShopName(item.getExclusiveShopId()));
            }
            if (item.getTeachId() != null){
                item.setTeachName(agentUserService.getTeacherName(item.getTeachId()));
            }
            if (item.getClassId() != null){
                KidUserInfoServiceImpl proxy = applicationContext.getBean(KidUserInfoServiceImpl.class);
                item.setClassName(proxy.getClassName(item.getClassId()));
            }
        });
        TableDataInfo<KidUserInfo> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(userInfoList);
        return rspData;
    }

    @Override
    public TableDataSummaryInfoVo countUserInfoList(KidUserInfo userInfo) {
        int total = 0;
        StringBuilder sb = new StringBuilder();
        Map<String, Object> stringObjectMap = kidUserInfoMapper.countUserInfoList(userInfo);
        if (stringObjectMap == null || stringObjectMap.size() <= 0) {
            sb.append("会员数量：<span>0</span>个。");
        } else {
            if (userInfo.getMemberType() == null || userInfo.getMemberType() > 0) {
                int svipNum = stringObjectMap.get("svipNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("svipNum").toString());
                int vipNum = stringObjectMap.get("vipNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("vipNum").toString());
                int ordinaryNum = stringObjectMap.get("ordinaryNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("ordinaryNum").toString());
                total += svipNum + vipNum + ordinaryNum;
                sb.append("正式会员：").append("<span>").append(ordinaryNum + vipNum + svipNum).append("</span>").append("个");
                sb.append("（普通会员：").append(ordinaryNum).append("个，");
                sb.append("vip会员：").append(vipNum).append("个，");
                sb.append("超级会员：").append(svipNum).append("个）");
            }
            if (userInfo.getMemberType() == null || userInfo.getMemberType() <= 0) {
                int experienceNum = stringObjectMap.get("experienceNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("experienceNum").toString());
                total += experienceNum;
                if (sb.length() > 0) {
                    sb.append("；");
                }
                sb.append("体验会员：").append("<span>").append(experienceNum).append("</span>").append("个");
            }
            sb.append("。");
        }
        return TableDataSummaryInfoVo.builder().summaryInfo(sb.toString()).total(total).build();
    }

    @Override
    public Boolean updateStudentInfo(KidUserInfo user) {
        return updateKidUserInfo(user)>0;
    }

    @Override
    @DataSource(value = DataSourceType.KIDS)
    public String resetPassword(String userId) {
        LambdaQueryWrapper<KidUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KidUserInfo::getUserId, userId);
        if (count(queryWrapper) > 0) {
            KidUserInfo userInfo = new KidUserInfo();
            userInfo.setUserId(userId);
            userInfo.setId(userInfo.getId());
            String password = IdentityGenerator.randomString(6);
            userInfo.setPassword(Md5Utils.hash(password));
            updateKidUserInfo(userInfo);
            return password;
        }
        return null;
    }

    @Override
    public List<ClassInfo> getClassList(String teacherId) {
        List<ClassInfo> classList = kidUserInfoMapper.getClassList(teacherId);
        if (classList == null || classList.isEmpty()) {
            AgentUser agentUser = agentUserService.selectAgentUserByUserId(teacherId);
            if (agentUser==null){
                return null;
            }
            kidUserInfoMapper.createDefaultClass(teacherId,agentUser.getAid());
            return getClassList(teacherId);
        }
        return classList;
    }

    @Override
    public Boolean updateStudentClass(UserClassChangeDto dto) {
        if (StringUtils.isBlank(dto.getAid()) && dto.getExclusiveShopId() == null) {
            return false;
        }
        if (dto.getExclusiveShopId() != null && StringUtils.isBlank(dto.getAid())) {
            dto.setAid(agentService.selectAgentById(exclusiveShopService.selectExclusiveShopById(dto.getExclusiveShopId()).getAgentId()).getAid());
        }
        return kidUserInfoMapper.updateStudentClass(dto)>0;
    }

    @Override
    @DataSource(value = DataSourceType.KIDS)
    public KidUserInfo selectUserInfoByUserId(String userId) {
        return kidUserInfoMapper.selectKidUserInfoByUserId(userId);
    }

    @Override
    @DataSource(value = DataSourceType.KIDS)
    public boolean renewKidUser(String userId, Integer memberType, String expirationDate, String firstPurchaseDate, String lastPurchaseDate) {
        return kidUserInfoMapper.renewKidUser(userId, memberType, expirationDate, firstPurchaseDate, lastPurchaseDate) > 0;
    }

    @Cacheable(value = "KidClassCache", key = "#classId",unless = "#result == null")
    @Override
    public String getClassName(Long classId) {
        return kidUserInfoMapper.getClassName(classId);
    }
}
