package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.domain.entity.SysUser;
import com.redbook.common.enums.TransactionFundType;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.ElectronicRenewCard;
import com.redbook.system.domain.UserInfo;
import com.redbook.system.domain.dto.ElectronicRenewCardBuyDto;
import com.redbook.system.domain.dto.ElectronicRenewCardTransferDto;
import com.redbook.system.mapper.ElectronicRenewCardMapper;
import com.redbook.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 电子续费卡Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-16
 */
@Service
public class ElectronicRenewCardServiceImpl extends ServiceImpl<ElectronicRenewCardMapper, ElectronicRenewCard> implements IElectronicRenewCardService {
    @Autowired
    private ElectronicRenewCardMapper electronicRenewCardMapper;
    @Autowired
    private IAgentAccountService agentAccountService;
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IAgentTransactionInfoService iAgentTransactionInfoService;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IExclusiveShopService exclusiveShopService;

    /**
     * 查询电子续费卡
     *
     * @param id 电子续费卡主键
     * @return 电子续费卡
     */
    @Override
    public ElectronicRenewCard selectElectronicRenewCardById(Long id) {
        return electronicRenewCardMapper.selectElectronicRenewCardById(id);
    }

    /**
     * 查询电子续费卡列表
     *
     * @param electronicRenewCard 电子续费卡
     * @return 电子续费卡
     */
    @Override
    public List<ElectronicRenewCard> selectElectronicRenewCardList(ElectronicRenewCard electronicRenewCard) {
        return electronicRenewCardMapper.selectElectronicRenewCardList(electronicRenewCard);
    }

    /**
     * 新增电子续费卡
     *
     * @param electronicRenewCard 电子续费卡
     * @return 结果
     */
    @Override
    public int insertElectronicRenewCard(ElectronicRenewCard electronicRenewCard) {
        electronicRenewCard.setCreateTime(DateUtils.getNowDate());
        return electronicRenewCardMapper.insertElectronicRenewCard(electronicRenewCard);
    }

    /**
     * 修改电子续费卡
     *
     * @param electronicRenewCard 电子续费卡
     * @return 结果
     */
    @Override
    public int updateElectronicRenewCard(ElectronicRenewCard electronicRenewCard) {
        electronicRenewCard.setUpdateTime(DateUtils.getNowDate());
        return electronicRenewCardMapper.updateElectronicRenewCard(electronicRenewCard);
    }

    /**
     * 批量删除电子续费卡
     *
     * @param ids 需要删除的电子续费卡主键
     * @return 结果
     */
    @Override
    public int deleteElectronicRenewCardByIds(Long[] ids) {
        return electronicRenewCardMapper.deleteElectronicRenewCardByIds(ids);
    }

    /**
     * 删除电子续费卡信息
     *
     * @param id 电子续费卡主键
     * @return 结果
     */
    @Override
    public int deleteElectronicRenewCardById(Long id) {
        return electronicRenewCardMapper.deleteElectronicRenewCardById(id);
    }

    @Override
    @Transactional
    public int buy(ElectronicRenewCardBuyDto electronicRenewCard) {
        if (electronicRenewCard.getPayInfo().getPayPassword() == null || !agentAccountService.checkPayPassword(electronicRenewCard.getPayInfo().getPayAgentId(),
                electronicRenewCard.getPayInfo().getPayPassword())) {
            throw ServiceException.fail("支付密码错误");
        }
        Agent agent = agentService.selectAgentById(electronicRenewCard.getPayInfo().getPayAgentId());
        BigDecimal totalPay;
        BigDecimal unitPrice = getUnitPrice(electronicRenewCard, agent);
        totalPay = unitPrice.multiply(new BigDecimal(electronicRenewCard.getNum()));
        BigDecimal memberBalance = agentAccountService.getBalance(agent.getId(), TransactionFundType.MEMBER);
        BigDecimal redBookVoucherBalance = agentAccountService.getBalance(agent.getId(), TransactionFundType.COUPON);
        BigDecimal totalBalance = memberBalance.add(redBookVoucherBalance);//可用于购买余额
        if (totalBalance.compareTo(totalPay) < 0) {
            throw ServiceException.fail("余额不足");
        }
        BigDecimal memberPay;
        BigDecimal redBookVoucherPay;
        BigDecimal surplusPay = totalPay;
        //扣除余额
        String indentNumber = String.valueOf(System.currentTimeMillis());
        String remark = electronicRenewCard.getDuration() + "个月 x " + electronicRenewCard.getNum() + "个";
        if (surplusPay.compareTo(BigDecimal.ZERO) > 0 && memberBalance.compareTo(BigDecimal.ZERO) == 1) {
            if (memberBalance.compareTo(surplusPay) > 0) {
                memberBalance = memberBalance.subtract(surplusPay);
                memberPay = surplusPay;
                surplusPay = BigDecimal.ZERO;
            } else {
                memberPay = memberBalance;
                memberBalance = BigDecimal.ZERO;
                surplusPay = surplusPay.subtract(memberPay);
            }
            agentAccountService.updateAccount(TransactionFundType.MEMBER, agent.getId(), memberBalance);
            iAgentTransactionInfoService.addTransactionInfo(agent.getId(), "购买电子续费卡", TransactionFundType.MEMBER, indentNumber, memberPay, memberBalance, 1, 0, 0, remark);
        }
        //扣代金券
        if (surplusPay.compareTo(BigDecimal.ZERO) > 0 && redBookVoucherBalance.compareTo(BigDecimal.ZERO) > 0) { //剩余应付大于0，续约款余额大于0
            if (redBookVoucherBalance.compareTo(surplusPay) == 1) {
                redBookVoucherPay = surplusPay;
                redBookVoucherBalance = redBookVoucherBalance.subtract(surplusPay);
            } else {
                redBookVoucherPay = redBookVoucherBalance;
                redBookVoucherBalance = BigDecimal.ZERO;
            }
            agentAccountService.updateAccount(TransactionFundType.COUPON, agent.getId(), redBookVoucherBalance);
            iAgentTransactionInfoService.addTransactionInfo(agent.getId(), "购买电子续费卡", TransactionFundType.COUPON, indentNumber, redBookVoucherPay, redBookVoucherBalance, 1, 0, 0, remark);
        }
        return generateElectronicRenewCard(electronicRenewCard.getNum(), electronicRenewCard.getDuration(), agent.getId(), unitPrice, indentNumber).size();
    }

    @Override
    public int refund(Long id) {
        ElectronicRenewCard electronicRenewCard = electronicRenewCardMapper.selectElectronicRenewCardById(id);
        if (electronicRenewCard == null) {
            throw ServiceException.fail("电子续费卡不存在");
        }
        if (electronicRenewCard.getStatus() != 0) {
            throw ServiceException.fail("电子续费卡已使用,或已退费");
        }
        if(!agentAccountService.refund(electronicRenewCard.getIndentNumber(),"电子续费卡退款",electronicRenewCard.getCardNumber())){
            throw ServiceException.fail("电子续费卡退款失败！");
        }
        electronicRenewCard.setStatus(2);
        return electronicRenewCardMapper.updateElectronicRenewCard(electronicRenewCard);
    }

    @Override
    public int transfer(ElectronicRenewCardTransferDto electronicRenewCardTransferDto) {
        //获取所有的电子续费卡
        List<Long> ids = electronicRenewCardTransferDto.getIds();
        List<ElectronicRenewCard> electronicRenewCards = electronicRenewCardMapper.selectElectronicRenewCardByIds(ids);
        //所有的电子续费卡必须是当前登录账户有权限管理的
        //店长判断专卖店id
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if(user.getRole().getRoleId().intValue()==106){
            if (electronicRenewCards.stream().anyMatch(electronicRenewCard -> electronicRenewCard.getExclusiveShopId()==null)) {
                throw ServiceException.fail("当前要转移的电子续费卡中有不属于您的！");
            }
            List<ExclusiveShop> exclusiveShopList = exclusiveShopService.selectExclusiveShopList(ExclusiveShop.builder().managerId(SecurityUtils.getUserId()).build());
            List<Integer> idList = exclusiveShopList.stream().map(ExclusiveShop::getId).collect(Collectors.toList());
            if (electronicRenewCards.stream().anyMatch(electronicRenewCard -> !idList.contains(electronicRenewCard.getExclusiveShopId()))) {
                throw ServiceException.fail("当前要转移的电子续费卡中有不属于您的！");
            }
        }else if(user.getLimitAgent()){
            //获取代理商信息
            List<Long> collect = electronicRenewCards.stream().map(ElectronicRenewCard::getAgentId).distinct().collect(Collectors.toList());
            List<Agent> agentList = collect.stream().map(agentId -> agentService.selectAgentById(agentId)).collect(Collectors.toList());
            if (agentList.stream().anyMatch(agent -> !agent.getContactPerson().equals(SecurityUtils.getUserId()))) {
                throw ServiceException.fail("当前要转移的电子续费卡中有不属于您的！");
            }
        }
        //所有的电子续费卡都必须是未使用的
        if (electronicRenewCards.stream().anyMatch(electronicRenewCard -> electronicRenewCard.getStatus() != 0)) {
            throw ServiceException.fail("已使用或已退款的续费卡无法转移！");
        }
        //所有的电子续费卡都必须是未过期的
        if (electronicRenewCards.stream().anyMatch(electronicRenewCard -> electronicRenewCard.getExpireDate().compareTo(DateUtils.getNowDate()) < 0)) {
            throw ServiceException.fail("当前要转移的电子续费卡中有已过期的！");
        }
        electronicRenewCards.forEach(electronicRenewCard -> {
            electronicRenewCard.setAgentId(electronicRenewCardTransferDto.getAgentId());
            electronicRenewCard.setExclusiveShopId(electronicRenewCardTransferDto.getExclusiveShopId());
            electronicRenewCard.setUpdateTime(DateUtils.getNowDate());
            electronicRenewCardMapper.updateElectronicRenewCard(electronicRenewCard);
        });
        return electronicRenewCards.size();
    }

    @Override
    public Boolean status(String cardNumber,Integer month,String userId) {
        ElectronicRenewCard electronicRenewCard = electronicRenewCardMapper.selectElectronicRenewCardByCardNumber(cardNumber);
        if (electronicRenewCard == null) {
            throw ServiceException.fail("您输入的卡号无法使用请重新输入！");
        }
        if (electronicRenewCard.getStatus() != 0) {
            throw ServiceException.fail("您输入的卡号无法使用请重新输入！");
        }
        if(null==userId){
            throw ServiceException.fail("账号不能为空！");
        }
        UserInfo userInfo = userInfoService.selectUserInfoByUserId(userId);
        if(null==userInfo){
            throw ServiceException.fail("用户不存在！");
        }
        if (!Objects.equals(agentService.selectAgentById(electronicRenewCard.getAgentId()).getAid(), userInfo.getAid())) {
            throw ServiceException.fail("请使用账号所在代理商下的电子续费卡！");
        }
        //专卖店下的会员只能使用该专卖店下的续费卡
        if(userInfo.getExclusiveShopId()!=null&&(
                electronicRenewCard.getExclusiveShopId()==null||
                electronicRenewCard.getExclusiveShopId().intValue()!=userInfo.getExclusiveShopId().intValue()
                )){
            throw ServiceException.fail("请使用账号所在专卖店下的电子续费卡！");
        }
        if (electronicRenewCard.getExpireDate().compareTo(DateUtils.getNowDate()) < 0) {
            throw ServiceException.fail("您输入的卡号无法使用请重新输入！");
        }
        if (month!=null&&!Objects.equals(electronicRenewCard.getRenewTimeLen(), month)) {
            throw ServiceException.fail("选择的续费时长与"+cardNumber+"卡包含时长不一致，请重新选择！");
        }
        return true;
    }

    @Override
    public ElectronicRenewCard selectElectronicRenewCardByCardNumber(String cardNumber) {
        return electronicRenewCardMapper.selectElectronicRenewCardByCardNumber(cardNumber);
    }

    @Override
    public Boolean useCard(String cardNumber, String userId, String userName, Integer renewTimeLen, Integer moreTimeLen, Integer renewStage, Date renewBeforeTime, Date renewAfterTime) {
        status(cardNumber, renewTimeLen,userId);
        ElectronicRenewCard electronicRenewCard = electronicRenewCardMapper.selectElectronicRenewCardByCardNumber(cardNumber);
        electronicRenewCard.setStatus(1);
        electronicRenewCard.setUpdateTime(DateUtils.getNowDate());
        electronicRenewCard.setUserId(userId);
        electronicRenewCard.setUserName(userName);
        electronicRenewCard.setRenewStage(renewStage);
        electronicRenewCard.setUsageTime(DateUtils.getNowDate());
        electronicRenewCard.setRenewBeforeDate(renewBeforeTime);
        electronicRenewCard.setRenewAfterDate(renewAfterTime);
       return electronicRenewCardMapper.updateElectronicRenewCard(electronicRenewCard)>0;
    }

    @Override
    public Boolean resetPassword(ElectronicRenewCard electronicRenewCard) {
        ElectronicRenewCard electronicRenewCard1 = electronicRenewCardMapper.selectElectronicRenewCardByCardNumber(electronicRenewCard.getCardNumber());
        if (electronicRenewCard1 == null) {
            throw ServiceException.fail("电子续费卡不存在");
        }
        if (electronicRenewCard1.getStatus() != 0) {
            throw ServiceException.fail("电子续费卡已使用,或已退费");
        }
        if (!Objects.equals(agentService.selectAgentById(electronicRenewCard.getAgentId()).getAgentId(), SecurityUtils.getLoginUser().getUser().getUserId())) {
            throw ServiceException.fail("电子续费卡不属于您");
        }
        if (electronicRenewCard.getExpireDate().compareTo(DateUtils.getNowDate()) < 0) {
            throw ServiceException.fail("电子续费卡已过期");
        }
        electronicRenewCard1.setPassword(getPassword());
        return updateElectronicRenewCard(electronicRenewCard1)>0;
    }

    @Override
    public void refundCard(String ecardNumber) {
        ElectronicRenewCard electronicRenewCard1 = electronicRenewCardMapper.selectElectronicRenewCardByCardNumber(ecardNumber);
        if (electronicRenewCard1 == null) {
            throw ServiceException.fail("电子续费卡不存在");
        }
        if (electronicRenewCard1.getStatus() != 1) {
            throw ServiceException.fail("当前卡不能取消使用");
        }
        electronicRenewCardMapper.refundCard(ecardNumber);
    }

    private static BigDecimal getUnitPrice(ElectronicRenewCardBuyDto electronicRenewCard, Agent agent) {
        BigDecimal unitPrice = BigDecimal.ZERO;
        if (electronicRenewCard.getDuration() == 1) {
            unitPrice = new BigDecimal("86.00");
        } else if (electronicRenewCard.getDuration() == 3) {
            unitPrice = new BigDecimal("196.00");
        } else if (electronicRenewCard.getDuration() == 6) {
            unitPrice = new BigDecimal("296.00");
        } else if (electronicRenewCard.getDuration() == 12) {
            unitPrice = new BigDecimal("456.00");
//            //战略合作伙伴有年课程续费优惠
//            //todo 年课程续费优惠
//            Integer pricingScheme = agent.getPriceScheme();
//            if (pricingScheme != null && pricingScheme == 1) {
//                unitPrice = new BigDecimal("396.00");
//            }
        }
        return unitPrice;
    }

    //根据数量和时长以及代理商生成电子续费卡
    private List<ElectronicRenewCard> generateElectronicRenewCard(Integer num, Integer duration, Long agentId, BigDecimal unitPrice, String indentNumber) {
        ArrayList<ElectronicRenewCard> electronicRenewCards = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            ElectronicRenewCard electronicRenewCard = new ElectronicRenewCard();
            electronicRenewCard.setAgentId(agentId);
            electronicRenewCard.setBuyAgentId(agentId);
            electronicRenewCard.setIndentNumber(indentNumber);
            electronicRenewCard.setRenewTimeLen(duration);
            electronicRenewCard.setStatus(0);
            electronicRenewCard.setMoney(unitPrice);
            electronicRenewCard.setBuyDate(DateUtils.getNowDate());
            electronicRenewCard.setExpireDate(DateUtils.addMonths(DateUtils.getNowDate(), 12));
            electronicRenewCard.setCreateTime(DateUtils.getNowDate());
            electronicRenewCard.setUpdateTime(DateUtils.getNowDate());
            electronicRenewCard.setCardNumber(getCardNo());
            electronicRenewCard.setPassword(getPassword());
            electronicRenewCardMapper.insertElectronicRenewCard(electronicRenewCard);
            electronicRenewCards.add(electronicRenewCard);
        }
        return electronicRenewCards;
    }

    private String getCardNo() {
        //卡号命名规则：HBRC+年份后两位+月份+5位随机数。（不能重复）
        String cardNo = "HBRC" + DateUtils.getYear().substring(2) + DateUtils.getMonth() + (int) ((Math.random() * 9 + 1) * 10000);
        if (electronicRenewCardMapper.selectElectronicRenewCardCountByCardNumber(cardNo) > 0) {
            return getCardNo();
        }
        return cardNo;
    }

    private String getPassword() {
        //随机的4位数字+4位大写字母。为避免混淆，字母不要出现大写i和大写o。
        String password = "";
        for (int i = 0; i < 4; i++) {
            password += (int) (Math.random() * 9 + 1);
        }
        for (int i = 0; i < 4; i++) {
            int random = (int) (Math.random() * 26 + 65);
            if (random == 73 || random == 79) {
                random += 1;
            }
            password += (char) random;
        }
        return password;
    }


}
