package com.redbook.system.service.postSale;

import com.redbook.system.domain.PostSaleSparePart;

import java.util.List;

/**
 * 备件Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IPostSaleSparePartService 
{
    /**
     * 查询备件
     * 
     * @param id 备件主键
     * @return 备件
     */
     PostSaleSparePart selectPostSaleSparePartById(Integer id);

    /**
     * 查询备件列表
     * 
     * @param postSaleSparePart 备件
     * @return 备件集合
     */
     List<PostSaleSparePart> selectPostSaleSparePartList(PostSaleSparePart postSaleSparePart);

    /**
     * 新增备件
     * 
     * @param postSaleSparePart 备件
     * @return 结果
     */
     int insertPostSaleSparePart(PostSaleSparePart postSaleSparePart);

    /**
     * 修改备件
     * 
     * @param postSaleSparePart 备件
     * @return 结果
     */
     int updatePostSaleSparePart(PostSaleSparePart postSaleSparePart);

    /**
     * 批量删除备件
     * 
     * @param ids 需要删除的备件主键集合
     * @return 结果
     */
     int deletePostSaleSparePartByIds(Long[] ids);

    /**
     * 删除备件信息
     * 
     * @param id 备件主键
     * @return 结果
     */
     int deletePostSaleSparePartById(Long id);
}
