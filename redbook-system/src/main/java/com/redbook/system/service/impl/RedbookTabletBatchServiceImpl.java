package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.Model;
import com.redbook.system.domain.RedbookTabletBatch;
import com.redbook.system.mapper.RedbookTabletBatchMapper;
import com.redbook.system.service.IModelService;
import com.redbook.system.service.IRedbookTabletBatchService;
import com.redbook.system.service.ISupplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 批次Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-11-15
 */
@Service
public class RedbookTabletBatchServiceImpl extends ServiceImpl<RedbookTabletBatchMapper, RedbookTabletBatch> implements IRedbookTabletBatchService
{
    @Autowired
    private RedbookTabletBatchMapper redbookTabletBatchMapper;
    @Autowired
    private IModelService modelService;
    @Autowired
    private ISupplyService supplyService;

    /**
     * 查询批次
     * 
     * @param id 批次主键
     * @return 批次
     */
    @Override
    public RedbookTabletBatch selectRedbookTabletBatchById(Long id)
    {
        return redbookTabletBatchMapper.selectRedbookTabletBatchById(id);
    }

    /**
     * 查询批次列表
     * 
     * @param redbookTabletBatch 批次
     * @return 批次
     */
    @Override
    public List<RedbookTabletBatch> selectRedbookTabletBatchList(RedbookTabletBatch redbookTabletBatch)
    {
        return redbookTabletBatchMapper.selectRedbookTabletBatchList(redbookTabletBatch);
    }

    /**
     * 新增批次
     * 
     * @param redbookTabletBatch 批次
     * @return 结果
     */
    @Override
    public int insertRedbookTabletBatch(RedbookTabletBatch redbookTabletBatch)
    {
        Model model = modelService.selectModelById(redbookTabletBatch.getModelId());
        redbookTabletBatch.setManufacturer(model.getSupplyName());
        redbookTabletBatch.setCreateTime(DateUtils.getNowDate());
        return redbookTabletBatchMapper.insertRedbookTabletBatch(redbookTabletBatch);
    }

    /**
     * 修改批次
     * 
     * @param redbookTabletBatch 批次
     * @return 结果
     */
    @Override
    public int updateRedbookTabletBatch(RedbookTabletBatch redbookTabletBatch)
    {
        redbookTabletBatch.setUpdateTime(DateUtils.getNowDate());
        return redbookTabletBatchMapper.updateRedbookTabletBatch(redbookTabletBatch);
    }

    /**
     * 批量删除批次
     * 
     * @param ids 需要删除的批次主键
     * @param username
     * @return 结果
     */
    @Override
    public int deleteRedbookTabletBatchByIds(Long[] ids, String username)
    {
        if (ids == null || ids.length == 0) {
            return 0;
        }
        return redbookTabletBatchMapper.deleteRedbookTabletBatchByIds(ids,username);
    }

    /**
     * 删除批次信息
     * 
     * @param id 批次主键
     * @return 结果
     */
    @Override
    public int deleteRedbookTabletBatchById(Long id)
    {
        return redbookTabletBatchMapper.deleteRedbookTabletBatchById(id);
    }
}
