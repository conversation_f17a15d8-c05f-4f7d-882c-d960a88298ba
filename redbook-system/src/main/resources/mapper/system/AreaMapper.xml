<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.AreaMapper">
    
    <resultMap type="Area" id="AreaResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="firstLetter"    column="first_letter"    />
        <result property="level"    column="level"    />
        <result property="fullName"    column="full_name"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectAreaVo">
        select id, code, name, parent_id, first_letter, level, full_name, status from area
    </sql>

    <select id="selectAreaList" parameterType="Area" resultMap="AreaResult">
        <include refid="selectAreaVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="firstLetter != null  and firstLetter != ''"> and first_letter = #{firstLetter}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="fullName != null  and fullName != ''"> and full_name like concat('%', #{fullName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectAreaById" parameterType="Long" resultMap="AreaResult">
        <include refid="selectAreaVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertArea" parameterType="Area" useGeneratedKeys="true" keyProperty="id">
        insert into area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="firstLetter != null and firstLetter != ''">first_letter,</if>
            <if test="level != null">level,</if>
            <if test="fullName != null">full_name,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="firstLetter != null and firstLetter != ''">#{firstLetter},</if>
            <if test="level != null">#{level},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateArea" parameterType="Area">
        update area
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="firstLetter != null and firstLetter != ''">first_letter = #{firstLetter},</if>
            <if test="level != null">level = #{level},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAreaById" parameterType="Long">
        delete from area where id = #{id}
    </delete>

    <delete id="deleteAreaByIds" parameterType="String">
        delete from area where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>