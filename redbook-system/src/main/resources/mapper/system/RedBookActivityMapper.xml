<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.IRedBookActivityDao">
    <select id="getActivityConfig" resultType="string">
        select
            acc_value
        from ${DB_RED_BOOK_ACTIVITY}.activity_content_config
        where activity_content_id=#{activityContentId} and acc_key=#{configKey}
    </select>

    <select id="getJoinUserInfo" resultType="map">
        select
            *
        from ${DB_RED_BOOK_ACTIVITY}.activity_user
        where activity_base_id=#{activityBaseId} and user_id=#{userId}
    </select>
    <insert id="insertUser">
        insert into
            ${DB_RED_BOOK_ACTIVITY}.activity_user
            (user_id,activity_base_id,user_name,user_addr)
        values
               (#{userId,jdbcType=VARCHAR},#{activityBaseId,jdbcType=INTEGER},#{userName,jdbcType=VARCHAR},#{userAddr,jdbcType=VARCHAR})
    </insert>

    <select id="getSurplusCommemorateCoin" resultType="int">
        select 1000-sum(artian_commemorate_coin) from ${DB_RED_BOOK_ACTIVITY}.activity_user where activity_base_id=5
    </select>

    <select id="getInviter" resultType="string">
        select user_id from ${DB_RED_BOOK_ACTIVITY}.activity_user_invited_record where activity_base_id=5 and invited_user_id=#{userId} and `status`=0
    </select>

    <update id="updateInvitedUserStatus">
        update ${DB_RED_BOOK_ACTIVITY}.activity_user_invited_record set `status`=1 where activity_base_id=5 and invited_user_id=#{userId} and `status`=0
    </update>

    <update id="addCoinNum">
        update ${DB_RED_BOOK_ACTIVITY}.activity_user
        set `cloud_artian_coin`=cloud_artian_coin+#{cloudCoin},`artian_commemorate_coin`=artian_commemorate_coin+#{commemorateCoin}
        where activity_base_id=5 and user_id=#{userId}
    </update>
    <insert id="insertCoinChangeRecord">
        insert into ${DB_RED_BOOK_ACTIVITY}.activity_user_artian_coin_record
            (activity_base_id,activity_content_id,user_id,change_num,change_type,`type`)
            values
            (5,7,#{userId},#{change_num},#{change_type},#{type})
    </insert>
</mapper>
