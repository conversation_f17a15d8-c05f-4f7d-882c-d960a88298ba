<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.RechargeDiscountMapper">
    
    <resultMap type="RechargeDiscount" id="RechargeDiscountResult">
        <result property="fundtypeid"    column="fundtypeid"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRechargeDiscountVo">
        select fundtypeid, start_time, end_time, create_by, create_time, update_by, update_time from recharge_discount
    </sql>

    <select id="selectRechargeDiscountList" parameterType="RechargeDiscount" resultMap="RechargeDiscountResult">
        <include refid="selectRechargeDiscountVo"/>
        <where>  
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>
    
    <select id="selectRechargeDiscountByFundtypeid" parameterType="Long" resultMap="RechargeDiscountResult">
        <include refid="selectRechargeDiscountVo"/>
        where fundtypeid = #{fundtypeid}
    </select>
        
    <insert id="insertRechargeDiscount" parameterType="RechargeDiscount">
        insert into recharge_discount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fundtypeid != null">fundtypeid,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fundtypeid != null">#{fundtypeid},</if>
            <if test=" startTime!= null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRechargeDiscount" parameterType="RechargeDiscount">
        update recharge_discount
        <trim prefix="SET" suffixOverrides=",">
            <if test="startTime != null"> start_time= #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where fundtypeid = #{fundtypeid}
    </update>

    <delete id="deleteRechargeDiscountByFundtypeid" parameterType="Long">
        delete from recharge_discount where fundtypeid = #{fundtypeid}
    </delete>

    <delete id="deleteRechargeDiscountByFundtypeids" parameterType="String">
        delete from recharge_discount where fundtypeid in
        <foreach item="fundtypeid" collection="array" open="(" separator="," close=")">
            #{fundtypeid}
        </foreach>
    </delete>

    <delete id="delete" parameterType="Long">
        delete from recharge_discount where fundtypeid = #{fundtypeid}
    </delete>
</mapper>