<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.RedbookTabletBatchMapper">
    <resultMap type="RedbookTabletBatch" id="RedbookTabletBatchResult">
        <result property="id" column="ID"/>
        <result property="batchName" column="Batch_Name"/>
        <result property="orderDate" column="Order_date"/>
        <result property="manufacturer" column="Manufacturer"/>
        <result property="modelId" column="model_id"/>
        <result property="number" column="Number"/>
        <result property="dod" column="DOD"/>
        <result property="tod" column="TOD"/>
        <result property="extra" column="extra"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="modelName" column="model_name"/>
        <result property="manufacturerId" column="manufacturerId"/>
    </resultMap>

    <sql id="selectRedbookTabletBatchVo">
        select rtb.ID,
        rtb.Batch_Name,
        rtb.Order_date,
        s.supply_name Manufacturer,
        rtb.status,
        rtb.model_id,
        rtb.Number,
        rtb.DOD,
        rtb.TOD,
        rtb.extra,
        rtb.create_by,
        rtb.create_time,
        rtb.update_by,
        rtb.update_time,
        m.code as model_name,
        s.id as manufacturerId
        from redbook_tablet_batch rtb
        left join redbook_tablet_model m on rtb.model_id = m.id
        left join redbook_tablet_supply s on m.supply_id = s.id
    </sql>

    <select id="selectRedbookTabletBatchList" parameterType="RedbookTabletBatch" resultMap="RedbookTabletBatchResult">
        <include refid="selectRedbookTabletBatchVo"/>
        <where>
            rtb.status = 0

            <if test="batchName != null  and batchName != ''">
                and rtb.Batch_Name like concat('%'
                                          , #{batchName}
                                          , '%')
            </if>
            <if test="orderDate != null">
                and rtb.Order_date = #{orderDate}
            </if>
            <if test="manufacturer != null  and manufacturer != ''">
                and s.supply_name = #{manufacturer}
            </if>
            <if test="modelId != null">
                and rtb.model_id = #{modelId}
            </if>
            <if test="number != null">
                and rtb.Number = #{number}
            </if>
            <if test="dod != null">
                and rtb.DOD = #{dod}
            </if>
            <if test="tod != null">
                and rtb.TOD = #{tod}
            </if>
            <if test="extra != null  and extra != ''">
                and rtb.extra = #{extra}
            </if>

        </where>
    </select>

    <select id="selectRedbookTabletBatchById" parameterType="Long" resultMap="RedbookTabletBatchResult">
        <include refid="selectRedbookTabletBatchVo"/>
        where rtb.ID = #{id}
    </select>

    <insert id="insertRedbookTabletBatch" parameterType="RedbookTabletBatch" useGeneratedKeys="true" keyProperty="id">
        insert into redbook_tablet_batch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchName != null">
                Batch_Name,
            </if>
            <if test="orderDate != null">
                Order_date,
            </if>
            <if test="modelId != null">
                model_id,
            </if>
            <if test="number != null">
                Number,
            </if>
            <if test="dod != null">
                DOD,
            </if>
            <if test="tod != null">
                TOD,
            </if>
            <if test="extra != null">
                extra,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchName != null">
                #{batchName},
            </if>
            <if test="orderDate != null">
                #{orderDate},
            </if>
            <if test="modelId != null">
                #{modelId},
            </if>
            <if test="number != null">
                #{number},
            </if>
            <if test="dod != null">
                #{dod},
            </if>
            <if test="tod != null">
                #{tod},
            </if>
            <if test="extra != null">
                #{extra},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <update id="updateRedbookTabletBatch" parameterType="RedbookTabletBatch">
        update redbook_tablet_batch
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchName != null">
                Batch_Name = #{batchName},
            </if>
            <if test="orderDate != null">
                Order_date = #{orderDate},
            </if>
            <if test="modelId != null">
                model_id = #{modelId},
            </if>
            <if test="number != null">
                Number = #{number},
            </if>
            <if test="dod != null">
                DOD = #{dod},
            </if>
            <if test="tod != null">
                TOD = #{tod},
            </if>
            <if test="extra != null">
                extra = #{extra},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteRedbookTabletBatchById" parameterType="Long">
        delete
        from redbook_tablet_batch
        where ID = #{id}
    </delete>

    <delete id="deleteRedbookTabletBatchByIds" parameterType="String">
        update redbook_tablet_batch set status='1' , create_by = #{username} where DOD is null
        and ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>