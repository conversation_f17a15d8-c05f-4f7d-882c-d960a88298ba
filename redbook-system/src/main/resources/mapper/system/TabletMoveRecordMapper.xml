<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.TabletMoveRecordMapper">
    
    <resultMap type="TabletMoveRecord" id="TabletMoveRecordResult">
        <result property="id"    column="id"    />
        <result property="tabletId"    column="tablet_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="sourceAgentId"    column="source_agent_id"    />
        <result property="targetAgentId"    column="target_agent_id"    />
        <result property="sourceName"    column="sourceName"    />
        <result property="targetName"    column="targetName"    />
        <result property="desc"    column="desc"    />
    </resultMap>

    <sql id="selectTabletMoveRecordVo">
        SELECT concat(IFNULL(b.`name`, '-'),IF(es1.name is null,"",concat("（",es1.name,"）"))) as sourceName,
               concat(IFNULL(c.`name`, '-'),IF(es2.name is null,"",concat("（",es2.name,"）"))) as targetName,
               a.*,
               d.nick_name           as operatorName
        from hssword_red_book_management.redbook_tablet_move_record a
                 left join hssword_red_book_management.agent b on a.source_agent_id = b.id
                 left join hssword_red_book_management.agent c on a.target_agent_id = c.id
                 left join exclusive_shop es1 on a.source_exclusive_shop_id=es1.id
                 left join exclusive_shop es2 on a.target_exclusive_shop_id=es2.id
                 left join sys_user d on a.create_by = d.user_name
    </sql>

    <select id="selectTabletMoveRecordList" parameterType="TabletMoveRecord" resultMap="TabletMoveRecordResult">
        <include refid="selectTabletMoveRecordVo"/>
        <where>  
            <if test="tabletId != null "> and tablet_id = #{tabletId}</if>
            <if test="sourceAgentId != null "> and source_agent_id = #{sourceAgentId}</if>
            <if test="targetAgentId != null "> and target_agent_id = #{targetAgentId}</if>
        </where>
        order by a.create_time desc
    </select>
    
    <select id="selectTabletMoveRecordById" parameterType="Long" resultMap="TabletMoveRecordResult">
        <include refid="selectTabletMoveRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTabletMoveRecord" parameterType="TabletMoveRecord" useGeneratedKeys="true" keyProperty="id">
        insert into hssword_red_book_management.redbook_tablet_move_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tabletId != null">tablet_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="sourceAgentId != null">source_agent_id,</if>
            <if test="sourceExclusiveShopId != null">source_exclusive_shop_id,</if>
            <if test="targetAgentId != null">target_agent_id,</if>
            <if test="targetExclusiveShopId != null">target_exclusive_shop_id,</if>
            <if test="desc != null and desc != ''">`desc`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tabletId != null">#{tabletId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="sourceAgentId != null">#{sourceAgentId},</if>
            <if test="sourceExclusiveShopId != null">#{sourceExclusiveShopId},</if>
            <if test="targetAgentId != null">#{targetAgentId},</if>
            <if test="targetExclusiveShopId != null">#{targetExclusiveShopId},</if>
            <if test="desc != null and desc != ''">#{desc},</if>
         </trim>
    </insert>

    <update id="updateTabletMoveRecord" parameterType="TabletMoveRecord">
        update hssword_red_book_management.redbook_tablet_move_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="tabletId != null">tablet_id = #{tabletId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="sourceAgentId != null">source_agent_id = #{sourceAgentId},</if>
            <if test="sourceExclusiveShopId != null">source_exclusive_shop_id = #{sourceExclusiveShopId},</if>
            <if test="targetAgentId != null">target_agent_id = #{targetAgentId},</if>
            <if test="targetExclusiveShopId != null">target_exclusive_shop_id = #{targetExclusiveShopId},</if>
            <if test="desc != null and desc != ''">desc = #{desc},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTabletMoveRecordById" parameterType="Long">
        delete from hssword_red_book_management.redbook_tablet_move_record where id = #{id}
    </delete>

    <delete id="deleteTabletMoveRecordByIds" parameterType="String">
        delete from hssword_red_book_management.redbook_tablet_move_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>