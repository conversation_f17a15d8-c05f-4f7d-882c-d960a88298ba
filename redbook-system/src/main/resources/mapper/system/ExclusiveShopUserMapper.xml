<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ExclusiveShopUserMapper">
    
    <resultMap type="ExclusiveShopUser" id="ExclusiveShopUserResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="phonenumber"    column="phonenumber"    />
        <result property="sex"    column="sex"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="haveCertificate"    column="have_certificate"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectExclusiveShopUserVo">
        select id, shop_id, user_name, nick_name, phonenumber, sex, status, del_flag, have_certificate, create_time, update_time from exclusive_shop_user
    </sql>

    <select id="selectExclusiveShopUserList" parameterType="ExclusiveShopUser" resultMap="ExclusiveShopUserResult">
        <include refid="selectExclusiveShopUserVo"/>
        <where>  
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="haveCertificate != null "> and have_certificate = #{haveCertificate}</if>
        </where>
    </select>
    
    <select id="selectExclusiveShopUserById" parameterType="Long" resultMap="ExclusiveShopUserResult">
        <include refid="selectExclusiveShopUserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertExclusiveShopUser" parameterType="ExclusiveShopUser" useGeneratedKeys="true" keyProperty="id">
        insert into exclusive_shop_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="sex != null">sex,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="haveCertificate != null">have_certificate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="sex != null">#{sex},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="haveCertificate != null">#{haveCertificate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateExclusiveShopUser" parameterType="ExclusiveShopUser">
        update exclusive_shop_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="haveCertificate != null">have_certificate = #{haveCertificate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExclusiveShopUserById" parameterType="Long">
        delete from exclusive_shop_user where id = #{id}
    </delete>

    <delete id="deleteExclusiveShopUserByIds" parameterType="String">
        delete from exclusive_shop_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>