<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.AppVersionMapper">
    
    <resultMap type="AppVersion" id="AppVersionResult">
        <result property="id"    column="id"    />
        <result property="versionCode"    column="version_code"    />
        <result property="versionName"    column="version_name"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="forceUpdate"    column="force_update"    />
        <result property="downloadUrl"    column="download_url"    />
        <result property="md5Checksum"    column="md5_checksum"    />
        <result property="fileSize"    column="file_size"    />
        <result property="changelog"    column="changelog"    />
        <result property="targetUserGroups"    column="target_user_groups"    />
        <result property="status"    column="status"    />
        <result property="hasPatches"    column="has_patches"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectAppVersionVo">
        select id, version_code, version_name, publish_time, force_update, download_url, md5_checksum, file_size, changelog, target_user_groups, status, has_patches, created_at, updated_at from lollipop_app.app_version
    </sql>

    <select id="selectAppVersionList" parameterType="AppVersion" resultMap="AppVersionResult">
        <include refid="selectAppVersionVo"/>
        <where>  
            <if test="versionCode != null "> and version_code = #{versionCode}</if>
            <if test="versionName != null  and versionName != ''"> and version_name like concat('%', #{versionName}, '%')</if>
            <if test="publishTime != null "> and publish_time = #{publishTime}</if>
            <if test="forceUpdate != null "> and force_update = #{forceUpdate}</if>
            <if test="downloadUrl != null  and downloadUrl != ''"> and download_url = #{downloadUrl}</if>
            <if test="md5Checksum != null  and md5Checksum != ''"> and md5_checksum = #{md5Checksum}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="changelog != null  and changelog != ''"> and changelog = #{changelog}</if>
            <if test="targetUserGroups != null  and targetUserGroups != ''"> and target_user_groups = #{targetUserGroups}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="hasPatches != null "> and has_patches = #{hasPatches}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectAppVersionById" parameterType="Long" resultMap="AppVersionResult">
        <include refid="selectAppVersionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAppVersion" parameterType="AppVersion" useGeneratedKeys="true" keyProperty="id">
        insert into lollipop_app.app_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="versionCode != null">version_code,</if>
            <if test="versionName != null and versionName != ''">version_name,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="forceUpdate != null">force_update,</if>
            <if test="downloadUrl != null and downloadUrl != ''">download_url,</if>
            <if test="md5Checksum != null">md5_checksum,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="changelog != null">changelog,</if>
            <if test="targetUserGroups != null">target_user_groups,</if>
            <if test="status != null">status,</if>
            <if test="hasPatches != null">has_patches,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="versionCode != null">#{versionCode},</if>
            <if test="versionName != null and versionName != ''">#{versionName},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="forceUpdate != null">#{forceUpdate},</if>
            <if test="downloadUrl != null and downloadUrl != ''">#{downloadUrl},</if>
            <if test="md5Checksum != null">#{md5Checksum},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="changelog != null">#{changelog},</if>
            <if test="targetUserGroups != null">#{targetUserGroups},</if>
            <if test="status != null">#{status},</if>
            <if test="hasPatches != null">#{hasPatches},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateAppVersion" parameterType="AppVersion">
        update lollipop_app.app_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="versionCode != null">version_code = #{versionCode},</if>
            <if test="versionName != null and versionName != ''">version_name = #{versionName},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="forceUpdate != null">force_update = #{forceUpdate},</if>
            <if test="downloadUrl != null and downloadUrl != ''">download_url = #{downloadUrl},</if>
            <if test="md5Checksum != null">md5_checksum = #{md5Checksum},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="changelog != null">changelog = #{changelog},</if>
            <if test="targetUserGroups != null">target_user_groups = #{targetUserGroups},</if>
            <if test="status != null">status = #{status},</if>
            <if test="hasPatches != null">has_patches = #{hasPatches},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppVersionById" parameterType="Long">
        delete from lollipop_app.app_version where id = #{id}
    </delete>

    <delete id="deleteAppVersionByIds" parameterType="String">
        delete from lollipop_app.app_version where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>