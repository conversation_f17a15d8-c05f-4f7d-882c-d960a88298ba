<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.TeacherFeedbackMapper">

    <resultMap type="TeacherFeedback" id="TeacherFeedbackResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="userId" column="user_id"/>
        <result property="userSystem" column="user_system"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="parentId" column="parent_id"/>
        <result property="replyStatus" column="reply_status"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastUpdateTime" column="last_update_time"/>
        <result property="verifyStatus" column="verify_status"/>
        <result property="ancestorVerifyStatus" column="ancestor_verify_status"/>
        <result property="hasVerify" column="has_verify"/>
        <result property="replyUserName"    column="replyUserName"    />
        <result property="feedBackUserName"    column="feedBackUserName"    />
        <result property="agentAddress"    column="agentAddress"    />
        <result property="agentAlias"    column="agentAlias"    />
        <result property="label"    column="label"    />
    </resultMap>

    <sql id="selectTeacherFeedbackVo">
        select  tf.*,su.nick_name as replyUserName,au.user_name as feedBackUserName,a.name as agentAddress,a.alias as agentAlias
        from ${DB_RED_BOOK_TEACH}.teacher_feedback tf
                 left join agent_user au on tf.user_id = au.user_id
                 left join sys_user su on tf.user_id = su.user_name
                 left join agent a on au.aid = a.`aid`
    </sql>

    <select id="selectTeacherFeedbackList" parameterType="TeacherFeedback" resultMap="TeacherFeedbackResult">
        <include refid="selectTeacherFeedbackVo"/>
        <where>
            tf.parent_id is null
            <if test="title != null  and title != ''">and tf.title = #{title}</if>
            <if test="type != null ">and tf.type = #{type}</if>
            <if test="userId != null  and userId != ''">and tf.user_id = #{userId}</if>
            <if test="userSystem != null  and userSystem != ''">and tf.user_system = #{userSystem}</if>
            <if test="content != null  and content != ''">and tf.content = #{content}</if>
            <if test="parentId != null ">and tf.parent_id = #{parentId}</if>
            <if test="replyStatus != null ">and tf.reply_status = #{replyStatus}</if>
            <if test="lastUpdateTime != null ">and tf.last_update_time = #{lastUpdateTime}</if>
            <if test="verifyStatus != null ">and tf.verify_status = #{verifyStatus}</if>
            <if test="hasVerify != null ">and tf.has_verify = #{hasVerify}</if>
            <if test="params!=null and params.createTimeS !=null">and tf.update_time &gt;= #{params.createTimeS}' 00:00:00' </if>
            <if test="params!=null and params.createTimeE !=null">and tf.update_time &lt;= #{params.createTimeE}' 23:59:59' </if>
            <if test="ancestorVerifyStatus != null">and tf.ancestor_verify_status = #{ancestorVerifyStatus}</if>
            <if test="label != null ">and tf.label = #{label}</if>
            <if test="keyWord != null ">
                and (
                        tf.title like concat('%', #{keyWord}, '%') or tf.content like concat('%', #{keyWord}, '%')
                        <if test="parentIdList != null and parentIdList.size() > 0">
                            or tf.id in
                                <foreach collection="parentIdList" item="item" open="("  separator="," close=")">
                                    #{item}
                                </foreach>
                        </if>
                    )
            </if>
        </where>
        order by tf.${orderField} ${orderDirection}
    </select>

    <select id="selectParent" resultMap="TeacherFeedbackResult">
        SELECT tf.*
        FROM hssword_red_book_teach.teacher_feedback tf
        where tf.id = #{id}
    </select>

    <select id="selectParentId" resultType="java.lang.Long">
        SELECT DISTINCT tf2.id
        FROM ${DB_RED_BOOK_TEACH}.teacher_feedback tf
        left JOIN ${DB_RED_BOOK_TEACH}.teacher_feedback_closure tfc ON tf.id = tfc.descendant_id
        left JOIN ${DB_RED_BOOK_TEACH}.teacher_feedback tf2 ON tf2.id = tf.parent_id
        where tf.content like concat('%', #{keyWord}, '%') and tf2.id is not null
    </select>

    <select id="selectTeacherFeedbackById" parameterType="Long" resultMap="TeacherFeedbackResult">
        <include refid="selectTeacherFeedbackVo"/>
        where tf.id = #{id}
    </select>
    <select id="getFeedBackByAncestor" resultType="com.redbook.system.domain.TeacherFeedback">
        SELECT tf.*, tfc.depth,au.user_name feedBackUserName,su.nick_name replyUserName,a.name as agentAddress,a.alias as agentAlias
        FROM ${DB_RED_BOOK_TEACH}.teacher_feedback tf
                 JOIN ${DB_RED_BOOK_TEACH}.teacher_feedback_closure tfc ON tf.id = tfc.descendant_id
                 left join agent_user au on tf.user_id = au.user_id
                 left join sys_user su on tf.user_id = su.user_name
                 left join agent a on au.aid = a.`aid`
        WHERE tfc.ancestor_id = #{ancestorId}
        ORDER BY tfc.depth asc
    </select>
    <select id="getTopFeedBackByDescendantId" resultType="java.lang.Long">
        SELECT ancestor_id
        FROM ${DB_RED_BOOK_TEACH}.teacher_feedback_closure
        WHERE descendant_id = #{descendantId}
          AND depth = (SELECT MAX(depth) FROM ${DB_RED_BOOK_TEACH}.teacher_feedback_closure WHERE descendant_id = #{descendantId})
    </select>
    <select id="getClosuresByDescendant" resultType="com.redbook.system.domain.TeacherFeedbackClosure">
        SELECT ancestor_id as ancestorId,descendant_id as descendantId,depth as depth
        FROM ${DB_RED_BOOK_TEACH}.teacher_feedback_closure WHERE descendant_id = #{descendantId}
    </select>

    <insert id="insertTeacherFeedback" parameterType="TeacherFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into ${DB_RED_BOOK_TEACH}.teacher_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="type != null">type,</if>
            <if test="userId != null">user_id,</if>
            <if test="userSystem != null">user_system,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="replyStatus != null">reply_status,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="lastUpdateTime != null">last_update_time,</if>
            <if test="verifyStatus != null">verify_status,</if>
            <if test="hasVerify != null">has_verify,</if>
            <if test="label != null">label,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userSystem != null">#{userSystem},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="replyStatus != null">#{replyStatus},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="lastUpdateTime != null">#{lastUpdateTime},</if>
            <if test="verifyStatus != null">#{verifyStatus},</if>
            <if test="hasVerify != null">#{hasVerify},</if>
            <if test="label != null">#{label},</if>
        </trim>
    </insert>
    <insert id="insertClosure">
        INSERT INTO ${DB_RED_BOOK_TEACH}.teacher_feedback_closure (ancestor_id, descendant_id, depth) VALUES (#{ancestorId}, #{descendantId}, #{depth})
    </insert>

    <update id="updateTeacherFeedback" parameterType="TeacherFeedback">
        update ${DB_RED_BOOK_TEACH}.teacher_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userSystem != null">user_system = #{userSystem},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="replyStatus != null">reply_status = #{replyStatus},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="lastUpdateTime != null">last_update_time = #{lastUpdateTime},</if>
            <if test="verifyStatus != null">verify_status = #{verifyStatus},</if>
            <if test="hasVerify != null">has_verify = #{hasVerify},</if>
            <if test="ancestorVerifyStatus != null">ancestor_verify_status = #{ancestorVerifyStatus},</if>
            <if test="verifyUserId != null and verifyUserId !=''">verify_user_id = #{verifyUserId},</if>
            <if test="label != null">label = #{label},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>