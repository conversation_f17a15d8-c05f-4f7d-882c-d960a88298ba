package com.redbook.dto.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel(reference = "AppletUserPhoneRequestDto")
public class AppletUserPhoneRequestDto {

    @ApiModelProperty(value = "微信code", required = true)
    private String code;

    @ApiModelProperty(value = "微信用户所在端唯一标识", required = true)
    private String openId;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
