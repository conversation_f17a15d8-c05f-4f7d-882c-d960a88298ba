package com.redbook.service;

import com.redbook.common.core.domain.AjaxResult;
import com.redbook.system.domain.PostSaleGoodsOrder;

import java.util.List;

/**
 * 商品订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IPostSaleGoodsOrderService 
{
    /**
     * 查询商品订单
     * 
     * @param id 商品订单主键
     * @return 商品订单
     */
     PostSaleGoodsOrder selectPostSaleGoodsOrderById(Long id);
    PostSaleGoodsOrder selectPostSaleGoodsOrderByOrderNo(String orderNo);

    /**
     * 查询商品订单列表
     * 
     * @param postSaleGoodsOrder 商品订单
     * @return 商品订单集合
     */
     List<PostSaleGoodsOrder> selectPostSaleGoodsOrderList(PostSaleGoodsOrder postSaleGoodsOrder);

    /**
     * 再次购买
     * @param id
     * @return
     */
    int buyAgain( Integer appletUserId,Long id);


    /**
     * 新增商品订单
     * 
     * @param postSaleGoodsOrder 商品订单
     * @return 结果
     */
     int insertPostSaleGoodsOrder(PostSaleGoodsOrder postSaleGoodsOrder);
    AjaxResult cancelPostSaleGoodsOrder(Long id);

    /**
     * 修改商品订单
     * 
     * @param postSaleGoodsOrder 商品订单
     * @return 结果
     */
     int updatePostSaleGoodsOrder(PostSaleGoodsOrder postSaleGoodsOrder);

    /**
     * 修改订单状态为待发货
     * @param postSaleGoodsOrder
     * @return
     */
     int updateOrder(PostSaleGoodsOrder postSaleGoodsOrder);

    /**
     * 批量删除商品订单
     * 
     * @param ids 需要删除的商品订单主键集合
     * @return 结果
     */
     int deletePostSaleGoodsOrderByIds(Long[] ids);

    /**
     * 删除商品订单信息
     * 
     * @param id 商品订单主键
     * @return 结果
     */
     int deletePostSaleGoodsOrderById(Long id);
     int deleteByOrderNo(String orderNo);
}
