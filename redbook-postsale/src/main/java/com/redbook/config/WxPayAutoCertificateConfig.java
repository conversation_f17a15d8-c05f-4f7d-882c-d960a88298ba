package com.redbook.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 微信支付证书自动更新配置
 * @date 2024-12-12 15:02
 */
@Configuration
@Component
public class WxPayAutoCertificateConfig {

    @Autowired
    private WxPayConfig wxPayConfig;

    /**
     * 初始化商户配置
     * @return
     */
//    @Bean
//    public RSAAutoCertificateConfig rsaAutoCertificateConfig() {
//        System.out.println("PrivateKey merchantId: " + this.wxPayConfig.getMerchantId());
//        System.out.println("PrivateKey Path: " +  this.wxPayConfig.getPrivateKeyPath());
//        RSAAutoCertificateConfig config = new RSAAutoCertificateConfig.Builder()
//                .merchantId( this.wxPayConfig.getMerchantId())
//                .privateKeyFromPath( this.wxPayConfig.getPrivateKeyPath())
//                .merchantSerialNumber( this.wxPayConfig.getMerchantSerialNumber())
//                .apiV3Key( this.wxPayConfig.getApiV3Key())
//                .build();
//        return config;
//    }


}
