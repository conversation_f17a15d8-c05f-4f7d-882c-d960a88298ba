package com.redbook.config;

import com.github.pagehelper.util.StringUtil;
import com.redbook.common.exception.ServiceException;
import com.redbook.service.IAppletUserLoginService;
import com.redbook.util.wehChat.JwtTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.regex.Pattern;

@Component
public class TokenInterceptor implements HandlerInterceptor {
    // 白名单路径
    private static final String[] WHITE_URL_LIST = {};
    // 白名单路径正则表达式
    private static final Pattern[] WHITE_LIST_PATHS = {
            Pattern.compile("^/postSale/applet/userLogin/.*"),
            Pattern.compile("^/postSale/expressOrderStatus/callback/.*")
            // 其他白名单路径
    };
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    IAppletUserLoginService wxUserService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();

        // 检查请求是否在白名单中
        if (WHITE_URL_LIST.length > 0) {
            for (String path : WHITE_URL_LIST) {
                if (requestURI.startsWith(path)) {
                    return true; // 允许访问
                }
            }
        }
        //正则判断
        for (Pattern pattern : WHITE_LIST_PATHS) {
            if (pattern.matcher(requestURI).matches()) {
                return true; // 允许访问
            }
        }
        //token的验证逻辑
        String token = jwtTokenUtil.getTokenFromRequest(request);

        if (token == null || token.isEmpty()) {
            throw new ServiceException("Token is missing", HttpServletResponse.SC_UNAUTHORIZED);
        }
        try{
            if (jwtTokenUtil.isTokenExpired(token)) {
                throw new ServiceException("Token is expired", HttpServletResponse.SC_NOT_ACCEPTABLE);
            }
            UserDetails userDetails = wxUserService.getUserTokenInfo(token);
            if (!jwtTokenUtil.validateToken(token, userDetails)) {
                throw new ServiceException("Invalid token", HttpServletResponse.SC_FORBIDDEN);
            }
        }catch (Exception e){
            if (e.getMessage().contains("Token invalided")) {
                throw new ServiceException("Token invalided", HttpServletResponse.SC_FORBIDDEN);
            }
            throw new ServiceException("Token is expired", HttpServletResponse.SC_NOT_ACCEPTABLE);
        }

        try {
            String userId = jwtTokenUtil.getUserIdFromToken(token);
            if (StringUtil.isEmpty(userId)) {
                throw new ServiceException("Invalid token", HttpServletResponse.SC_FORBIDDEN);
            }
        } catch (Exception e) {
            throw new ServiceException("Invalid token", HttpServletResponse.SC_FORBIDDEN);
        }
        return true; // 允许访问
    }
}
