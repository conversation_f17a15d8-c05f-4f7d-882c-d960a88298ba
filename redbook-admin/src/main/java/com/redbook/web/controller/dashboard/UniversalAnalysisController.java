package com.redbook.dashboard.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.redbook.dashboard.domain.entity.AnalysisDataEntity;
import com.redbook.dashboard.domain.enums.AnalysisTypeEnum;
import com.redbook.dashboard.domain.enums.BusinessTypeEnum;
import com.redbook.dashboard.domain.enums.DimensionLevelEnum;
import com.redbook.dashboard.domain.vo.ComparisonResult;
import com.redbook.dashboard.domain.vo.RankingItem;
import com.redbook.dashboard.domain.vo.TrendPoint;
import com.redbook.dashboard.service.AnalysisDataService;
import com.redbook.dashboard.service.UniversalAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用数据分析控制器
 * 提供统一的数据分析API接口，支持多种业务类型和分析维度
 * 
 * <AUTHOR>
 * @since 2024-06-25
 */
@Slf4j
@RestController
@RequestMapping("/api/analysis")
@Api(tags = "通用数据分析接口")
public class UniversalAnalysisController {
    
    @Autowired
    private UniversalAnalysisService universalAnalysisService;
    
    @Autowired
    private AnalysisDataService analysisDataService;
    
    /**
     * 趋势分析
     */
    @GetMapping("/trend")
    @ApiOperation(value = "趋势分析", notes = "分析指定指标在时间维度上的变化趋势")
    public List<TrendPoint> analyzeTrend(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @RequestParam String businessType,
            
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @RequestParam String dimensionLevel,
            
            @ApiParam(value = "指标名称", required = true, example = "quizAvgScore") 
            @RequestParam String metricName,
            
            @ApiParam(value = "开始日期", required = true, example = "2024-01-01") 
            @RequestParam String startDate,
            
            @ApiParam(value = "结束日期", required = true, example = "2024-12-31") 
            @RequestParam String endDate,
            
            @ApiParam(value = "省份ID", example = "110000") 
            @RequestParam(required = false) String provinceId,
            
            @ApiParam(value = "城市ID", example = "110100") 
            @RequestParam(required = false) String cityId,
            
            @ApiParam(value = "体验中心ID", example = "1001") 
            @RequestParam(required = false) String aliasId) {
        
        log.info("趋势分析请求: businessType={}, dimensionLevel={}, metricName={}", 
                businessType, dimensionLevel, metricName);
        
        // 构建过滤条件
        Map<String, Object> filters = buildFilters(provinceId, cityId, aliasId);
        
        // 执行分析
        return universalAnalysisService.analyzeTrend(
                BusinessTypeEnum.valueOf(businessType),
                DimensionLevelEnum.valueOf(dimensionLevel),
                metricName,
                startDate,
                endDate,
                filters
        );
    }
    
    /**
     * 排行分析
     */
    @GetMapping("/ranking")
    @ApiOperation(value = "排行分析", notes = "按指定指标对数据进行排序分析")
    public List<RankingItem> analyzeRanking(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @RequestParam String businessType,
            
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @RequestParam String dimensionLevel,
            
            @ApiParam(value = "排序指标", required = true, example = "quizAvgScore") 
            @RequestParam String rankBy,
            
            @ApiParam(value = "开始日期", required = true, example = "2024-01-01") 
            @RequestParam String startDate,
            
            @ApiParam(value = "结束日期", required = true, example = "2024-12-31") 
            @RequestParam String endDate,
            
            @ApiParam(value = "返回数量限制", example = "10") 
            @RequestParam(required = false, defaultValue = "10") Integer limit,
            
            @ApiParam(value = "省份ID", example = "110000") 
            @RequestParam(required = false) String provinceId,
            
            @ApiParam(value = "城市ID", example = "110100") 
            @RequestParam(required = false) String cityId) {
        
        log.info("排行分析请求: businessType={}, dimensionLevel={}, rankBy={}, limit={}", 
                businessType, dimensionLevel, rankBy, limit);
        
        // 构建过滤条件
        Map<String, Object> filters = buildFilters(provinceId, cityId, null);
        
        // 执行分析
        return universalAnalysisService.analyzeRanking(
                BusinessTypeEnum.valueOf(businessType),
                DimensionLevelEnum.valueOf(dimensionLevel),
                rankBy,
                startDate,
                endDate,
                limit,
                filters
        );
    }
    
    /**
     * 对比分析
     */
    @GetMapping("/comparison")
    @ApiOperation(value = "对比分析", notes = "对比两个时间段的数据差异")
    public ComparisonResult analyzeComparison(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @RequestParam String businessType,
            
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @RequestParam String dimensionLevel,
            
            @ApiParam(value = "指标名称", required = true, example = "quizAvgScore") 
            @RequestParam String metricName,
            
            @ApiParam(value = "当前期开始日期", required = true, example = "2024-12-01") 
            @RequestParam String currentStartDate,
            
            @ApiParam(value = "当前期结束日期", required = true, example = "2024-12-31") 
            @RequestParam String currentEndDate,
            
            @ApiParam(value = "对比期开始日期", required = true, example = "2024-11-01") 
            @RequestParam String previousStartDate,
            
            @ApiParam(value = "对比期结束日期", required = true, example = "2024-11-30") 
            @RequestParam String previousEndDate,
            
            @ApiParam(value = "省份ID", example = "110000") 
            @RequestParam(required = false) String provinceId,
            
            @ApiParam(value = "城市ID", example = "110100") 
            @RequestParam(required = false) String cityId) {
        
        log.info("对比分析请求: businessType={}, dimensionLevel={}, metricName={}", 
                businessType, dimensionLevel, metricName);
        
        // 构建过滤条件
        Map<String, Object> filters = buildFilters(provinceId, cityId, null);
        
        // 执行分析
        return universalAnalysisService.analyzeComparison(
                BusinessTypeEnum.valueOf(businessType),
                DimensionLevelEnum.valueOf(dimensionLevel),
                metricName,
                currentStartDate,
                currentEndDate,
                previousStartDate,
                previousEndDate,
                filters
        );
    }
    
    /**
     * 汇总统计
     */
    @GetMapping("/summary")
    @ApiOperation(value = "汇总统计", notes = "对指定指标进行汇总统计")
    public Object analyzeSummary(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @RequestParam String businessType,
            
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @RequestParam String dimensionLevel,
            
            @ApiParam(value = "指标名称", required = true, example = "quizAvgScore") 
            @RequestParam String metricName,
            
            @ApiParam(value = "汇总类型", required = true, example = "AVG") 
            @RequestParam String aggregationType,
            
            @ApiParam(value = "开始日期", required = true, example = "2024-01-01") 
            @RequestParam String startDate,
            
            @ApiParam(value = "结束日期", required = true, example = "2024-12-31") 
            @RequestParam String endDate,
            
            @ApiParam(value = "省份ID", example = "110000") 
            @RequestParam(required = false) String provinceId,
            
            @ApiParam(value = "城市ID", example = "110100") 
            @RequestParam(required = false) String cityId) {
        
        log.info("汇总统计请求: businessType={}, dimensionLevel={}, metricName={}, aggregationType={}", 
                businessType, dimensionLevel, metricName, aggregationType);
        
        // 构建过滤条件
        Map<String, Object> filters = buildFilters(provinceId, cityId, null);
        
        // 执行分析
        return universalAnalysisService.analyzeSummary(
                BusinessTypeEnum.valueOf(businessType),
                DimensionLevelEnum.valueOf(dimensionLevel),
                metricName,
                aggregationType,
                startDate,
                endDate,
                filters
        );
    }
    
    /**
     * 数据列表查询
     */
    @GetMapping("/data")
    @ApiOperation(value = "数据列表查询", notes = "查询原始分析数据")
    public IPage<AnalysisDataEntity> queryData(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @RequestParam String businessType,
            
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @RequestParam String dimensionLevel,
            
            @ApiParam(value = "开始日期", example = "2024-01-01") 
            @RequestParam(required = false) String startDate,
            
            @ApiParam(value = "结束日期", example = "2024-12-31") 
            @RequestParam(required = false) String endDate,
            
            @ApiParam(value = "页码", example = "1") 
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            
            @ApiParam(value = "页大小", example = "20") 
            @RequestParam(required = false, defaultValue = "20") Integer pageSize,
            
            @ApiParam(value = "省份ID", example = "110000") 
            @RequestParam(required = false) String provinceId,
            
            @ApiParam(value = "城市ID", example = "110100") 
            @RequestParam(required = false) String cityId) {
        
        log.info("数据列表查询请求: businessType={}, dimensionLevel={}, pageNum={}, pageSize={}", 
                businessType, dimensionLevel, pageNum, pageSize);
        
        // 构建过滤条件
        Map<String, Object> filters = buildFilters(provinceId, cityId, null);
        
        // 执行查询
        return analysisDataService.queryAnalysisDataPage(
                BusinessTypeEnum.valueOf(businessType),
                DimensionLevelEnum.valueOf(dimensionLevel),
                startDate,
                endDate,
                filters,
                pageNum,
                pageSize
        );
    }
    
    /**
     * 获取支持的指标列表
     */
    @GetMapping("/metrics")
    @ApiOperation(value = "获取支持的指标列表", notes = "获取指定业务类型支持的所有指标")
    public List<String> getSupportedMetrics(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @RequestParam String businessType) {
        
        log.info("获取支持的指标列表: businessType={}", businessType);
        
        return universalAnalysisService.getSupportedMetrics(BusinessTypeEnum.valueOf(businessType));
    }
    
    /**
     * 获取维度值列表
     */
    @GetMapping("/dimensions")
    @ApiOperation(value = "获取维度值列表", notes = "获取指定维度的所有可选值")
    public List<String> getDimensionValues(
            @ApiParam(value = "业务类型", required = true, example = "STUDY") 
            @RequestParam String businessType,
            
            @ApiParam(value = "维度级别", required = true, example = "PROVINCE") 
            @RequestParam String dimensionLevel,
            
            @ApiParam(value = "维度字段", required = true, example = "province") 
            @RequestParam String dimensionField) {
        
        log.info("获取维度值列表: businessType={}, dimensionLevel={}, dimensionField={}", 
                businessType, dimensionLevel, dimensionField);
        
        return analysisDataService.queryDimensionValues(
                BusinessTypeEnum.valueOf(businessType),
                DimensionLevelEnum.valueOf(dimensionLevel),
                dimensionField,
                new HashMap<>()
        );
    }
    
    /**
     * 构建过滤条件
     */
    private Map<String, Object> buildFilters(String provinceId, String cityId, String aliasId) {
        Map<String, Object> filters = new HashMap<>();
        
        if (provinceId != null && !provinceId.trim().isEmpty()) {
            filters.put("provinceId", provinceId);
        }
        
        if (cityId != null && !cityId.trim().isEmpty()) {
            filters.put("cityId", cityId);
        }
        
        if (aliasId != null && !aliasId.trim().isEmpty()) {
            filters.put("aliasId", aliasId);
        }
        
        return filters;
    }
}
