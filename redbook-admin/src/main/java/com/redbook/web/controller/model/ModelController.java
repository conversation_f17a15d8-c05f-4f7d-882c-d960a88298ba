package com.redbook.web.controller.model;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.Model;
import com.redbook.system.service.IModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 产品型号Controller
 *
 * <AUTHOR>
 * @date 2022-11-14
 */
@RestController
@RequestMapping("/system/model")
@Api(tags = "产品型号管理")
public class ModelController extends BaseController {
    @Autowired
    private IModelService modelService;

    /**
     * 查询产品型号列表
     */
    @GetMapping("/list")
    @ApiOperation("查询产品型号列表")
    public TableDataInfo<Model> list(Model model) {
        startPage();
        List<Model> list = modelService.selectModelList(model);
        return getDataTable(list);
    }

    /**
     * 导出产品型号列表
     */
    @ApiOperation("导出产品型号列表")
    @Log(title = "产品型号", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Model model) {
        List<Model> list = modelService.selectModelList(model);
        ExcelUtil<Model> util = new ExcelUtil<Model>(Model.class);
        util.exportExcel(response, list, "产品型号数据");
    }

    /**
     * 获取产品型号详细信息
     */
    @ApiOperation("获取产品型号详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(modelService.selectModelById(id));
    }

    /**
     * 新增产品型号
     */
    @ApiOperation("新增产品型号")
    @Log(title = "产品型号", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Model model) {
        model.setCreateBy(getUsername());
        return toAjax(modelService.insertModel(model));
    }

    /**
     * 修改产品型号
     */
    @ApiOperation("修改产品型号")
    @Log(title = "产品型号", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Model model) {
        model.setUpdateBy(getUsername());
        return toAjax(modelService.updateModel(model));
    }

    /**
     * 删除产品型号
     */
    @ApiOperation("删除产品型号")
    @Log(title = "产品型号", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(modelService.deleteModelByIds(ids, getUsername()));
    }
}
