package com.redbook.web.controller.sf;

import com.redbook.util.SfUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("sf")
@Api(tags = "顺丰查询")
public class ShunFengController {

    @Autowired
    SfUtil sfUtil;

    @GetMapping("/query")
    @ApiOperation("快递查询")
    public String query(String orderId,String phone) {
        return sfUtil.queryOrder(orderId,phone);
    }
}
