package com.redbook.web.controller.kids.app;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.kids.AppVersion;
import com.redbook.system.service.kids.IAppVersionService;
import com.redbook.common.utils.poi.ExcelUtil;

/**
 * 应用版本，存储所有应用版本信息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
@RestController
@RequestMapping("/kids/version")
@Api(tags = "应用版本，存储所有应用版本信息")
public class AppVersionController extends BaseController
{
    @Autowired
    private IAppVersionService appVersionService;

    /**
     * 查询应用版本，存储所有应用版本信息列表
     */
    @GetMapping("/list")
    @ApiOperation("查询应用版本，存储所有应用版本信息列表")
    public TableDataInfo<AppVersion> list(AppVersion appVersion)
    {
        startPage();
        List<AppVersion> list = appVersionService.selectAppVersionList(appVersion);
        return getDataTable(list);
    }

    /**
     * 导出应用版本，存储所有应用版本信息列表
     */
    @ApiOperation("导出应用版本，存储所有应用版本信息列表")
    @Log(title = "应用版本，存储所有应用版本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppVersion appVersion)
    {
        List<AppVersion> list = appVersionService.selectAppVersionList(appVersion);
        ExcelUtil<AppVersion> util = new ExcelUtil<AppVersion>(AppVersion.class);
        util.exportExcel(response, list, "应用版本，存储所有应用版本信息数据");
    }

    /**
     * 获取应用版本，存储所有应用版本信息详细信息
     */
    @ApiOperation("获取应用版本，存储所有应用版本信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(appVersionService.selectAppVersionById(id));
    }

    /**
     * 新增应用版本，存储所有应用版本信息
     */
    @ApiOperation("新增应用版本，存储所有应用版本信息")
    @Log(title = "应用版本，存储所有应用版本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppVersion appVersion)
    {
        return toAjax(appVersionService.insertAppVersion(appVersion));
    }

    /**
     * 修改应用版本，存储所有应用版本信息
     */
    @ApiOperation("修改应用版本，存储所有应用版本信息")
    @Log(title = "应用版本，存储所有应用版本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppVersion appVersion)
    {
        return toAjax(appVersionService.updateAppVersion(appVersion));
    }

    /**
     * 删除应用版本，存储所有应用版本信息
     */
    @ApiOperation("删除应用版本，存储所有应用版本信息")
    @Log(title = "应用版本，存储所有应用版本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appVersionService.deleteAppVersionByIds(ids));
    }
}
