package com.redbook.web.controller.market;

import java.util.List;
import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.entity.SysUser;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.market.ConsultPictureFolder;
import com.redbook.system.domain.market.ConsultPictureFolderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.service.IConsultPictureFolderService;

/**
 * 咨询夹图片Controller
 * 
 * <AUTHOR>
 * @date 2025-02-22
 */
@RestController
@RequestMapping("/system/consulFolder")
@Api(tags = "咨询夹图片")
public class ConsultPictureFolderController extends BaseController
{
    @Autowired
    private IConsultPictureFolderService consultPictureFolderService;

    /**
     * 查询咨询夹图片列表
     */
    @GetMapping("/list")
    @ApiOperation("查询咨询夹图片列表")
    public TableDataInfo<ConsultPictureFolder> list(ConsultPictureFolder consultPictureFolder)
    {
//        startPage();
        List<ConsultPictureFolder> list = consultPictureFolderService.selectConsultPictureFolderList(consultPictureFolder);
        return getDataTable(list);
    }

    @GetMapping("/systemPictureList")
    @ApiOperation("查询咨询夹图片列表")
    public AjaxResult<List<ConsultPictureFolder>> systemPictureList()
    {
        List<ConsultPictureFolder> list = consultPictureFolderService.systemPictureList();
        return AjaxResult.success(list);
    }

    @ApiOperation("批量更新图片排序")
    @Log(title = "批量更新图片排序", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateSort")
    public AjaxResult batchUpdateSort(@RequestBody ConsultPictureFolderDTO consultPictureFolderDTO)
    {
        return toAjax(consultPictureFolderService.batchUpdateSort(consultPictureFolderDTO));
    }

//    /**
//     * 导出咨询夹图片列表
//     */
//    @ApiOperation("导出咨询夹图片列表")
//    @Log(title = "咨询夹图片", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ConsultPictureFolder consultPictureFolder)
//    {
//        List<ConsultPictureFolder> list = consultPictureFolderService.selectConsultPictureFolderList(consultPictureFolder);
//        ExcelUtil<ConsultPictureFolder> util = new ExcelUtil<ConsultPictureFolder>(ConsultPictureFolder.class);
//        util.exportExcel(response, list, "咨询夹图片数据");
//    }

    /**
     * 获取咨询夹图片详细信息
     */
    @ApiOperation("获取咨询夹图片详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(consultPictureFolderService.selectConsultPictureFolderById(id));
    }

    /**
     * 新增咨询夹图片
     */
    @ApiOperation("新增咨询夹图片")
    @Log(title = "咨询夹图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ConsultPictureFolder consultPictureFolder)
    {
        if (SysUser.isAdmin(SecurityUtils.getUserId())) {
            return AjaxResult.error("超级管理员不可操作");
        }
        return toAjax(consultPictureFolderService.insertConsultPictureFolder(consultPictureFolder));
    }

    /**
     * 修改咨询夹图片
     */
    @ApiOperation("修改咨询夹图片")
    @Log(title = "咨询夹图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConsultPictureFolder consultPictureFolder)
    {
        return toAjax(consultPictureFolderService.updateConsultPictureFolder(consultPictureFolder));
    }

    /**
     * 删除咨询夹图片
     */
    @ApiOperation("删除咨询夹图片")
    @Log(title = "咨询夹图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Integer id)
    {
        return toAjax(consultPictureFolderService.deleteConsultPictureFolderById(id));
    }
}
