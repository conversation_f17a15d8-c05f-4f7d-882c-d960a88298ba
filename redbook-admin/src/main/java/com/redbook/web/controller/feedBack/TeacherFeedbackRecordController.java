package com.redbook.web.controller.feedBack;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.model.LoginUser;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.TeacherFeedbackRecord;
import com.redbook.system.service.ITeacherFeedbackRecordService;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.core.page.TableDataInfo;

/**
 * 教师反馈信息记录Controller
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@RestController
@RequestMapping("/teacherFeedBack/record")
@Api(tags = "教师反馈信息记录")
public class TeacherFeedbackRecordController extends BaseController
{
    @Autowired
    private ITeacherFeedbackRecordService teacherFeedbackRecordService;

    /**
     * 查询教师反馈信息记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询教师反馈信息记录列表")
    public TableDataInfo<TeacherFeedbackRecord> list(TeacherFeedbackRecord teacherFeedbackRecord)
    {
        startPage();
        List<TeacherFeedbackRecord> list = teacherFeedbackRecordService.selectTeacherFeedbackRecordList(teacherFeedbackRecord);
        return getDataTable(list);
    }

    /**
     * 导出教师反馈信息记录列表
     */
    @ApiOperation("导出教师反馈信息记录列表")
    @Log(title = "教师反馈信息记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TeacherFeedbackRecord teacherFeedbackRecord)
    {
        List<TeacherFeedbackRecord> list = teacherFeedbackRecordService.selectTeacherFeedbackRecordList(teacherFeedbackRecord);
        ExcelUtil<TeacherFeedbackRecord> util = new ExcelUtil<TeacherFeedbackRecord>(TeacherFeedbackRecord.class);
        util.exportExcel(response, list, "教师反馈信息记录数据");
    }

    /**
     * 获取教师反馈信息记录详细信息
     */
    @ApiOperation("获取教师反馈信息记录详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(teacherFeedbackRecordService.selectTeacherFeedbackRecordById(id));
    }

    /**
     * 新增教师反馈信息记录
     */
    @ApiOperation("新增教师反馈信息记录")
    @Log(title = "教师反馈信息记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TeacherFeedbackRecord teacherFeedbackRecord)
    {
        return toAjax(teacherFeedbackRecordService.insertTeacherFeedbackRecord(teacherFeedbackRecord));
    }

    /**
     * 修改教师反馈信息记录
     */
    @ApiOperation("修改教师反馈信息记录")
    @Log(title = "教师反馈信息记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TeacherFeedbackRecord teacherFeedbackRecord)
    {
        return toAjax(teacherFeedbackRecordService.updateTeacherFeedbackRecord(teacherFeedbackRecord));
    }

    /**
     * 删除教师反馈信息记录
     */
    @ApiOperation("删除教师反馈信息记录")
    @Log(title = "教师反馈信息记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(teacherFeedbackRecordService.deleteTeacherFeedbackRecordByIds(ids));
    }
}
