package com.redbook;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
@EnableCaching
@ComponentScan(basePackages = {
        "com.redbook",
        "com.redbook.system",
        "com.redbook.common",
        "com.redbook.framework",
        "com.redbook.generator",
        "com.redbook.quartz",
        "com.redbook.postsaleapi",
        "com.redbook.dashboard"
})
public class RedBookApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(RedBookApplication.class, args);
    }
}
